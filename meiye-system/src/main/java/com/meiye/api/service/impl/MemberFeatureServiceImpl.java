package com.meiye.api.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.Date;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.service.MemberFeatureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.MemberFeatureMapper;
import com.meiye.api.domain.MemberFeature;
import org.springframework.transaction.annotation.Transactional;
import com.meiye.api.service.MemberFeatureLogService;
import com.meiye.api.mapper.MemberFeatureTypeMapper;
import com.meiye.api.domain.MemberFeatureLog;
import com.meiye.api.domain.MemberFeatureType;

/**
 * 会员特性Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class MemberFeatureServiceImpl
        extends ServiceImpl<MemberFeatureMapper, MemberFeature>
        implements MemberFeatureService
{
    @Autowired
    private MemberFeatureMapper memberFeatureMapper;

    @Autowired
    private MemberFeatureLogService memberFeatureLogService;

    @Autowired
    private MemberFeatureTypeMapper memberFeatureTypeMapper;

    /**
     * 查询会员特性
     * 
     * @param id 会员特性主键
     * @return 会员特性
     */
    @Override
    public MemberFeature selectMemberFeatureById(Long id)
    {
        return memberFeatureMapper.selectMemberFeatureById(id);
    }

    /**
     * 查询会员特性列表
     * 
     * @param memberFeature 会员特性
     * @return 会员特性
     */
    @Override
    public List<MemberFeature> selectMemberFeatureList(MemberFeature memberFeature)
    {
        return memberFeatureMapper.selectMemberFeatureList(memberFeature);
    }

    /**
     * 新增会员特性
     * 
     * @param memberFeature 会员特性
     * @return 结果
     */
    @Override
    public int insertMemberFeature(MemberFeature memberFeature)
    {
        return memberFeatureMapper.insertMemberFeature(memberFeature);
    }

    /**
     * 修改会员特性
     * 
     * @param memberFeature 会员特性
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMemberFeature(MemberFeature memberFeature)
    {
        // 获取原始数据
        MemberFeature oldFeature = memberFeatureMapper.selectMemberFeatureById(memberFeature.getId());
        if (oldFeature == null) {
            throw new RuntimeException("会员特性不存在");
        }
        
        // 检查是否有变更
        boolean hasChange = false;
        MemberFeatureLog log = new MemberFeatureLog();
        log.setFeatureTypeId(memberFeature.getMemberFeatureId());
        log.setCreatedByEmployeeId(memberFeature.getInputBy());
        log.setCreatedAt(new Date());
        
        // 获取特性类型名称
        MemberFeatureType featureType = memberFeatureTypeMapper.selectById(memberFeature.getMemberFeatureId());
        String featureTypeName = featureType != null ? featureType.getName() : "";
        
        // 检查特性类型是否变更
        if (!Objects.equals(oldFeature.getMemberFeatureId(), memberFeature.getMemberFeatureId())) {
            MemberFeatureType oldFeatureType = memberFeatureTypeMapper.selectById(oldFeature.getMemberFeatureId());
            log.setOriginFeatureName(oldFeatureType != null ? oldFeatureType.getName() : "");
            log.setCurrentFeatureName(featureTypeName);
            hasChange = true;
        } else {
            log.setOriginFeatureName(featureTypeName);
            log.setCurrentFeatureName(featureTypeName);
        }
        
        // 检查特性值是否变更
        if (!Objects.equals(oldFeature.getValue(), memberFeature.getValue())) {
            log.setOriginValue(oldFeature.getValue());
            log.setCurrentValue(memberFeature.getValue());
            hasChange = true;
        } else {
            log.setOriginValue(memberFeature.getValue());
            log.setCurrentValue(memberFeature.getValue());
        }
        
        // 更新会员特性
        int result = memberFeatureMapper.updateMemberFeature(memberFeature);
        
        // 如果有变更，记录日志
        if (hasChange && result > 0) {
            memberFeatureLogService.insertMemberFeatureLog(log);
        }
        
        return result;
    }

    /**
     * 批量删除会员特性
     * 
     * @param ids 需要删除的会员特性主键
     * @return 结果
     */
    @Override
    public int deleteMemberFeatureByIds(Long[] ids)
    {
        return memberFeatureMapper.deleteMemberFeatureByIds(ids);
    }

    /**
     * 删除会员特性信息
     * 
     * @param id 会员特性主键
     * @return 结果
     */
    @Override
    public int deleteMemberFeatureById(Long id)
    {
        return memberFeatureMapper.deleteMemberFeatureById(id);
    }
}
