package com.meiye.api.service;

import com.meiye.api.domain.ServiceRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ServiceRecordService
        extends IService<ServiceRecord>{

    /**
     * 查询服务记录
     *
     * @param id 服务记录主键
     * @return 服务记录
     */
    public ServiceRecord selectServiceRecordById(Long id);

    /**
     * 查询服务记录列表
     *
     * @param serviceRecord 服务记录
     * @return 服务记录集合
     */
    public List<ServiceRecord> selectServiceRecordList(ServiceRecord serviceRecord);

    /**
     * 新增服务记录
     *
     * @param serviceRecord 服务记录
     * @return 结果
     */
    public int insertServiceRecord(ServiceRecord serviceRecord);

    /**
     * 修改服务记录
     *
     * @param serviceRecord 服务记录
     * @return 结果
     */
    public int updateServiceRecord(ServiceRecord serviceRecord);

    /**
     * 批量删除服务记录
     *
     * @param ids 需要删除的服务记录主键集合
     * @return 结果
     */
    public int deleteServiceRecordByIds(Long[] ids);

    /**
     * 删除服务记录信息
     *
     * @param id 服务记录主键
     * @return 结果
     */
    public int deleteServiceRecordById(Long id);

}
