package com.meiye.api.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.domain.ServiceScriptDetails;

/**
 * 标准话术详情Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ServiceScriptDetailsService extends IService<ServiceScriptDetails>
{
    /**
     * 查询标准话术详情
     * 
     * @param serviceScriptId 标准话术详情主键
     * @return 标准话术详情
     */
    public ServiceScriptDetails selectServiceScriptDetailsByServiceScriptId(Long serviceScriptId);

    /**
     * 查询标准话术详情列表
     * 
     * @param serviceScriptDetails 标准话术详情
     * @return 标准话术详情集合
     */
    public List<ServiceScriptDetails> selectServiceScriptDetailsList(ServiceScriptDetails serviceScriptDetails);

    /**
     * 新增标准话术详情
     * 
     * @param serviceScriptDetails 标准话术详情
     * @return 结果
     */
    public int insertServiceScriptDetails(ServiceScriptDetails serviceScriptDetails);

    /**
     * 修改标准话术详情
     * 
     * @param serviceScriptDetails 标准话术详情
     * @return 结果
     */
    public int updateServiceScriptDetails(ServiceScriptDetails serviceScriptDetails);

    /**
     * 批量删除标准话术详情
     * 
     * @param serviceScriptIds 需要删除的标准话术详情主键集合
     * @return 结果
     */
    public int deleteServiceScriptDetailsByServiceScriptIds(Long[] serviceScriptIds);

    /**
     * 删除标准话术详情信息
     * 
     * @param serviceScriptId 标准话术详情主键
     * @return 结果
     */
    public int deleteServiceScriptDetailsByServiceScriptId(Long serviceScriptId);
}
