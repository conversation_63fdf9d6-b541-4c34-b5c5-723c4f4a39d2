package com.meiye.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.vo.RecordingDeviceVo;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

import com.meiye.api.domain.RecordingDeviceEmployee;
import com.meiye.api.domain.RecordingDeviceEmployeeLog;
import com.meiye.api.mapper.RecordingDeviceEmployeeLogMapper;
import com.meiye.api.mapper.RecordingDeviceEmployeeMapper;

import com.meiye.api.mapper.RecordingDeviceMapper;
import com.meiye.api.domain.RecordingDevice;
import com.meiye.api.service.RecordingDeviceService;
import org.springframework.transaction.annotation.Transactional;

import static com.meiye.common.utils.SecurityUtils.getUserId;

@Service
public class RecordingDeviceServiceImpl extends ServiceImpl<RecordingDeviceMapper, RecordingDevice> implements RecordingDeviceService{

    @Autowired
    private RecordingDeviceMapper recordingDeviceMapper;

    @Autowired
    private RecordingDeviceEmployeeMapper recordingDeviceEmployeeMapper;
    @Autowired
    RecordingDeviceEmployeeLogMapper recordingDeviceEmployeeLogMapper;
    @Override
    public List<RecordingDeviceVo> getRecordDeviceVoList() {
        return recordingDeviceMapper.selectDeviceWithAssignInfo();
    }
    /**
     * 查询录音设备
     *
     * @param id 录音设备主键
     * @return 录音设备
     */
    @Override
    public RecordingDevice selectRecordingDeviceById(Long id)
    {
        return recordingDeviceMapper.selectRecordingDeviceById(id);
    }

    /**
     * 查询录音设备列表
     *
     * @param recordingDevice 录音设备
     * @return 录音设备
     */
    @Override
    public List<RecordingDevice> selectRecordingDeviceList(RecordingDevice recordingDevice)
    {
        return recordingDeviceMapper.selectRecordingDeviceList(recordingDevice);
    }

    /**
     * 新增录音设备
     *
     * @param recordingDevice 录音设备
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRecordingDevice(RecordingDevice recordingDevice) {
        // 1. 先设置设备的固定属性，插入设备表（获取自增ID）
        recordingDevice.setStatus("0");
        recordingDevice.setBound_status("1");
        int deviceResult = recordingDeviceMapper.insertRecordingDevice(recordingDevice);

        // 2. 此时设备的自增ID已生成（需Mapper配置支持），再给关联表对象赋值
        RecordingDeviceEmployee employee = new RecordingDeviceEmployee();
        // 获取插入后生成的设备ID（关键）
        employee.setDeviceId(recordingDevice.getId());
        // 确保storeId不为空（若为空需检查上游传递逻辑）
        employee.setStoreId(recordingDevice.getStoreId());

        // 打印校验属性值（确认是否有值）
        System.out.println("插入后设备ID: " + recordingDevice.getId());
        System.out.println("关联表storeId: " + employee.getStoreId());

        // 3. 插入关联表
        if (employee.getDeviceId() != null && employee.getStoreId() != null) {
            recordingDeviceEmployeeMapper.insertRecordingDeviceEmployee(employee);
        } else {
            // 处理异常情况（如日志报警）
            System.err.println("设备ID或店铺ID为空，无法插入关联表");
        }

        return deviceResult;
    }

    /**
     * 修改录音设备
     *
     * @param recordingDevice 录音设备
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRecordingDevice(RecordingDevice recordingDevice) {
        // 1. 获取原始设备信息
        RecordingDevice originalDevice = recordingDeviceMapper.selectRecordingDeviceById(recordingDevice.getId());
        if (originalDevice == null) {
            throw new RuntimeException("设备不存在");
        }

        // 2. 检查绑定状态是否变化
        String newBoundStatus = recordingDevice.getBound_status();
        String originalBoundStatus = originalDevice.getBound_status();
        boolean isBoundStatusChanged = (newBoundStatus != null && !newBoundStatus.equals(originalBoundStatus));

        Long employeeId = null;
        Long deviceId = recordingDevice.getId(); // 当前设备ID

        // 3. 处理设备员工关联表（核心修改）
        if (isBoundStatusChanged) {
            // 查询该设备的关联记录（通过device_id）
            RecordingDeviceEmployee deviceEmployee = recordingDeviceEmployeeMapper.selectByDeviceId(deviceId);

            // 3.1 解绑操作：将employee_id设为null（保留记录）
            if ("1".equals(newBoundStatus)) {
                if (deviceEmployee != null) {
                    employeeId = deviceEmployee.getEmployeeId(); // 记录原员工ID用于日志
                    deviceEmployee.setEmployeeId(null); // 设为null而非删除
                    int updateResult = recordingDeviceEmployeeMapper.updateRecordingDeviceEmployee(deviceEmployee);
                    if (updateResult == 0) {
                        throw new RuntimeException("解绑失败：更新员工关联记录失败");
                    }
                }
            }
            // 3.2 绑定操作：更新或新增关联记录（确保唯一）
            else if ("0".equals(newBoundStatus)) {
                employeeId = recordingDevice.getTempEmployeeId();
                if (employeeId == null) {
                    throw new RuntimeException("绑定失败：缺少员工ID");
                }

                // 检查员工是否已绑定其他设备
                RecordingDeviceEmployee existingBinding = recordingDeviceEmployeeMapper.selectByEmployeeId(employeeId);
                if (existingBinding != null) {
                    throw new RuntimeException("绑定失败：该员工已绑定其他设备");
                }

                // 3.2.1 已有记录：直接更新employee_id
                if (deviceEmployee != null) {
                    deviceEmployee.setEmployeeId(employeeId);
                    deviceEmployee.setStoreId(recordingDevice.getStoreId() != null ?
                            recordingDevice.getStoreId() : originalDevice.getStoreId());
                    int updateResult = recordingDeviceEmployeeMapper.updateRecordingDeviceEmployee(deviceEmployee);
                    if (updateResult == 0) {
                        throw new RuntimeException("绑定失败：更新员工关联记录失败");
                    }
                }
                // 3.2.2 无记录：新增一条关联（确保后续解绑可更新）
                else {
                    RecordingDeviceEmployee newDeviceEmployee = new RecordingDeviceEmployee();
                    newDeviceEmployee.setDeviceId(deviceId);
                    newDeviceEmployee.setEmployeeId(employeeId);
                    newDeviceEmployee.setStoreId(recordingDevice.getStoreId() != null ?
                            recordingDevice.getStoreId() : originalDevice.getStoreId());
                    int insertResult = recordingDeviceEmployeeMapper.insertRecordingDeviceEmployee(newDeviceEmployee);
                    if (insertResult == 0) {
                        throw new RuntimeException("绑定失败：创建员工关联记录失败");
                    }
                }
            }
        }

        // 4. 记录日志
        if (isBoundStatusChanged) {
            RecordingDeviceEmployeeLog log = new RecordingDeviceEmployeeLog();
            log.setDeviceId(recordingDevice.getId());
            log.setAction("0".equals(newBoundStatus) ? "bind" : "unbind");
            log.setEmployeeId(employeeId);
            log.setCreatedAt(new Date());
            log.setCreatedBy(String.valueOf(getUserId()));

            // 设置店铺ID
            Long storeId = recordingDevice.getStoreId() != null ?
                    recordingDevice.getStoreId() : originalDevice.getStoreId();
            log.setStoreId(storeId);

            int logResult = recordingDeviceEmployeeLogMapper.insertRecordingDeviceEmployeeLog(log);
            if (logResult == 0) {
                throw new RuntimeException("日志记录失败");
            }
        }

        // 5. 更新设备表
        int updateResult = recordingDeviceMapper.updateRecordingDevice(recordingDevice);
        if (updateResult == 0) {
            throw new RuntimeException("设备更新失败");
        }

        return updateResult;
    }

    // 获取设备最近一次绑定的员工ID
    private Long getLastBoundEmployeeId(Long deviceId) {
        // 查询设备最近一次绑定操作的日志记录
        RecordingDeviceEmployeeLog lastBindLog = recordingDeviceEmployeeLogMapper.selectLastBindLog(deviceId);
        return lastBindLog != null ? lastBindLog.getEmployeeId() : null;
    }

    /**
     * 批量删除录音设备
     *
     * @param ids 需要删除的录音设备主键
     * @return 结果
     */
    @Override
    public int deleteRecordingDeviceByIds(Long[] ids)
    {
        recordingDeviceEmployeeMapper.deleteRecordingDeviceEmployeeByDeviceIds(ids);
        return recordingDeviceMapper.deleteRecordingDeviceByIds(ids);
    }

    /**
     * 删除录音设备信息
     *
     * @param id 录音设备主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRecordingDeviceById(Long id)
    {
        recordingDeviceEmployeeMapper.deleteRecordingDeviceEmployeeByDeviceId(id);
        return recordingDeviceMapper.deleteRecordingDeviceById(id);
    }
}
