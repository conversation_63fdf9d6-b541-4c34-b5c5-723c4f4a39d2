package com.meiye.api.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.RecordingDeviceEmployeeLogMapper;
import com.meiye.api.domain.RecordingDeviceEmployeeLog;
import com.meiye.api.service.RecordingDeviceEmployeeLogService;

/**
 * 录音设备分配记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class RecordingDeviceEmployeeLogServiceImpl implements RecordingDeviceEmployeeLogService
{
    @Autowired
    private RecordingDeviceEmployeeLogMapper recordingDeviceEmployeeLogMapper;

    /**
     * 查询录音设备分配记录
     *
     * @param id 录音设备分配记录主键
     * @return 录音设备分配记录
     */
    @Override
    public RecordingDeviceEmployeeLog selectRecordingDeviceEmployeeLogById(Long id)
    {
        return recordingDeviceEmployeeLogMapper.selectRecordingDeviceEmployeeLogById(id);
    }

    /**
     * 查询录音设备分配记录列表
     *
     * @param recordingDeviceEmployeeLog 录音设备分配记录
     * @return 录音设备分配记录
     */
    @Override
    public List<RecordingDeviceEmployeeLog> selectRecordingDeviceEmployeeLogList(RecordingDeviceEmployeeLog recordingDeviceEmployeeLog)
    {
        return recordingDeviceEmployeeLogMapper.selectRecordingDeviceEmployeeLogList(recordingDeviceEmployeeLog);
    }

    /**
     * 新增录音设备分配记录
     *
     * @param recordingDeviceEmployeeLog 录音设备分配记录
     * @return 结果
     */
    @Override
    public int insertRecordingDeviceEmployeeLog(RecordingDeviceEmployeeLog recordingDeviceEmployeeLog)
    {
        return recordingDeviceEmployeeLogMapper.insertRecordingDeviceEmployeeLog(recordingDeviceEmployeeLog);
    }

    /**
     * 修改录音设备分配记录
     *
     * @param recordingDeviceEmployeeLog 录音设备分配记录
     * @return 结果
     */
    @Override
    public int updateRecordingDeviceEmployeeLog(RecordingDeviceEmployeeLog recordingDeviceEmployeeLog)
    {
        return recordingDeviceEmployeeLogMapper.updateRecordingDeviceEmployeeLog(recordingDeviceEmployeeLog);
    }

    /**
     * 批量删除录音设备分配记录
     *
     * @param ids 需要删除的录音设备分配记录主键
     * @return 结果
     */
    @Override
    public int deleteRecordingDeviceEmployeeLogByIds(Long[] ids)
    {
        return recordingDeviceEmployeeLogMapper.deleteRecordingDeviceEmployeeLogByIds(ids);
    }

    /**
     * 删除录音设备分配记录信息
     *
     * @param id 录音设备分配记录主键
     * @return 结果
     */
    @Override
    public int deleteRecordingDeviceEmployeeLogById(Long id)
    {
        return recordingDeviceEmployeeLogMapper.deleteRecordingDeviceEmployeeLogById(id);
    }
}
