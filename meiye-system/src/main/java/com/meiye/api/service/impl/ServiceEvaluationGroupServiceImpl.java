package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.ServiceEvaluationGroup;
import com.meiye.api.mapper.ServiceEvaluationGroupMapper;
import com.meiye.api.service.ServiceEvaluationGroupService;
@Service
public class ServiceEvaluationGroupServiceImpl extends ServiceImpl<ServiceEvaluationGroupMapper, ServiceEvaluationGroup> implements ServiceEvaluationGroupService{
    @Autowired
    private ServiceEvaluationGroupMapper serviceEvaluationGroupMapper;

    /**
     * 查询服务评价维度分组
     *
     * @param id 服务评价维度分组主键
     * @return 服务评价维度分组
     */
    @Override
    public ServiceEvaluationGroup selectServiceEvaluationGroupById(Long id)
    {
        return serviceEvaluationGroupMapper.selectServiceEvaluationGroupById(id);
    }

    /**
     * 查询服务评价维度分组列表
     *
     * @param serviceEvaluationGroup 服务评价维度分组
     * @return 服务评价维度分组
     */
    @Override
    public List<ServiceEvaluationGroup> selectServiceEvaluationGroupList(ServiceEvaluationGroup serviceEvaluationGroup)
    {
        return serviceEvaluationGroupMapper.selectServiceEvaluationGroupList(serviceEvaluationGroup);
    }

    /**
     * 新增服务评价维度分组
     *
     * @param serviceEvaluationGroup 服务评价维度分组
     * @return 结果
     */
    @Override
    public int insertServiceEvaluationGroup(ServiceEvaluationGroup serviceEvaluationGroup)
    {
        return serviceEvaluationGroupMapper.insertServiceEvaluationGroup(serviceEvaluationGroup);
    }

    /**
     * 修改服务评价维度分组
     *
     * @param serviceEvaluationGroup 服务评价维度分组
     * @return 结果
     */
    @Override
    public int updateServiceEvaluationGroup(ServiceEvaluationGroup serviceEvaluationGroup)
    {
        return serviceEvaluationGroupMapper.updateServiceEvaluationGroup(serviceEvaluationGroup);
    }

    /**
     * 批量删除服务评价维度分组
     *
     * @param ids 需要删除的服务评价维度分组主键
     * @return 结果
     */
    @Override
    public int deleteServiceEvaluationGroupByIds(Long[] ids)
    {
        return serviceEvaluationGroupMapper.deleteServiceEvaluationGroupByIds(ids);
    }

    /**
     * 删除服务评价维度分组信息
     *
     * @param id 服务评价维度分组主键
     * @return 结果
     */
    @Override
    public int deleteServiceEvaluationGroupById(Long id)
    {
        return serviceEvaluationGroupMapper.deleteServiceEvaluationGroupById(id);
    }

}
