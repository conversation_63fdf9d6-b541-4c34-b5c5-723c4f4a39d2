package com.meiye.api.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.StoreProductLogMapper;
import com.meiye.api.domain.StoreProductLog;
import com.meiye.api.service.StoreProductLogService;

/**
 * 店面实际销售物定义变更记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class StoreProductLogServiceImpl implements StoreProductLogService
{
    @Autowired
    private StoreProductLogMapper storeProductLogMapper;

    /**
     * 查询店面实际销售物定义变更记录
     * 
     * @param id 店面实际销售物定义变更记录主键
     * @return 店面实际销售物定义变更记录
     */
    @Override
    public StoreProductLog selectStoreProductLogById(Long id)
    {
        return storeProductLogMapper.selectStoreProductLogById(id);
    }

    /**
     * 查询店面实际销售物定义变更记录列表
     * 
     * @param storeProductLog 店面实际销售物定义变更记录
     * @return 店面实际销售物定义变更记录
     */
    @Override
    public List<StoreProductLog> selectStoreProductLogList(StoreProductLog storeProductLog)
    {
        return storeProductLogMapper.selectStoreProductLogList(storeProductLog);
    }

    /**
     * 新增店面实际销售物定义变更记录
     * 
     * @param storeProductLog 店面实际销售物定义变更记录
     * @return 结果
     */
    @Override
    public int insertStoreProductLog(StoreProductLog storeProductLog)
    {
        return storeProductLogMapper.insertStoreProductLog(storeProductLog);
    }

    /**
     * 修改店面实际销售物定义变更记录
     * 
     * @param storeProductLog 店面实际销售物定义变更记录
     * @return 结果
     */
    @Override
    public int updateStoreProductLog(StoreProductLog storeProductLog)
    {
        return storeProductLogMapper.updateStoreProductLog(storeProductLog);
    }

    /**
     * 批量删除店面实际销售物定义变更记录
     * 
     * @param ids 需要删除的店面实际销售物定义变更记录主键
     * @return 结果
     */
    @Override
    public int deleteStoreProductLogByIds(Long[] ids)
    {
        return storeProductLogMapper.deleteStoreProductLogByIds(ids);
    }

    /**
     * 删除店面实际销售物定义变更记录信息
     * 
     * @param id 店面实际销售物定义变更记录主键
     * @return 结果
     */
    @Override
    public int deleteStoreProductLogById(Long id)
    {
        return storeProductLogMapper.deleteStoreProductLogById(id);
    }
}
