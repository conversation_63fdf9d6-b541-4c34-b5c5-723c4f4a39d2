package com.meiye.api.service;

import java.util.List;
import com.meiye.api.domain.Attendance;

/**
 * 出勤Service接口
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface AttendanceService
{
    /**
     * 查询出勤
     *
     * @param id 出勤主键
     * @return 出勤
     */
    public Attendance selectAttendanceById(Long id);

    /**
     * 查询出勤列表
     *
     * @param attendance 出勤
     * @return 出勤集合
     */
    public List<Attendance> selectAttendanceList(Attendance attendance);

    /**
     * 新增出勤
     *
     * @param attendance 出勤
     * @return 结果
     */
    public int insertAttendance(Attendance attendance);

    /**
     * 修改出勤
     *
     * @param attendance 出勤
     * @return 结果
     */
    public int updateAttendance(Attendance attendance);

    /**
     * 批量删除出勤
     *
     * @param ids 需要删除的出勤主键集合
     * @return 结果
     */
    public int deleteAttendanceByIds(Long[] ids);

    /**
     * 删除出勤信息
     *
     * @param id 出勤主键
     * @return 结果
     */
    public int deleteAttendanceById(Long id);
}
