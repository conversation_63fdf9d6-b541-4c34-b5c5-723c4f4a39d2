package com.meiye.api.service;

import java.util.List;
import com.meiye.api.domain.ProductLog;

/**
 * 主店面销售物定义变更记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface ProductLogService
{
    /**
     * 查询主店面销售物定义变更记录
     * 
     * @param id 主店面销售物定义变更记录主键
     * @return 主店面销售物定义变更记录
     */
    public ProductLog selectProductLogById(Long id);

    /**
     * 查询主店面销售物定义变更记录列表
     * 
     * @param productLog 主店面销售物定义变更记录
     * @return 主店面销售物定义变更记录集合
     */
    public List<ProductLog> selectProductLogList(ProductLog productLog);

    /**
     * 新增主店面销售物定义变更记录
     * 
     * @param productLog 主店面销售物定义变更记录
     * @return 结果
     */
    public int insertProductLog(ProductLog productLog);

    /**
     * 修改主店面销售物定义变更记录
     * 
     * @param productLog 主店面销售物定义变更记录
     * @return 结果
     */
    public int updateProductLog(ProductLog productLog);

    /**
     * 批量删除主店面销售物定义变更记录
     * 
     * @param ids 需要删除的主店面销售物定义变更记录主键集合
     * @return 结果
     */
    public int deleteProductLogByIds(Long[] ids);

    /**
     * 删除主店面销售物定义变更记录信息
     * 
     * @param id 主店面销售物定义变更记录主键
     * @return 结果
     */
    public int deleteProductLogById(Long id);
}
