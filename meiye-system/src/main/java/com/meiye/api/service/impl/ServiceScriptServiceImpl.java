package com.meiye.api.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.meiye.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.meiye.api.domain.ServiceScriptItem;
import com.meiye.api.mapper.ServiceScriptMapper;
import com.meiye.api.domain.ServiceScript;
import com.meiye.api.service.ServiceScriptService;

/**
 * 标准话术Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class ServiceScriptServiceImpl
        extends ServiceImpl<ServiceScriptMapper, ServiceScript>
        implements ServiceScriptService

{
    @Autowired
    private ServiceScriptMapper serviceScriptMapper;

    /**
     * 查询标准话术
     * 
     * @param id 标准话术主键
     * @return 标准话术
     */
    @Override
    public ServiceScript selectServiceScriptById(Long id)
    {
        return serviceScriptMapper.selectServiceScriptById(id);
    }

    /**
     * 查询标准话术列表
     * 
     * @param serviceScript 标准话术
     * @return 标准话术
     */
    @Override
    public List<ServiceScript> selectServiceScriptList(ServiceScript serviceScript)
    {
        return serviceScriptMapper.selectServiceScriptList(serviceScript);
    }

    /**
     * 新增标准话术
     * 
     * @param serviceScript 标准话术
     * @return 结果
     */
    @Transactional
    @Override
    public int insertServiceScript(ServiceScript serviceScript)
    {
        int rows = serviceScriptMapper.insertServiceScript(serviceScript);
        insertServiceScriptItem(serviceScript);
        return rows;
    }

    /**
     * 修改标准话术
     * 
     * @param serviceScript 标准话术
     * @return 结果
     */
    @Transactional
    @Override
    public int updateServiceScript(ServiceScript serviceScript)
    {
        serviceScriptMapper.deleteServiceScriptItemById(serviceScript.getId());
        insertServiceScriptItem(serviceScript);
        return serviceScriptMapper.updateServiceScript(serviceScript);
    }

    /**
     * 批量删除标准话术
     * 
     * @param ids 需要删除的标准话术主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteServiceScriptByIds(Long[] ids)
    {
        serviceScriptMapper.deleteServiceScriptItemByIds(ids);
        return serviceScriptMapper.deleteServiceScriptByIds(ids);
    }

    /**
     * 删除标准话术信息
     * 
     * @param id 标准话术主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteServiceScriptById(Long id)
    {
        serviceScriptMapper.deleteServiceScriptItemById(id);
        return serviceScriptMapper.deleteServiceScriptById(id);
    }

    /**
     * 新增标准话术单元信息
     * 
     * @param serviceScript 标准话术对象
     */
    public void insertServiceScriptItem(ServiceScript serviceScript)
    {
        List<ServiceScriptItem> serviceScriptItemList = serviceScript.getServiceScriptItemList();
        Long id = serviceScript.getId();
        if (StringUtils.isNotNull(serviceScriptItemList))
        {
            List<ServiceScriptItem> list = new ArrayList<ServiceScriptItem>();
            for (ServiceScriptItem serviceScriptItem : serviceScriptItemList)
            {
                serviceScriptItem.setId(id);
                list.add(serviceScriptItem);
            }
            if (list.size() > 0)
            {
                serviceScriptMapper.batchServiceScriptItem(list);
            }
        }
    }
}
