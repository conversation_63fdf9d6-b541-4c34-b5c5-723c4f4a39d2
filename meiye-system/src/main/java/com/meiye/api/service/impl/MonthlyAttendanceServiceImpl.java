package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.MonthlyAttendance;
import com.meiye.api.mapper.MonthlyAttendanceMapper;
import com.meiye.api.service.MonthlyAttendanceService;
@Service
public class MonthlyAttendanceServiceImpl extends ServiceImpl<MonthlyAttendanceMapper, MonthlyAttendance> implements MonthlyAttendanceService{

}
