package com.meiye.api.service;

import com.meiye.api.domain.UploadedImage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface UploadedImageService extends IService<UploadedImage>{
    /**
     * 查询用户图片信息
     *
     * @param id 用户图片信息主键
     * @return 用户图片信息
     */
    public UploadedImage selectUploadedImageById(Long id);

    /**
     * 查询用户图片信息列表
     *
     * @param uploadedImage 用户图片信息
     * @return 用户图片信息集合
     */
    public List<UploadedImage> selectUploadedImageList(UploadedImage uploadedImage);

    /**
     * 新增用户图片信息
     *
     * @param uploadedImage 用户图片信息
     * @return 结果
     */
    public int insertUploadedImage(UploadedImage uploadedImage);

    /**
     * 修改用户图片信息
     *
     * @param uploadedImage 用户图片信息
     * @return 结果
     */
    public int updateUploadedImage(UploadedImage uploadedImage);

    /**
     * 批量删除用户图片信息
     *
     * @param ids 需要删除的用户图片信息主键集合
     * @return 结果
     */
    public int deleteUploadedImageByIds(Long[] ids);

    /**
     * 删除用户图片信息信息
     *
     * @param id 用户图片信息主键
     * @return 结果
     */
    public int deleteUploadedImageById(Long id);

}
