package com.meiye.api.service;

import com.meiye.api.domain.Store;
import com.meiye.api.domain.StoreNotice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.vo.StoreNoticeVo;

import java.util.List;

public interface StoreNoticeService extends IService<StoreNotice>{
    /**
     * 查询店铺公告
     *
     * @param id 店铺公告主键
     * @return 店铺公告
     */
    public StoreNotice selectStoreNoticeById(Long id);

    List<Store> listAllStores();

    /**
     * 查询店铺公告列表
     *
     * @param storeNotice 店铺公告
     * @return 店铺公告集合
     */
    public List<StoreNotice> selectStoreNoticeList(StoreNotice storeNotice);

    public List<StoreNoticeVo> selectStoreNoticeVoList(StoreNotice storeNotice);

    /**
     * 新增店铺公告
     *
     * @param storeNotice 店铺公告
     * @return 结果
     */
    public int insertStoreNotice(StoreNotice storeNotice);

    /**
     * 修改店铺公告
     *
     * @param storeNotice 店铺公告
     * @return 结果
     */
    public int updateStoreNotice(StoreNotice storeNotice);

    /**
     * 批量删除店铺公告
     *
     * @param ids 需要删除的店铺公告主键集合
     * @return 结果
     */
    public int deleteStoreNoticeByIds(Long[] ids);

    /**
     * 删除店铺公告信息
     *
     * @param id 店铺公告主键
     * @return 结果
     */
    public int deleteStoreNoticeById(Long id);

}
