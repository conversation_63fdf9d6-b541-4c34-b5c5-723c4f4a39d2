package com.meiye.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.SaleOrder;
import com.meiye.api.domain.SaleOrderDetail;
import com.meiye.api.domain.SoldCard;
import com.meiye.api.domain.SoldCardProductTimes;
import com.meiye.api.dto.PendingOrderDto;
import com.meiye.api.dto.SaleOrderDTO;
import com.meiye.api.dto.StoreProductDTO;
import com.meiye.api.mapper.SaleOrderMapper;
import com.meiye.api.mapper.SoldCardMapper;
import com.meiye.api.mapper.SoldCardProductTimesMapper;
import com.meiye.api.service.SaleOrderService;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.domain.model.LoginUser;
import com.meiye.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

@Service
public class SaleOrderServiceImpl extends ServiceImpl<SaleOrderMapper, SaleOrder> implements SaleOrderService {


    @Autowired
    SoldCardMapper soldCardMapper;

    @Autowired
    SoldCardProductTimesMapper soldCardProductTimesMapper;


    @Override
    @Transactional
    public AjaxResult addSaleOrderNew(PendingOrderDto dto) {
        // 处理挂单逻辑
        if (Objects.equals(dto.getType(), "pendingOrder")) {
            //1，创建订单
            SaleOrder saleOrder = new SaleOrder();
            if (dto.getMemberId() != null) {
                saleOrder.setMemberId(dto.getMemberId());
            } else if (dto.getTempName() != null && !dto.getTempName().isEmpty()) {
                saleOrder.setTempName(dto.getTempName());
            }else {
                throw new RuntimeException("未选择用户信息");
            }
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String todayStr = today.format(formatter);
            QueryWrapper<SaleOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("store_id", dto.getStoreId());
            queryWrapper.apply("DATE(created_at) = CURDATE()");
            Integer count = Math.toIntExact(this.count(queryWrapper)) + 1;
// 格式: SO + 日期 + 3位流水号
            String sn = "SO" + todayStr + String.format("%03d", count);

            saleOrder.setSn(sn);



        }


        return null;
    }


    /*--------------------------------------------------------------------------------------*/
    @Override
    public void addSaleOrder(SaleOrderDTO dto) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SaleOrder saleOrder = new SaleOrder();
        if (dto.getStoreProductList() != null && !dto.getStoreProductList().isEmpty()) {
            for (StoreProductDTO storeProductDTO : dto.getStoreProductList()) {
                if ("card".equals(storeProductDTO.getCategoryType())) {
                    // 创建SoldCard对象并设置公共属性
                    SoldCard soldCard = createBaseSoldCard(storeProductDTO, dto, loginUser);
                    // 根据卡类型处理特有属性和关联操作
                    switch (storeProductDTO.getConsumptionCardType()) {
                        case "balance":
                            soldCard.setRealBalance(storeProductDTO.getRealBalance());
                            soldCard.setGiftBalance(storeProductDTO.getGiftBalance());
                            soldCardMapper.insert(soldCard);
                            break;
                        case "discount":
                            soldCard.setDiscountRatio(storeProductDTO.getDiscountRatio());
                            soldCardMapper.insert(soldCard);
                            break;
                        case "experience":
                        case "times":
                            soldCardMapper.insert(soldCard);
                            handleProductItems(storeProductDTO, soldCard);
                            break;
                        default:
                            break;
                    }
                } else if ("type".equals(storeProductDTO.getCategoryType())) {

                } else if ("service".equals(storeProductDTO.getCategoryType())) {

                }
                SaleOrderDetail saleOrderDetail = new SaleOrderDetail();
                saleOrderDetail.setSaleOrderId(saleOrder.getId());

            }
        }
    }


    private SoldCard createBaseSoldCard(StoreProductDTO dto, SaleOrderDTO member, LoginUser loginUser) {
        SoldCard card = new SoldCard();
        card.setStoreProductId(dto.getStoreProductId());
        card.setMemberId(member.getMemberId());
        card.setCardSn("no" + System.currentTimeMillis());
        card.setDisabled(false);
        card.setCreatedByEmployeeId(loginUser.getUserId());
        card.setCreatedAt(new Date());
        return card;
    }

    private void handleProductItems(StoreProductDTO dto, SoldCard soldCard) {
        if (dto.getProductItemList() == null || dto.getProductItemList().isEmpty()) return;
        for (StoreProductDTO.ProductItemVO item : dto.getProductItemList()) {
            SoldCardProductTimes productTimes = new SoldCardProductTimes();
            productTimes.setSaleStoreProductId(item.getMainProductId());
            productTimes.setSoldCardId(soldCard.getId());
            productTimes.setQuantity(item.getQuantity());
            soldCardProductTimesMapper.insert(productTimes);
        }
    }
}
/*--------------------------------------------------------------------------------------*/