package com.meiye.api.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.service.MemberProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.MemberProductMapper;
import com.meiye.api.domain.MemberProduct;

/**
 * 适于该会员的推荐项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class MemberProductServiceImpl
        extends ServiceImpl<MemberProductMapper, MemberProduct>
        implements MemberProductService
{
    @Autowired
    private MemberProductMapper memberProductMapper;

    /**
     * 查询适于该会员的推荐项目
     * 
     * @param id 适于该会员的推荐项目主键
     * @return 适于该会员的推荐项目
     */
    @Override
    public MemberProduct selectMemberProductById(Long id)
    {
        return memberProductMapper.selectMemberProductById(id);
    }

    /**
     * 查询适于该会员的推荐项目列表
     * 
     * @param memberProduct 适于该会员的推荐项目
     * @return 适于该会员的推荐项目
     */
    @Override
    public List<MemberProduct> selectMemberProductList(MemberProduct memberProduct)
    {
        return memberProductMapper.selectMemberProductList(memberProduct);
    }

    /**
     * 新增适于该会员的推荐项目
     * 
     * @param memberProduct 适于该会员的推荐项目
     * @return 结果
     */
    @Override
    public int insertMemberProduct(MemberProduct memberProduct)
    {
        return memberProductMapper.insertMemberProduct(memberProduct);
    }

    /**
     * 修改适于该会员的推荐项目
     * 
     * @param memberProduct 适于该会员的推荐项目
     * @return 结果
     */
    @Override
    public int updateMemberProduct(MemberProduct memberProduct)
    {
        return memberProductMapper.updateMemberProduct(memberProduct);
    }

    /**
     * 批量删除适于该会员的推荐项目
     * 
     * @param ids 需要删除的适于该会员的推荐项目主键
     * @return 结果
     */
    @Override
    public int deleteMemberProductByIds(Long[] ids)
    {
        return memberProductMapper.deleteMemberProductByIds(ids);
    }

    /**
     * 删除适于该会员的推荐项目信息
     * 
     * @param id 适于该会员的推荐项目主键
     * @return 结果
     */
    @Override
    public int deleteMemberProductById(Long id)
    {
        return memberProductMapper.deleteMemberProductById(id);
    }
}
