package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.mapper.ServiceLogMapper;
import com.meiye.api.domain.ServiceLog;
import com.meiye.api.service.ServiceLogService;
@Service
public class ServiceLogServiceImpl
        extends ServiceImpl<ServiceLogMapper, ServiceLog>
        implements ServiceLogService{
    @Autowired
    private ServiceLogMapper serviceLogMapper;

    /**
     * 查询服务实施
     *
     * @param id 服务实施主键
     * @return 服务实施
     */
    @Override
    public ServiceLog selectServiceLogById(Long id)
    {
        return serviceLogMapper.selectServiceLogById(id);
    }

    /**
     * 查询服务实施列表
     *
     * @param serviceLog 服务实施
     * @return 服务实施
     */
    @Override
    public List<ServiceLog> selectServiceLogList(ServiceLog serviceLog)
    {
        return serviceLogMapper.selectServiceLogList(serviceLog);
    }

    /**
     * 新增服务实施
     *
     * @param serviceLog 服务实施
     * @return 结果
     */
    @Override
    public int insertServiceLog(ServiceLog serviceLog)
    {
        return serviceLogMapper.insertServiceLog(serviceLog);
    }

    /**
     * 修改服务实施
     *
     * @param serviceLog 服务实施
     * @return 结果
     */
    @Override
    public int updateServiceLog(ServiceLog serviceLog)
    {
        return serviceLogMapper.updateServiceLog(serviceLog);
    }

    /**
     * 批量删除服务实施
     *
     * @param ids 需要删除的服务实施主键
     * @return 结果
     */
    @Override
    public int deleteServiceLogByIds(Long[] ids)
    {
        return serviceLogMapper.deleteServiceLogByIds(ids);
    }

    /**
     * 删除服务实施信息
     *
     * @param id 服务实施主键
     * @return 结果
     */
    @Override
    public int deleteServiceLogById(Long id)
    {
        return serviceLogMapper.deleteServiceLogById(id);
    }
}
