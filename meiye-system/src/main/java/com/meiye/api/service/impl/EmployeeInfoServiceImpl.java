package com.meiye.api.service.impl;

import com.meiye.api.vo.EmployeeCommissionItem;
import com.meiye.api.vo.EmployeeCommissionOptionVO;
import com.meiye.api.vo.EmployeeInfoVo;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.EmployeeInfo;
import com.meiye.api.mapper.EmployeeInfoMapper;
import com.meiye.api.service.EmployeeInfoService;
@Service
public class EmployeeInfoServiceImpl extends ServiceImpl<EmployeeInfoMapper, EmployeeInfo> implements EmployeeInfoService{

    @Autowired
    private EmployeeInfoMapper employeeInfoMapper;

    /**
     * 查询员工扩展信息
     *
     * @param id 员工扩展信息主键
     * @return 员工扩展信息
     */
    @Override
    public EmployeeInfo selectEmployeeInfoById(Long id)
    {
        return employeeInfoMapper.selectEmployeeInfoById(id);
    }

    /**
     * 查询员工扩展信息列表
     *
     * @param employeeInfo 员工扩展信息
     * @return 员工扩展信息
     */
    @Override
    public List<EmployeeInfo> selectEmployeeInfoList(EmployeeInfo employeeInfo)
    {
        return employeeInfoMapper.selectEmployeeInfoList(employeeInfo);
    }

    /**
     * 新增员工扩展信息
     *
     * @param employeeInfo 员工扩展信息
     * @return 结果
     */
    @Override
    public int insertEmployeeInfo(EmployeeInfo employeeInfo)
    {
        return employeeInfoMapper.insertEmployeeInfo(employeeInfo);
    }

    /**
     * 修改员工扩展信息
     *
     * @param employeeInfo 员工扩展信息
     * @return 结果
     */
    @Override
    public int updateEmployeeInfo(EmployeeInfo employeeInfo)
    {
        return employeeInfoMapper.updateEmployeeInfo(employeeInfo);
    }

    /**
     * 批量删除员工扩展信息
     *
     * @param ids 需要删除的员工扩展信息主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeInfoByIds(Long[] ids)
    {
        return employeeInfoMapper.deleteEmployeeInfoByIds(ids);
    }

    /**
     * 删除员工扩展信息信息
     *
     * @param id 员工扩展信息主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeInfoById(Long id)
    {
        return employeeInfoMapper.deleteEmployeeInfoById(id);
    }

    @Override
    public List<EmployeeInfoVo> selectEmployeeVoList(EmployeeInfoVo employeeInfo) {
        return employeeInfoMapper.selectEmployeeVoList(employeeInfo);
    }

    @Override
    public EmployeeInfoVo selectEmployeeInfoVoById(Long id) {
        return employeeInfoMapper.selectEmployeeVoById(id);
    }

    @Override
    public List<EmployeeCommissionOptionVO> selectECOptions(Long storeId) {
        return employeeInfoMapper.selectECOptions(storeId);
    }
}
