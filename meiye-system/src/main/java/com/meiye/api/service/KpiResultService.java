package com.meiye.api.service;

import com.meiye.api.domain.KpiResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface KpiResultService extends IService<KpiResult>{

    /**
     * 查询KPI结果
     *
     * @param id KPI结果主键
     * @return KPI结果
     */
    public KpiResult selectKpiResultById(Long id);

    /**
     * 查询KPI结果列表
     *
     * @param kpiResult KPI结果
     * @return KPI结果集合
     */
    public List<KpiResult> selectKpiResultList(KpiResult kpiResult);

    /**
     * 新增KPI结果
     *
     * @param kpiResult KPI结果
     * @return 结果
     */
    public int insertKpiResult(KpiResult kpiResult);

    /**
     * 修改KPI结果
     *
     * @param kpiResult KPI结果
     * @return 结果
     */
    public int updateKpiResult(KpiResult kpiResult);

    /**
     * 批量删除KPI结果
     *
     * @param ids 需要删除的KPI结果主键集合
     * @return 结果
     */
    public int deleteKpiResultByIds(Long[] ids);

    /**
     * 删除KPI结果信息
     *
     * @param id KPI结果主键
     * @return 结果
     */
    public int deleteKpiResultById(Long id);

}
