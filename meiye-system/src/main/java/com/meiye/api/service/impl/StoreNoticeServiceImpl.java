package com.meiye.api.service.impl;

import com.meiye.api.domain.Store;
import com.meiye.api.domain.StoreNoticeItem;
import com.meiye.api.mapper.StoreMapper;
import com.meiye.api.mapper.StoreNoticeItemMapper;
import com.meiye.api.vo.StoreNoticeVo;
import com.meiye.common.core.domain.entity.SysUser;
import com.meiye.system.mapper.SysUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.StoreNotice;
import com.meiye.api.mapper.StoreNoticeMapper;
import com.meiye.api.service.StoreNoticeService;
import org.springframework.transaction.annotation.Transactional;

import static com.meiye.common.utils.SecurityUtils.getUserId;

@Service
public class StoreNoticeServiceImpl extends ServiceImpl<StoreNoticeMapper, StoreNotice> implements StoreNoticeService{
    @Autowired
    private StoreNoticeMapper storeNoticeMapper;
    @Autowired
    private StoreNoticeItemMapper storeNoticeItemMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private StoreMapper storeMapper;

    /**
     * 查询店铺公告
     *
     * @param id 店铺公告主键
     * @return 店铺公告
     */
    @Override
    public StoreNotice selectStoreNoticeById(Long id)
    {
        return storeNoticeMapper.selectStoreNoticeById(id);
    }

    @Override
    public List<Store> listAllStores() {
        return storeMapper.selectList(null);
    }

    /**
     * 查询店铺公告列表
     *
     * @param storeNotice 店铺公告
     * @return 店铺公告
     */
    @Override
    public List<StoreNotice> selectStoreNoticeList(StoreNotice storeNotice)
    {
        return storeNoticeMapper.selectStoreNoticeList(storeNotice);
    }

    public List<StoreNoticeVo> selectStoreNoticeVoList(StoreNotice storeNotice) {
        // 获取当前用户ID
        Long userId = getUserId();

        // 1. 查询当前用户的公告项
        StoreNoticeItem queryItem = new StoreNoticeItem();
        queryItem.setEmployeeId(userId);
        List<StoreNoticeItem> userItems = storeNoticeItemMapper.selectStoreNoticeItemList(queryItem);

        if (userItems == null || userItems.isEmpty()) {
            return Collections.emptyList();
        }

        // 用于存储最终结果
        List<StoreNoticeVo> resultList = new ArrayList<>();

        // 遍历用户公告项
        for (StoreNoticeItem noticeItem : userItems) {
            // 2. 根据公告ID查询公告详情
            StoreNotice queryNotice = new StoreNotice();
            queryNotice.setId(noticeItem.getStoreNoticeId());
            List<StoreNotice> noticeList = storeNoticeMapper.selectStoreNoticeListNoTime(queryNotice);

            if (noticeList != null && !noticeList.isEmpty()) {
                // 3. 转换为VO对象
                for (StoreNotice notice : noticeList) {
                    StoreNoticeVo vo = new StoreNoticeVo();
                    vo.setId(notice.getId());

                    vo.setStoreName(notice.getStoreId());//目前是店铺id

                    vo.setTitle(notice.getTitle());
                    vo.setContent(notice.getContent());
                    vo.setPublicTime(notice.getPublicTime());
                    vo.setCreatedAt(notice.getCreatedAt());

                    SysUser user= sysUserMapper.selectUserById(Long.valueOf(notice.getCreatedBy()));
                    vo.setCreator(user.getNickName());

                    vo.setEmployeeId(userId);
                    vo.setIsRead(noticeItem.getIsRead());
                    vo.setReadTime(noticeItem.getReadTime());

                    resultList.add(vo);
                }
            }
        }

        // 4. 返回构建好的VO列表
        return resultList;
    }


    // 获取店铺名称的方法（需要实现）
    private String getStoreNameById(Long storeId) {
        // 这里需要调用店铺服务或DAO获取店铺名称
        // 示例：return storeService.getStoreNameById(storeId);
        return "店铺" + storeId; // 示例返回值
    }

    // 获取用户名称的方法（需要实现）
    private String getUserNameById(String userId) {
        // 这里需要调用用户服务或DAO获取用户名称
        // 示例：return userService.getUserNameById(userId);
        return "用户" + userId; // 示例返回值
    }

    /**
     * 新增店铺公告
     *
     * @param storeNotice 店铺公告
     * @return 结果
     */
    @Override
    @Transactional
    public int insertStoreNotice(StoreNotice storeNotice)
    {
        int mainResult = storeNoticeMapper.insertStoreNotice(storeNotice);

        StoreNoticeItem storeNoticeItem=new StoreNoticeItem();
        storeNoticeItem.setStoreNoticeId(storeNotice.getId());
        storeNoticeItem.setIsRead(false);

        Long[] eids=storeNotice.getEmployeeIds();
        Long[] sids=storeNotice.getStoreIds();
        if (eids!=null){
            int length=eids.length;
            for (int i=0;i<length;i++){
                storeNoticeItem.setEmployeeId(eids[i]);
                SysUser user= sysUserMapper.selectUserById(eids[i]);
                storeNoticeItem.setStoreId(user.getStoreId());
                storeNoticeItemMapper.insertStoreNoticeItem(storeNoticeItem);
            }
        }

        if (sids != null && sids.length > 0) {
            for (int i = 0; i < sids.length; i++) {
                Long currentStoreId = sids[i]; // 当前店铺ID

                // 1. 查询该店铺下的所有员工
                SysUser userQuery = new SysUser();
                userQuery.setStoreId(currentStoreId); // 设置店铺ID作为查询条件
                List<SysUser> userList = sysUserMapper.selectUserList(userQuery); // 获取该店铺所有员工

                // 2. 若该店铺有员工，为每个员工创建公告记录
                if (userList != null && !userList.isEmpty()) {
                    for (SysUser user : userList) {
                        // 关键：每次循环创建新的StoreNoticeItem对象，避免复用导致的字段值覆盖
                        StoreNoticeItem noticeItem = new StoreNoticeItem();

                        // 设置公共字段（从原对象复制或直接赋值）
                        noticeItem.setStoreNoticeId(storeNotice.getId()); // 公告主表ID
                        noticeItem.setStoreId(currentStoreId); // 当前店铺ID
                        noticeItem.setIsRead(false); // 初始化为未读

                        // 设置当前员工ID
                        noticeItem.setEmployeeId(user.getUserId()); // 关联该员工

                        // 插入记录
                        storeNoticeItemMapper.insertStoreNoticeItem(noticeItem);
                    }
                }
            }
        }
        return mainResult;
    }

    /**
     * 修改店铺公告
     *
     * @param storeNotice 店铺公告
     * @return 结果
     */
    @Override
    public int updateStoreNotice(StoreNotice storeNotice)
    {

        return storeNoticeMapper.updateStoreNotice(storeNotice);
    }

    /**
     * 批量删除店铺公告
     *
     * @param ids 需要删除的店铺公告主键
     * @return 结果
     */
    @Override
    public int deleteStoreNoticeByIds(Long[] ids)
    {
        return storeNoticeMapper.deleteStoreNoticeByIds(ids);
    }

    /**
     * 删除店铺公告信息
     *
     * @param id 店铺公告主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteStoreNoticeById(Long id)
    {
        storeNoticeItemMapper.deleteStoreNoticeItemByNId(id);
        return storeNoticeMapper.deleteStoreNoticeById(id);
    }
}
