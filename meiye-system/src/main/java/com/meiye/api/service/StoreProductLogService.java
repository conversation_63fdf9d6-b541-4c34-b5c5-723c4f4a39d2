package com.meiye.api.service;

import java.util.List;
import com.meiye.api.domain.StoreProductLog;

/**
 * 店面实际销售物定义变更记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface StoreProductLogService
{
    /**
     * 查询店面实际销售物定义变更记录
     * 
     * @param id 店面实际销售物定义变更记录主键
     * @return 店面实际销售物定义变更记录
     */
    public StoreProductLog selectStoreProductLogById(Long id);

    /**
     * 查询店面实际销售物定义变更记录列表
     * 
     * @param storeProductLog 店面实际销售物定义变更记录
     * @return 店面实际销售物定义变更记录集合
     */
    public List<StoreProductLog> selectStoreProductLogList(StoreProductLog storeProductLog);

    /**
     * 新增店面实际销售物定义变更记录
     * 
     * @param storeProductLog 店面实际销售物定义变更记录
     * @return 结果
     */
    public int insertStoreProductLog(StoreProductLog storeProductLog);

    /**
     * 修改店面实际销售物定义变更记录
     * 
     * @param storeProductLog 店面实际销售物定义变更记录
     * @return 结果
     */
    public int updateStoreProductLog(StoreProductLog storeProductLog);

    /**
     * 批量删除店面实际销售物定义变更记录
     * 
     * @param ids 需要删除的店面实际销售物定义变更记录主键集合
     * @return 结果
     */
    public int deleteStoreProductLogByIds(Long[] ids);

    /**
     * 删除店面实际销售物定义变更记录信息
     * 
     * @param id 店面实际销售物定义变更记录主键
     * @return 结果
     */
    public int deleteStoreProductLogById(Long id);
}
