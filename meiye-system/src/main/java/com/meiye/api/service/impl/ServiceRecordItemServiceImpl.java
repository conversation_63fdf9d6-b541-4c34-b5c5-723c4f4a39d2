package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.mapper.ServiceRecordItemMapper;
import com.meiye.api.domain.ServiceRecordItem;
import com.meiye.api.service.ServiceRecordItemService;
@Service
public class ServiceRecordItemServiceImpl
        extends ServiceImpl<ServiceRecordItemMapper,
        ServiceRecordItem> implements ServiceRecordItemService{

    @Autowired
    private ServiceRecordItemMapper serviceRecordItemMapper;

    /**
     * 查询服务记录的话术单元信息
     *
     * @param id 服务记录的话术单元信息主键
     * @return 服务记录的话术单元信息
     */
    @Override
    public ServiceRecordItem selectServiceRecordItemById(Long id)
    {
        return serviceRecordItemMapper.selectServiceRecordItemById(id);
    }

    /**
     * 查询服务记录的话术单元信息列表
     *
     * @param serviceRecordItem 服务记录的话术单元信息
     * @return 服务记录的话术单元信息
     */
    @Override
    public List<ServiceRecordItem> selectServiceRecordItemList(ServiceRecordItem serviceRecordItem)
    {
        return serviceRecordItemMapper.selectServiceRecordItemList(serviceRecordItem);
    }

    /**
     * 新增服务记录的话术单元信息
     *
     * @param serviceRecordItem 服务记录的话术单元信息
     * @return 结果
     */
    @Override
    public int insertServiceRecordItem(ServiceRecordItem serviceRecordItem)
    {
        return serviceRecordItemMapper.insertServiceRecordItem(serviceRecordItem);
    }

    /**
     * 修改服务记录的话术单元信息
     *
     * @param serviceRecordItem 服务记录的话术单元信息
     * @return 结果
     */
    @Override
    public int updateServiceRecordItem(ServiceRecordItem serviceRecordItem)
    {
        return serviceRecordItemMapper.updateServiceRecordItem(serviceRecordItem);
    }

    /**
     * 批量删除服务记录的话术单元信息
     *
     * @param ids 需要删除的服务记录的话术单元信息主键
     * @return 结果
     */
    @Override
    public int deleteServiceRecordItemByIds(Long[] ids)
    {
        return serviceRecordItemMapper.deleteServiceRecordItemByIds(ids);
    }

    /**
     * 删除服务记录的话术单元信息信息
     *
     * @param id 服务记录的话术单元信息主键
     * @return 结果
     */
    @Override
    public int deleteServiceRecordItemById(Long id)
    {
        return serviceRecordItemMapper.deleteServiceRecordItemById(id);
    }


}
