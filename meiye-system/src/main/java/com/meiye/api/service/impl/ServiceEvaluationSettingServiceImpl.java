package com.meiye.api.service.impl;

import com.meiye.api.domain.Store;
import com.meiye.api.service.IStoreService;
import com.meiye.common.utils.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.ServiceEvaluationSetting;
import com.meiye.api.mapper.ServiceEvaluationSettingMapper;
import com.meiye.api.service.ServiceEvaluationSettingService;
import com.meiye.common.utils.SecurityUtils;

@Service
public class ServiceEvaluationSettingServiceImpl
        extends ServiceImpl<ServiceEvaluationSettingMapper, ServiceEvaluationSetting>
        implements ServiceEvaluationSettingService{
    @Autowired
    private ServiceEvaluationSettingMapper serviceEvaluationSettingMapper;
    
    @Autowired
    private IStoreService storeService;

    /**
     * 查询服务评价设置
     *
     * @param id 服务评价设置主键
     * @return 服务评价设置
     */
    @Override
    public ServiceEvaluationSetting selectServiceEvaluationSettingById(Long id)
    {
        return serviceEvaluationSettingMapper.selectServiceEvaluationSettingById(id);
    }

    /**
     * 查询服务评价设置列表
     *
     * @param serviceEvaluationSetting 服务评价设置
     * @return 服务评价设置
     */
    @Override
    public List<ServiceEvaluationSetting> selectServiceEvaluationSettingList(ServiceEvaluationSetting serviceEvaluationSetting)
    {
        return serviceEvaluationSettingMapper.selectServiceEvaluationSettingList(serviceEvaluationSetting);
    }

    /**
     * 新增服务评价设置
     *
     * @param serviceEvaluationSetting 服务评价设置
     * @return 结果
     */
    @Override
    public int insertServiceEvaluationSetting(ServiceEvaluationSetting serviceEvaluationSetting)
    {
        // 自动填充创建人和创建时间
        if (serviceEvaluationSetting.getCreatedBy() == null) {
            serviceEvaluationSetting.setCreatedBy(SecurityUtils.getUserId());
        }
        if (serviceEvaluationSetting.getCreatedAt() == null) {
            serviceEvaluationSetting.setCreatedAt(DateUtils.getNowDate());
        }
        
        // 填充主店面和店面名称
        fillStoreNames(serviceEvaluationSetting);
        
        return serviceEvaluationSettingMapper.insertServiceEvaluationSetting(serviceEvaluationSetting);
    }

    /**
     * 修改服务评价设置
     *
     * @param serviceEvaluationSetting 服务评价设置
     * @return 结果
     */
    @Override
    public int updateServiceEvaluationSetting(ServiceEvaluationSetting serviceEvaluationSetting)
    {
        // 填充主店面和店面名称
        fillStoreNames(serviceEvaluationSetting);
        
        return serviceEvaluationSettingMapper.updateServiceEvaluationSetting(serviceEvaluationSetting);
    }

    /**
     * 批量删除服务评价设置
     *
     * @param ids 需要删除的服务评价设置主键
     * @return 结果
     */
    @Override
    public int deleteServiceEvaluationSettingByIds(Long[] ids)
    {
        return serviceEvaluationSettingMapper.deleteServiceEvaluationSettingByIds(ids);
    }

    /**
     * 删除服务评价设置信息
     *
     * @param id 服务评价设置主键
     * @return 结果
     */
    @Override
    public int deleteServiceEvaluationSettingById(Long id)
    {
        return serviceEvaluationSettingMapper.deleteServiceEvaluationSettingById(id);
    }
    
    /**
     * 填充主店面和店面名称
     * 
     * @param serviceEvaluationSetting 服务评价设置
     */
    private void fillStoreNames(ServiceEvaluationSetting serviceEvaluationSetting) {
        // 填充主店面名称
        if (serviceEvaluationSetting.getRootStoreId() != null) {
            Store rootStore = storeService.getById(serviceEvaluationSetting.getRootStoreId());
            if (rootStore != null) {
                serviceEvaluationSetting.setRootStoreName(rootStore.getName());
            }
        }
        
        // 填充店面名称
        if (serviceEvaluationSetting.getStoreId() != null) {
            Store store = storeService.getById(serviceEvaluationSetting.getStoreId());
            if (store != null) {
                serviceEvaluationSetting.setStoreName(store.getName());
            }
        }
    }
}