package com.meiye.api.service;

import com.meiye.api.domain.MemberFeatureType;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface MemberFeatureTypeService extends IService<MemberFeatureType>{

    /**
     * 查询特性类型
     *
     * @param id 特性类型主键
     * @return 特性类型
     */
    public MemberFeatureType selectMemberFeatureTypeById(Long id);

    /**
     * 查询特性类型列表
     *
     * @param memberFeatureType 特性类型
     * @return 特性类型集合
     */
    public List<MemberFeatureType> selectMemberFeatureTypeList(MemberFeatureType memberFeatureType);

    List<MemberFeatureType> selectAllMemberFeatureTypes();

    /**
     * 新增特性类型
     *
     * @param memberFeatureType 特性类型
     * @return 结果
     */
    public int insertMemberFeatureType(MemberFeatureType memberFeatureType);

    /**
     * 修改特性类型
     *
     * @param memberFeatureType 特性类型
     * @return 结果
     */
    public int updateMemberFeatureType(MemberFeatureType memberFeatureType);

    /**
     * 批量删除特性类型
     *
     * @param ids 需要删除的特性类型主键集合
     * @return 结果
     */
    public int deleteMemberFeatureTypeByIds(Long[] ids);

    /**
     * 删除特性类型信息
     *
     * @param id 特性类型主键
     * @return 结果
     */
    public int deleteMemberFeatureTypeById(Long id);
}
