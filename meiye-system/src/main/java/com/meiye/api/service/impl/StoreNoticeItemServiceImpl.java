package com.meiye.api.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.meiye.api.domain.StoreNotice;
import com.meiye.api.mapper.StoreNoticeMapper;
import com.meiye.api.vo.StoreNoticeVo;
import com.meiye.common.core.domain.entity.SysUser;
import com.meiye.system.mapper.SysUserMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.StoreNoticeItemMapper;
import com.meiye.api.domain.StoreNoticeItem;
import com.meiye.api.service.StoreNoticeItemService;

import static com.meiye.common.utils.SecurityUtils.getUserId;

/**
 * 店铺公告发布记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class StoreNoticeItemServiceImpl implements StoreNoticeItemService
{
    @Autowired
    private StoreNoticeItemMapper storeNoticeItemMapper;
    @Autowired
    private StoreNoticeMapper storeNoticeMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询店铺公告发布记录
     * 
     * @param id 店铺公告发布记录主键
     * @return 店铺公告发布记录
     */
    @Override
    public StoreNoticeItem selectStoreNoticeItemById(Long id)
    {
        return storeNoticeItemMapper.selectStoreNoticeItemById(id);
    }

    /**
     * 查询店铺公告发布记录列表
     * 
     * @param storeNoticeItem 店铺公告发布记录
     * @return 店铺公告发布记录
     */
    @Override
    public List<StoreNoticeItem> selectStoreNoticeItemList(StoreNoticeItem storeNoticeItem)
    {
        return storeNoticeItemMapper.selectStoreNoticeItemList(storeNoticeItem);
    }

    /**
     * 查询店铺公告发布记录列表
     *
     * @param storeNoticeVo 店铺公告发布记录
     * @return 店铺公告发布记录
     */
    @Override
    public List<StoreNoticeVo> selectStoreNoticeVoList(StoreNoticeVo storeNoticeVo)
    {
        StoreNoticeItem noticeItem=new StoreNoticeItem();
        BeanUtils.copyProperties(storeNoticeVo, noticeItem);
        System.out.println("Vo:"+storeNoticeVo);
        System.out.println("noticeItem:"+noticeItem);
        List<StoreNoticeItem> noticeItems=storeNoticeItemMapper.selectStoreNoticeItemList(noticeItem);

        List<StoreNoticeVo>list=new ArrayList<>();

        for (StoreNoticeItem storeNoticeItem : noticeItems) {
            System.out.println("item:"+storeNoticeItem.getId()+storeNoticeItem.getReadTime());
            StoreNoticeVo vo=new StoreNoticeVo();
            vo.setId(storeNoticeItem.getId());
            vo.setIsRead(storeNoticeItem.getIsRead());
            vo.setReadTime(storeNoticeItem.getReadTime());
            StoreNotice storeNotice=storeNoticeMapper.selectStoreNoticeById(storeNoticeItem.getStoreNoticeId());
            List<StoreNotice> storeNotices=storeNoticeMapper.selectStoreNoticeListNoTime(storeNotice);
            for (StoreNotice notice : storeNotices) {
                System.out.println("notice:"+notice.getTitle()+notice.getContent());
                vo.setStoreName(notice.getStoreId());
                vo.setTitle(notice.getTitle());
                vo.setContent(notice.getContent());
                vo.setPublicTime(notice.getPublicTime());
                vo.setCreatedAt(notice.getCreatedAt());
                vo.setEmployeeId(getUserId());
                SysUser user = sysUserMapper.selectUserById(Long.valueOf(notice.getCreatedBy()));
                vo.setCreator(user.getNickName());
            }
            list.add(vo);
        }
        return list;
    }

    /**
     * 新增店铺公告发布记录
     * 
     * @param storeNoticeItem 店铺公告发布记录
     * @return 结果
     */
    @Override
    public int insertStoreNoticeItem(StoreNoticeItem storeNoticeItem)
    {
        return storeNoticeItemMapper.insertStoreNoticeItem(storeNoticeItem);
    }

    /**
     * 修改店铺公告发布记录
     * 
     * @param storeNoticeItem 店铺公告发布记录
     * @return 结果
     */
    @Override
    public int updateStoreNoticeItem(StoreNoticeItem storeNoticeItem)
    {
        return storeNoticeItemMapper.updateStoreNoticeItem(storeNoticeItem);
    }

    /**
     * 员工已读消息
     *
     * @param id 员工已读消息
     * @return 结果
     */
    @Override
    public Boolean read(Long id) {
        StoreNoticeItem storeNoticeItem = storeNoticeItemMapper.selectStoreNoticeItemById(id);
        if (storeNoticeItem == null)
            return false;

        storeNoticeItem.setIsRead(true);
        storeNoticeItem.setReadTime(new Date());

        int affectedRows = storeNoticeItemMapper.updateStoreNoticeItem(storeNoticeItem);
        return affectedRows > 0;
    }

    /**
     * 批量删除店铺公告发布记录
     * 
     * @param ids 需要删除的店铺公告发布记录主键
     * @return 结果
     */
    @Override
    public int deleteStoreNoticeItemByIds(Long[] ids)
    {
        return storeNoticeItemMapper.deleteStoreNoticeItemByIds(ids);
    }

    /**
     * 删除店铺公告发布记录信息
     * 
     * @param id 店铺公告发布记录主键
     * @return 结果
     */
    @Override
    public int deleteStoreNoticeItemById(Long id)
    {
        return storeNoticeItemMapper.deleteStoreNoticeItemById(id);
    }
}
