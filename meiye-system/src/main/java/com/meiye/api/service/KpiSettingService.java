package com.meiye.api.service;

import com.meiye.api.domain.KpiSetting;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface KpiSettingService extends IService<KpiSetting>{
    /**
     * 查询KPI设置
     *
     * @param id KPI设置主键
     * @return KPI设置
     */
    public KpiSetting selectKpiSettingById(Long id);

    /**
     * 查询KPI设置列表
     *
     * @param kpiSetting KPI设置
     * @return KPI设置集合
     */
    public List<KpiSetting> selectKpiSettingList(KpiSetting kpiSetting);

    /**
     * 新增KPI设置
     *
     * @param kpiSetting KPI设置
     * @return 结果
     */
    public int insertKpiSetting(KpiSetting kpiSetting);

    /**
     * 修改KPI设置
     *
     * @param kpiSetting KPI设置
     * @return 结果
     */
    public int updateKpiSetting(KpiSetting kpiSetting);

    /**
     * 批量删除KPI设置
     *
     * @param ids 需要删除的KPI设置主键集合
     * @return 结果
     */
    public int deleteKpiSettingByIds(Long[] ids);

    /**
     * 删除KPI设置信息
     *
     * @param id KPI设置主键
     * @return 结果
     */
    public int deleteKpiSettingById(Long id);


}
