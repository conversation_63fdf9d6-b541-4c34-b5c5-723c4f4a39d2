package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.UploadedImage;
import com.meiye.api.mapper.UploadedImageMapper;
import com.meiye.api.service.UploadedImageService;
@Service
public class UploadedImageServiceImpl extends ServiceImpl<UploadedImageMapper, UploadedImage> implements UploadedImageService{
    @Autowired
    private UploadedImageMapper uploadedImageMapper;

    /**
     * 查询用户图片信息
     *
     * @param id 用户图片信息主键
     * @return 用户图片信息
     */
    @Override
    public UploadedImage selectUploadedImageById(Long id)
    {
        return uploadedImageMapper.selectUploadedImageById(id);
    }

    /**
     * 查询用户图片信息列表
     *
     * @param uploadedImage 用户图片信息
     * @return 用户图片信息
     */
    @Override
    public List<UploadedImage> selectUploadedImageList(UploadedImage uploadedImage)
    {
        return uploadedImageMapper.selectUploadedImageList(uploadedImage);
    }

    /**
     * 新增用户图片信息
     *
     * @param uploadedImage 用户图片信息
     * @return 结果
     */
    @Override
    public int insertUploadedImage(UploadedImage uploadedImage)
    {
        return uploadedImageMapper.insertUploadedImage(uploadedImage);
    }

    /**
     * 修改用户图片信息
     *
     * @param uploadedImage 用户图片信息
     * @return 结果
     */
    @Override
    public int updateUploadedImage(UploadedImage uploadedImage)
    {
        return uploadedImageMapper.updateUploadedImage(uploadedImage);
    }

    /**
     * 批量删除用户图片信息
     *
     * @param ids 需要删除的用户图片信息主键
     * @return 结果
     */
    @Override
    public int deleteUploadedImageByIds(Long[] ids)
    {
        return uploadedImageMapper.deleteUploadedImageByIds(ids);
    }

    /**
     * 删除用户图片信息信息
     *
     * @param id 用户图片信息主键
     * @return 结果
     */
    @Override
    public int deleteUploadedImageById(Long id)
    {
        return uploadedImageMapper.deleteUploadedImageById(id);
    }
}
