package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.mapper.KpiSettingMapper;
import com.meiye.api.domain.KpiSetting;
import com.meiye.api.service.KpiSettingService;
@Service
public class KpiSettingServiceImpl
        extends ServiceImpl<KpiSettingMapper, KpiSetting>
        implements KpiSettingService{
    @Autowired
    private KpiSettingMapper kpiSettingMapper;

    /**
     * 查询KPI设置
     *
     * @param id KPI设置主键
     * @return KPI设置
     */
    @Override
    public KpiSetting selectKpiSettingById(Long id)
    {
        return kpiSettingMapper.selectKpiSettingById(id);
    }

    /**
     * 查询KPI设置列表
     *
     * @param kpiSetting KPI设置
     * @return KPI设置
     */
    @Override
    public List<KpiSetting> selectKpiSettingList(KpiSetting kpiSetting)
    {
        return kpiSettingMapper.selectKpiSettingList(kpiSetting);
    }

    /**
     * 新增KPI设置
     *
     * @param kpiSetting KPI设置
     * @return 结果
     */
    @Override
    public int insertKpiSetting(KpiSetting kpiSetting)
    {
        return kpiSettingMapper.insertKpiSetting(kpiSetting);
    }

    /**
     * 修改KPI设置
     *
     * @param kpiSetting KPI设置
     * @return 结果
     */
    @Override
    public int updateKpiSetting(KpiSetting kpiSetting)
    {
        return kpiSettingMapper.updateKpiSetting(kpiSetting);
    }

    /**
     * 批量删除KPI设置
     *
     * @param ids 需要删除的KPI设置主键
     * @return 结果
     */
    @Override
    public int deleteKpiSettingByIds(Long[] ids)
    {
        return kpiSettingMapper.deleteKpiSettingByIds(ids);
    }

    /**
     * 删除KPI设置信息
     *
     * @param id KPI设置主键
     * @return 结果
     */
    @Override
    public int deleteKpiSettingById(Long id)
    {
        return kpiSettingMapper.deleteKpiSettingById(id);
    }

}
