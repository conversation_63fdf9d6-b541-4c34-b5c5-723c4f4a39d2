package com.meiye.api.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.domain.ServiceScript;

/**
 * 标准话术Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ServiceScriptService extends IService<ServiceScript>
{
    /**
     * 查询标准话术
     * 
     * @param id 标准话术主键
     * @return 标准话术
     */
    public ServiceScript selectServiceScriptById(Long id);

    /**
     * 查询标准话术列表
     * 
     * @param serviceScript 标准话术
     * @return 标准话术集合
     */
    public List<ServiceScript> selectServiceScriptList(ServiceScript serviceScript);

    /**
     * 新增标准话术
     * 
     * @param serviceScript 标准话术
     * @return 结果
     */
    public int insertServiceScript(ServiceScript serviceScript);

    /**
     * 修改标准话术
     * 
     * @param serviceScript 标准话术
     * @return 结果
     */
    public int updateServiceScript(ServiceScript serviceScript);

    /**
     * 批量删除标准话术
     * 
     * @param ids 需要删除的标准话术主键集合
     * @return 结果
     */
    public int deleteServiceScriptByIds(Long[] ids);

    /**
     * 删除标准话术信息
     * 
     * @param id 标准话术主键
     * @return 结果
     */
    public int deleteServiceScriptById(Long id);
}
