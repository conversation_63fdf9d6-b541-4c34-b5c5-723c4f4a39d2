package com.meiye.api.service;

import com.meiye.api.domain.KpiMetric;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface KpiMetricService extends IService<KpiMetric>{

    /**
     * 查询KPI指标
     *
     * @param id KPI指标主键
     * @return KPI指标
     */
    public KpiMetric selectKpiMetricById(Long id);

    /**
     * 查询KPI指标列表
     *
     * @param kpiMetric KPI指标
     * @return KPI指标集合
     */
    public List<KpiMetric> selectKpiMetricList(KpiMetric kpiMetric);

    /**
     * 新增KPI指标
     *
     * @param kpiMetric KPI指标
     * @return 结果
     */
    public int insertKpiMetric(KpiMetric kpiMetric);

    /**
     * 修改KPI指标
     *
     * @param kpiMetric KPI指标
     * @return 结果
     */
    public int updateKpiMetric(KpiMetric kpiMetric);

    /**
     * 批量删除KPI指标
     *
     * @param ids 需要删除的KPI指标主键集合
     * @return 结果
     */
    public int deleteKpiMetricByIds(Long[] ids);

    /**
     * 删除KPI指标信息
     *
     * @param id KPI指标主键
     * @return 结果
     */
    public int deleteKpiMetricById(Long id);

}
