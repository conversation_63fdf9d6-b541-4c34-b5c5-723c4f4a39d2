package com.meiye.api.service;

import com.meiye.api.domain.StoreProduct;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.query.StoreProductQuery;
import com.meiye.api.vo.StoreProductVO;

import java.util.List;

public interface StoreProductService extends IService<StoreProduct>{
    /**
     * 查询店面实际销售物
     *
     * @param id 店面实际销售物主键
     * @return 店面实际销售物
     */
    public StoreProduct selectStoreProductById(Long id);

    /**
     * 查询店面实际销售物列表
     *
     * @param storeProduct 店面实际销售物
     * @return 店面实际销售物集合
     */
    public List<StoreProduct> selectStoreProductList(StoreProduct storeProduct);

    /**
     * 新增店面实际销售物
     *
     * @param storeProduct 店面实际销售物
     * @return 结果
     */
    public int insertStoreProduct(StoreProduct storeProduct);

    /**
     * 修改店面实际销售物
     *
     * @param storeProduct 店面实际销售物
     * @return 结果
     */
    public int updateStoreProduct(StoreProduct storeProduct);

    /**
     * 批量删除店面实际销售物
     *
     * @param ids 需要删除的店面实际销售物主键集合
     * @return 结果
     */
    public int deleteStoreProductByIds(Long[] ids);

    /**
     * 删除店面实际销售物信息
     *
     * @param id 店面实际销售物主键
     * @return 结果
     */
    public int deleteStoreProductById(Long id);

    /**
     * 删除店面实际销售物信息
     *
     * @param id 店面实际销售物主键
     * @return 结果
     */
    public int deleteStoreProductByPId(Long id);

    List<StoreProductVO> queryStoreProductList(StoreProductQuery storeProductQuery);
}
