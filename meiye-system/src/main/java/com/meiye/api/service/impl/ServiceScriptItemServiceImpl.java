package com.meiye.api.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.ServiceScriptItemMapper;
import com.meiye.api.domain.ServiceScriptItem;
import com.meiye.api.service.ServiceScriptItemService;
import com.meiye.common.utils.SecurityUtils;

/**
 * 标准话术单元Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class ServiceScriptItemServiceImpl
        extends ServiceImpl<ServiceScriptItemMapper, ServiceScriptItem>
        implements ServiceScriptItemService
{
    @Autowired
    private ServiceScriptItemMapper serviceScriptItemMapper;

    /**
     * 查询标准话术单元
     * 
     * @param id 标准话术单元主键
     * @return 标准话术单元
     */
    @Override
    public ServiceScriptItem selectServiceScriptItemById(Long id)
    {
        return serviceScriptItemMapper.selectServiceScriptItemById(id);
    }

    /**
     * 查询标准话术单元列表
     * 
     * @param serviceScriptItem 标准话术单元
     * @return 标准话术单元
     */
    @Override
    public List<ServiceScriptItem> selectServiceScriptItemList(ServiceScriptItem serviceScriptItem)
    {
        return serviceScriptItemMapper.selectServiceScriptItemList(serviceScriptItem);
    }

    /**
     * 新增标准话术单元
     * 
     * @param serviceScriptItem 标准话术单元
     * @return 结果
     */
    @Override
    public int insertServiceScriptItem(ServiceScriptItem serviceScriptItem)
    {
        return serviceScriptItemMapper.insertServiceScriptItem(serviceScriptItem);
    }

    /**
     * 修改标准话术单元
     * 
     * @param serviceScriptItem 标准话术单元
     * @return 结果
     */
    @Override
    public int updateServiceScriptItem(ServiceScriptItem serviceScriptItem)
    {
        return serviceScriptItemMapper.updateServiceScriptItem(serviceScriptItem);
    }

    /**
     * 批量删除标准话术单元
     * 
     * @param ids 需要删除的标准话术单元主键
     * @return 结果
     */
    @Override
    public int deleteServiceScriptItemByIds(Long[] ids)
    {
        return serviceScriptItemMapper.deleteServiceScriptItemByIds(ids);
    }

    /**
     * 删除标准话术单元信息
     * 
     * @param id 标准话术单元主键
     * @return 结果
     */
    @Override
    public int deleteServiceScriptItemById(Long id)
    {
        return serviceScriptItemMapper.deleteServiceScriptItemById(id);
    }
    
    /**
     * 获取主店面ID
     *
     * @return 主店面ID
     */
    @Override
    public Long getStoreRootId() {
        return serviceScriptItemMapper.getStoreRootId();
    }
    
    /**
     * 根据店面名称获取店面ID
     *
     * @param storeName 店面名称
     * @return 店面ID
     */
    @Override
    public Long getStoreIdByName(String storeName) {
        return serviceScriptItemMapper.getStoreIdByName(storeName);
    }
}