package com.meiye.api.service;

import java.util.List;
import com.meiye.api.domain.RecordingDeviceEmployeeLog;

/**
 * 录音设备分配记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface RecordingDeviceEmployeeLogService
{
    /**
     * 查询录音设备分配记录
     *
     * @param id 录音设备分配记录主键
     * @return 录音设备分配记录
     */
    public RecordingDeviceEmployeeLog selectRecordingDeviceEmployeeLogById(Long id);

    /**
     * 查询录音设备分配记录列表
     *
     * @param recordingDeviceEmployeeLog 录音设备分配记录
     * @return 录音设备分配记录集合
     */
    public List<RecordingDeviceEmployeeLog> selectRecordingDeviceEmployeeLogList(RecordingDeviceEmployeeLog recordingDeviceEmployeeLog);

    /**
     * 新增录音设备分配记录
     *
     * @param recordingDeviceEmployeeLog 录音设备分配记录
     * @return 结果
     */
    public int insertRecordingDeviceEmployeeLog(RecordingDeviceEmployeeLog recordingDeviceEmployeeLog);

    /**
     * 修改录音设备分配记录
     *
     * @param recordingDeviceEmployeeLog 录音设备分配记录
     * @return 结果
     */
    public int updateRecordingDeviceEmployeeLog(RecordingDeviceEmployeeLog recordingDeviceEmployeeLog);

    /**
     * 批量删除录音设备分配记录
     *
     * @param ids 需要删除的录音设备分配记录主键集合
     * @return 结果
     */
    public int deleteRecordingDeviceEmployeeLogByIds(Long[] ids);

    /**
     * 删除录音设备分配记录信息
     *
     * @param id 录音设备分配记录主键
     * @return 结果
     */
    public int deleteRecordingDeviceEmployeeLogById(Long id);
}
