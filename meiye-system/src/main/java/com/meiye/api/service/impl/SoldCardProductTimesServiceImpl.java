package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.mapper.SoldCardProductTimesMapper;
import com.meiye.api.domain.SoldCardProductTimes;
import com.meiye.api.service.SoldCardProductTimesService;
@Service
public class SoldCardProductTimesServiceImpl extends ServiceImpl<SoldCardProductTimesMapper, SoldCardProductTimes> implements SoldCardProductTimesService{

}
