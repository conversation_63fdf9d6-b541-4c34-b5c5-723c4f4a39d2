package com.meiye.api.service;

import java.util.List;
import com.meiye.api.domain.ConsumptionCard;

/**
 * 消费卡补充定义Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface ConsumptionCardService
{
    /**
     * 查询消费卡补充定义
     * 
     * @param id 消费卡补充定义主键
     * @return 消费卡补充定义
     */
    public ConsumptionCard selectConsumptionCardById(Long id);

    /**
     * 查询消费卡补充定义列表
     * 
     * @param consumptionCard 消费卡补充定义
     * @return 消费卡补充定义集合
     */
    public List<ConsumptionCard> selectConsumptionCardList(ConsumptionCard consumptionCard);

    /**
     * 新增消费卡补充定义
     * 
     * @param consumptionCard 消费卡补充定义
     * @return 结果
     */
    public int insertConsumptionCard(ConsumptionCard consumptionCard);

    /**
     * 修改消费卡补充定义
     * 
     * @param consumptionCard 消费卡补充定义
     * @return 结果
     */
    public int updateConsumptionCard(ConsumptionCard consumptionCard);

    /**
     * 批量删除消费卡补充定义
     * 
     * @param ids 需要删除的消费卡补充定义主键集合
     * @return 结果
     */
    public int deleteConsumptionCardByIds(Long[] ids);

    /**
     * 删除消费卡补充定义信息
     * 
     * @param id 消费卡补充定义主键
     * @return 结果
     */
    public int deleteConsumptionCardById(Long id);
}
