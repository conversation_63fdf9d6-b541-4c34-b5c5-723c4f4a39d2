package com.meiye.api.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meiye.api.domain.*;
import com.meiye.api.mapper.*;
import com.meiye.api.query.StoreProductQuery;
import com.meiye.api.service.ConsumptionCardService;
import com.meiye.api.vo.OptionVo;
import com.meiye.api.vo.StoreNoticeVo;
import com.meiye.common.core.domain.entity.SysUser;
import com.meiye.common.utils.SecurityUtils;
import com.meiye.system.mapper.SysUserMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.service.ProductService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.meiye.common.utils.SecurityUtils.*;

/**
 * 主店面销售物定义Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class ProductServiceImpl implements ProductService
{
    @Resource
    private ProductMapper productMapper;
    @Autowired
    private ProductCategoryMapper productCategoryMapper;
    @Autowired
    private StoreProductLogMapper storeProductLogMapper;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private StoreProductMapper storeProductMapper;
    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private ConsumptionCardService consumptionCardService;
    @Autowired
    private ConsumptionCardMapper consumptionCardMapper;
    @Autowired
    private ProductItemMapper productItemMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询主店面销售物定义
     *
     * @param id 主店面销售物定义主键
     * @return 主店面销售物定义
     */
    @Override
    public Product selectProductById(Long id) {
        Product product = productMapper.selectProductById(id);
        if (product == null) {
            return null;
        }

        ConsumptionCard consumptionCard = consumptionCardMapper.selectConsumptionCardByPId(product.getId());
        product.setConsumptionCard(consumptionCard);

        // 查询关联的产品项目及其子产品名称
        List<ProductItem> productItems = productItemMapper.selectProductItemsWithSubProductNames(id);
        product.setServiceList(new ArrayList<>());
        product.setProjectList(new ArrayList<>());

        for (ProductItem productItem : productItems) {
            // 直接通过子产品ID查询分类信息
            Product product1=productMapper.selectProductById(productItem.getSubProductId());
            ProductCategory productCategory = productCategoryMapper.selectProductCategoryById(product1.getCategoryId());

            if (productCategory == null) {
                continue;
            }

            System.out.println("产品ID: " + productItem.getSubProductId() + ", 分类类型: " + productCategory.getType());

            if ("service".equals(productCategory.getType())) {
                product.getServiceList().add(productItem);
            } else if ("type".equals(productCategory.getType())) {
                product.getProjectList().add(productItem);
            } else {
                System.out.println("未知分类类型: " + productCategory.getType() + ", 产品ID: " + productItem.getSubProductId());
            }
        }

        return product;
    }

    /**
     * 查询主店面销售物定义列表
     *
     * @param product 主店面销售物定义
     * @return 主店面销售物定义
     */
    @Override
    public List<Product> selectProductList(Product product)
    {
        return productMapper.selectProductList(product);
    }

    /**
     * 查询主店面销售物定义列表
     *
     * @param product 主店面销售物定义
     * @return 主店面销售物定义
     */
    @Override
    public List<Product> selectServiceList(Product product)
    {
        List<Product> resultList = new ArrayList<>();
        if(SecurityUtils.isAdmin(getUserId()))
        {
            return productMapper.selectServiceList(product);
        }
        else if(SecurityUtils.isOperator(getUserId())){
            return productMapper.selectServiceList(product);
        }
        else {
            //BaseController的getStoreId里获取店铺id，按店铺查product表的内容
            Long[] storeId= new Long[]{product.getRootStoreId()};
            product.setStoreIds(storeId);
            productMapper.selectServiceList(product);
            }
return resultList;
        }

    /**
     * 查询主店面销售物定义列表
     *
     * @param product 主店面销售物定义
     * @return 主店面销售物定义
     */
    @Override
    public List<Product> selectCardList(Product product)
    {
        return productMapper.selectCardList(product);
    }

    /**
     * 新增主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    @Override
    @Transactional
    public int insertService(Product product) {
        // 1. 创建并插入 ProductCategory
        ProductCategory category = new ProductCategory();
        StoreProductLog log = new StoreProductLog();

        SysUser user = userMapper.selectUserById(getUserId());
        Store store = storeMapper.selectById(user.getStoreId());
        product.setRootStoreId(store.getId());

        List<Store> sonStores = storeMapper.selectSonsById(store.getId());

        category.setType("service");
        category.setName(product.getName());
        category.setParentId(product.getCategoryCode());
        category.setRootStoreId(product.getRootStoreId());
        category.setStatus("1");
        if(product.getStatus()!=null)
            category.setStatus("enabled".equals(product.getStatus()) ? "0" : "1");
        productCategoryMapper.insertProductCategory(category);

        // 2. 将新创建的 categoryId 设置到 Product
        product.setCategoryId(category.getId()); // 关键：设置关联ID
        int mainResult = productMapper.insertProduct(product);

        Date now = new Date();
        log.setType("create");
        log.setCreatedAt(now);
        log.setCreatedByEmployeeId(getUserId());
        log.setStoreId(user.getStoreId());
        log.setStoreProductId(product.getId());
        try {
            // 序列化新对象作为更新后状态
            String currentJson = objectMapper.writeValueAsString(product);
            log.setCurrentJson(currentJson);
        } catch (JsonProcessingException e) {
            // 统一错误处理
            String errorMsg = "序列化失败: " + e.getMessage();
            log.setCurrentJson("{\"error\":\"" + errorMsg + "\"}");
            System.out.println("产品更新日志序列化异常: {}" + errorMsg);
        }
        storeProductLogMapper.insertStoreProductLog(log);

        // 3. 使用productId创建并插入StoreProduct
        StoreProduct mainStoreProduct = new StoreProduct();
        mainStoreProduct.setStoreId(product.getRootStoreId());
        mainStoreProduct.setSalePrice(product.getPrice());
        if(product.getStatus()!=null)
            mainStoreProduct.setIsAvailable(product.getStatus().equals("enabled"));
        mainStoreProduct.setIsAvailable(false);
        mainStoreProduct.setProductId(product.getId());
        storeProductMapper.insertStoreProduct(mainStoreProduct);

        if (sonStores != null && !sonStores.isEmpty()) {
            for (Store sonStore : sonStores) {
                if (sonStore.getId().equals(product.getRootStoreId()))
                    continue;
                // 创建新的StoreProduct对象（子店铺）
                StoreProduct sonStoreProduct = new StoreProduct();
                sonStoreProduct.setStoreId(sonStore.getId());
                sonStoreProduct.setSalePrice(product.getPrice());
                sonStoreProduct.setIsAvailable(mainStoreProduct.getIsAvailable());
                sonStoreProduct.setProductId(product.getId());

                storeProductMapper.insertStoreProduct(sonStoreProduct);
            }
        }
            return mainResult;
    }

    /**
     * 新增主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    @Override
    @Transactional
    public int insertProduct(Product product) {
        // 1. 创建并插入 ProductCategory
        ProductCategory category = new ProductCategory();
        StoreProductLog log=new StoreProductLog();

        SysUser user = userMapper.selectUserById(getUserId());
        Store store = storeMapper.selectById(user.getStoreId());
        product.setRootStoreId(store.getId());
        System.out.println(store.getId());
        List<Store> sonStores = storeMapper.selectSonsById(store.getId());

        category.setType("goods");
        category.setName(product.getName());
        category.setParentId(product.getCategoryCode());
        category.setRootStoreId(product.getRootStoreId());
        category.setStatus("1");
        if(product.getStatus()!=null)
            category.setStatus("enabled".equals(product.getStatus()) ? "0" : "1");
        productCategoryMapper.insertProductCategory(category);

        // 2. 将新创建的 categoryId 设置到 Product
        product.setCategoryId(category.getId()); // 关键：设置关联ID
        int mainResult = productMapper.insertProduct(product);

        Date now=new Date();
        log.setType("create");
        log.setCreatedAt(now);
        log.setCreatedByEmployeeId(getUserId());
        log.setStoreId(user.getStoreId());
        log.setStoreProductId(product.getId());
        try {
            // 序列化新对象作为更新后状态
            String currentJson = objectMapper.writeValueAsString(product);
            log.setCurrentJson(currentJson);
        } catch (JsonProcessingException e) {
            // 统一错误处理
            String errorMsg = "序列化失败: " + e.getMessage();
            log.setCurrentJson("{\"error\":\"" + errorMsg + "\"}");
            System.out.println("产品更新日志序列化异常: {}"+errorMsg);
        }
        storeProductLogMapper.insertStoreProductLog(log);

        // 3. 使用productId创建并插入StoreProduct
        StoreProduct mainStoreProduct = new StoreProduct();
        mainStoreProduct.setStoreId(product.getRootStoreId());
        mainStoreProduct.setSalePrice(product.getPrice());
        mainStoreProduct.setIsAvailable(false);
        if(product.getStatus()!=null)
            mainStoreProduct.setIsAvailable(product.getStatus().equals("enabled"));
        mainStoreProduct.setProductId(product.getId());
        storeProductMapper.insertStoreProduct(mainStoreProduct);

        if (sonStores != null && !sonStores.isEmpty()) {
            for (Store sonStore : sonStores) {
                if (sonStore.getId().equals(product.getRootStoreId()))
                    continue;
                // 创建新的StoreProduct对象（子店铺）
                StoreProduct sonStoreProduct = new StoreProduct();
                sonStoreProduct.setStoreId(sonStore.getId());
                sonStoreProduct.setSalePrice(product.getPrice());
                sonStoreProduct.setIsAvailable(mainStoreProduct.getIsAvailable());
                sonStoreProduct.setProductId(product.getId());

                storeProductMapper.insertStoreProduct(sonStoreProduct);
            }
        }
        return mainResult;
    }

    /**
     * 新增主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    @Override
    @Transactional
    public int insertCard(Product product) {
        try {
            // 1. 创建并插入 ProductCategory
            ProductCategory category = new ProductCategory();
            StoreProductLog log = new StoreProductLog();

            SysUser user = userMapper.selectUserById(getUserId());
            Store store = storeMapper.selectById(user.getStoreId());
            product.setRootStoreId(store.getId());
            List<Store> sonStores = storeMapper.selectSonsById(store.getId());

            category.setType("card");
            category.setName(product.getName());
            category.setParentId(product.getCategoryCode());
            category.setRootStoreId(product.getRootStoreId());
            category.setStatus("1");
            if(product.getStatus()!=null)
                category.setStatus("enabled".equals(product.getStatus()) ? "0" : "1");

            // 插入分类并获取自增ID
            productCategoryMapper.insertProductCategory(category);
            Long newCategoryId = category.getId();

            // 2. 设置product的categoryCode为新分类的ID
            System.out.println("categoryCode"+product.getCategoryCode());
            product.setCategoryId(newCategoryId);
            int mainResult = productMapper.insertProduct(product);
// 3. 创建消费卡记录
            ConsumptionCard consumptionCard = new ConsumptionCard();
            BeanUtils.copyProperties(product, consumptionCard);
            consumptionCard.setProductId(product.getId());
//  手动设置列表
            consumptionCard.setServiceList(product.getServiceList());
            consumptionCard.setProjectList(product.getProjectList());
//  设置消费卡特有字段（从 product 中取）
            consumptionCard.setType(product.getType());
// 4. 插入消费卡
            consumptionCardService.insertConsumptionCard(consumptionCard);

            Date now = new Date();
            log.setType("create");
            log.setCreatedAt(now);
            log.setCreatedByEmployeeId(getUserId());
            log.setStoreId(user.getStoreId());
            log.setStoreProductId(product.getId());

            try {
                String currentJson = objectMapper.writeValueAsString(product);
                log.setCurrentJson(currentJson);
            } catch (JsonProcessingException e) {
                String errorMsg = "序列化失败: " + e.getMessage();
                log.setCurrentJson("{\"error\":\"" + errorMsg + "\"}");
                System.out.println("产品更新日志序列化异常: " + errorMsg);
            }

            storeProductLogMapper.insertStoreProductLog(log);

            // 5. 使用productId创建并插入StoreProduct
            StoreProduct mainStoreProduct = new StoreProduct();
            mainStoreProduct.setStoreId(product.getRootStoreId());
            mainStoreProduct.setSalePrice(product.getPrice());
            mainStoreProduct.setIsAvailable(false);
            if(product.getStatus()!=null)
                mainStoreProduct.setIsAvailable(product.getStatus().equals("enabled"));
            mainStoreProduct.setProductId(product.getId());
            storeProductMapper.insertStoreProduct(mainStoreProduct);

            if (sonStores != null) {
                for (Store sonStore : sonStores) {
                    if (sonStore.getId().equals(product.getRootStoreId()))
                        continue;
                    StoreProduct sonStoreProduct = new StoreProduct();
                    BeanUtils.copyProperties(mainStoreProduct, sonStoreProduct, "id");
                    sonStoreProduct.setStoreId(sonStore.getId());
                    storeProductMapper.insertStoreProduct(sonStoreProduct);
                }
            }
            return mainResult;
        } catch (Exception e) {
            System.out.println("插入卡片时发生异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 修改主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    @Override
    @Transactional
    public int updateProduct(Product product) {
        // 获取更新前的原始产品数据
        Product originalProduct = productMapper.selectProductById(product.getId());
        //获取店铺id
        SysUser sysUser= sysUserMapper.selectUserById(getUserId());

        StoreProductLog log = new StoreProductLog();
        Date now = new Date();
        log.setType("update");
        log.setCreatedByEmployeeId(getUserId());
        log.setCreatedAt(now);
        log.setStoreId(sysUser.getStoreId());
        log.setStoreProductId(product.getId());

        try {
            // 序列化原始对象作为更新前状态
            String originJson = objectMapper.writeValueAsString(originalProduct);
            log.setOriginJson(originJson);
            // 序列化新对象作为更新后状态
            String currentJson = objectMapper.writeValueAsString(product);
            log.setCurrentJson(currentJson);
        } catch (JsonProcessingException e) {
            // 统一错误处理
            String errorMsg = "序列化失败: " + e.getMessage();
            log.setOriginJson("{\"error\":\"" + errorMsg + "\"}");
            log.setCurrentJson("{\"error\":\"" + errorMsg + "\"}");
            System.out.println("产品更新日志序列化异常: {}" + errorMsg);
        }

        storeProductLogMapper.insertStoreProductLog(log);

        SysUser user = userMapper.selectUserById(getUserId());
        Store store = storeMapper.selectById(user.getStoreId());
        product.setRootStoreId(store.getId());
        Store type = storeMapper.selectTypeById(store.getId());

        // 根据productId和storeId获取已存在的StoreProduct记录
        StoreProduct query = new StoreProduct();
        query.setStoreId(store.getId());
        query.setProductId(product.getId());
        List<StoreProduct> storeProducts = storeProductMapper.selectStoreProductList(query);

        if (storeProducts.isEmpty()) {
            // 如果没有找到StoreProduct记录，创建一个新的
            StoreProduct newStoreProduct = new StoreProduct();
            newStoreProduct.setStoreId(store.getId());
            newStoreProduct.setProductId(product.getId());
            newStoreProduct.setSalePrice(product.getPrice());

            // 设置状态
            if ("enabled".equals(product.getStatus())) {
                newStoreProduct.setIsAvailable(true);
            } else {
                newStoreProduct.setIsAvailable(false);
            }

            storeProductMapper.insertStoreProduct(newStoreProduct);
        } else {
            // 获取第一个匹配的记录
            StoreProduct storeProduct = storeProducts.get(0);

            // 设置要更新的StoreProduct的价格
            storeProduct.setSalePrice(product.getPrice());
            System.out.println("getStatus"+product.getStatus());
            if ("enabled".equals(product.getStatus()))
                storeProduct.setIsAvailable(true);
            else
                storeProduct.setIsAvailable(false);
            System.out.println("setIsAvailable"+storeProduct.getIsAvailable());
            // 更新StoreProduct
            int result = storeProductMapper.updateStoreProduct(storeProduct);
        }

        ProductCategory category = productCategoryMapper.selectProductCategoryById(product.getCategoryId());

        // 如果是主店铺，同时更新Product表
        if ("main".equals(type.getType())) {
            productMapper.updateProduct(product);

            category.setParentId(product.getCategoryCode());
            if ("enabled".equals(product.getStatus()))
                category.setStatus("0");
            else if ("disabled".equals(product.getStatus()))
                category.setStatus("1");
            category.setName(product.getName());
            category.setParentId(product.getCategoryCode());
            System.out.println(category);
            productCategoryMapper.updateProductCategoryList(category);
        }
        if("card".equals(category.getType())) {
            ConsumptionCard consumptionCard=consumptionCardMapper.selectConsumptionCardByPId(product.getId());
            BeanUtils.copyProperties(product,consumptionCard);
            consumptionCardService.updateConsumptionCard(consumptionCard);
        }
        return 1;
    }
    /**
     * 删除主店面销售物定义信息
     *
     * @param id 主店面销售物定义主键
     * @return 结果
     */
    @Override
    public int deleteProductById(Long id)
    {
        Product product=selectProductById(id);
        StoreProductLog log=new StoreProductLog();
        Long categoryId= product.getCategoryId();
        productCategoryMapper.deleteProductCategoryById(categoryId);

        Date now=new Date();
        log.setType("delete");
        log.setStoreProductId(product.getId());
        log.setCreatedAt(now);
        log.setCreatedByEmployeeId(getUserId());
        // 序列化product对象为JSON字符串
        try {
            String originJson = objectMapper.writeValueAsString(product);
            log.setOriginJson(originJson);
        } catch (JsonProcessingException e) {
            // 异常处理：记录错误并使用备用值
            System.out.println("序列化产品失败: {}"+ e.getMessage());
            log.setOriginJson("{\"error\":\"serialization_failed\"}");
        }
        log.setStoreId(product.getRootStoreId());
        storeProductLogMapper.insertStoreProductLog(log);

        storeProductMapper.deleteStoreProductByPId(product.getId());
        return productMapper.deleteProductById(id);
    }

    @Override
    public List<Product> selectProductOption(Product product) {
        return productMapper.selectProductOption(product);
    }

    @Override
    public List<OptionVo> selectOptionSp(StoreProductQuery storeProductQuery) {
        return productMapper.selectOptionSp(storeProductQuery);
    }
}
