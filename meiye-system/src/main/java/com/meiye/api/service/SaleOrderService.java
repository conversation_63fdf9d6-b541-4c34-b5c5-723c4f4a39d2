package com.meiye.api.service;

import com.meiye.api.domain.SaleOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.dto.PendingOrderDto;
import com.meiye.api.dto.SaleOrderDTO;
import com.meiye.common.core.domain.AjaxResult;

public interface SaleOrderService extends IService<SaleOrder>{


    void addSaleOrder(SaleOrderDTO dto);


    AjaxResult addSaleOrderNew(PendingOrderDto dto);
}
