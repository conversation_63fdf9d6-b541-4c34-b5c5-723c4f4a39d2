package com.meiye.api.service;

import com.meiye.api.domain.ProductCategory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.domain.Store;

import java.util.List;

public interface ProductCategoryService extends IService<ProductCategory>{
    /**
     * 查询销售物分类列表
     *
     * @param productCategory 图片信息
     * @return 图片集合
     */
    List<ProductCategory> selectProductCategoryList(ProductCategory productCategory);

    /**
     * 新增销售物分类
     *
     * @param productCategory 销售物分类
     * @return 结果
     */
    public int insertProductCategory(ProductCategory productCategory);

    /**
     * 修改销售物分类列表
     *
     * @param productCategory 销售物分类信息
     * @return 销售物分类集合
     */
    public int updateProductCategoryList(ProductCategory productCategory);

    /**
     * 查询销售物分类
     *
     * @param id 销售物分类主键
     * @return 销售物分类
     */
    public ProductCategory selectProductCategoryById(Long id);

    /**
     * 删除销售物分类信息
     *
     * @param id 销售物分类主键
     * @return 结果
     */
    public int deleteProductCategoryById(Long id);

    /**
     * 批量删除销售物分类
     *
     * @param ids 需要删除的销售物分类主键集合
     * @return 结果
     */
    public int deleteProductCategoryByIds(Long[] ids);
}
