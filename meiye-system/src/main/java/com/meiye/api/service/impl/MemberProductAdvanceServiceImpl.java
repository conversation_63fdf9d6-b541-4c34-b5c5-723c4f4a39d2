package com.meiye.api.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.MemberLevel;
import com.meiye.api.domain.MemberProductAdvance;
import com.meiye.api.mapper.MemberLevelMapper;
import com.meiye.api.mapper.MemberProductAdvanceMapper;
import com.meiye.api.service.MemberLevelService;
import com.meiye.api.service.MemberProductAdvanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 为会员推荐过的项目记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class MemberProductAdvanceServiceImpl
        extends ServiceImpl<MemberProductAdvanceMapper, MemberProductAdvance>
        implements MemberProductAdvanceService
{
    @Autowired
    private MemberProductAdvanceMapper memberProductAdvanceMapper;

    /**
     * 查询为会员推荐过的项目记录
     * 
     * @param id 为会员推荐过的项目记录主键
     * @return 为会员推荐过的项目记录
     */
    @Override
    public MemberProductAdvance selectMemberProductAdvanceById(Long id)
    {
        return memberProductAdvanceMapper.selectMemberProductAdvanceById(id);
    }

    /**
     * 查询为会员推荐过的项目记录列表
     * 
     * @param memberProductAdvance 为会员推荐过的项目记录
     * @return 为会员推荐过的项目记录
     */
    @Override
    public List<MemberProductAdvance> selectMemberProductAdvanceList(MemberProductAdvance memberProductAdvance)
    {
        return memberProductAdvanceMapper.selectMemberProductAdvanceList(memberProductAdvance);
    }

    /**
     * 新增为会员推荐过的项目记录
     * 
     * @param memberProductAdvance 为会员推荐过的项目记录
     * @return 结果
     */
    @Override
    public int insertMemberProductAdvance(MemberProductAdvance memberProductAdvance)
    {
        return memberProductAdvanceMapper.insertMemberProductAdvance(memberProductAdvance);
    }

    /**
     * 修改为会员推荐过的项目记录
     * 
     * @param memberProductAdvance 为会员推荐过的项目记录
     * @return 结果
     */
    @Override
    public int updateMemberProductAdvance(MemberProductAdvance memberProductAdvance)
    {
        return memberProductAdvanceMapper.updateMemberProductAdvance(memberProductAdvance);
    }

    /**
     * 批量删除为会员推荐过的项目记录
     * 
     * @param ids 需要删除的为会员推荐过的项目记录主键
     * @return 结果
     */
    @Override
    public int deleteMemberProductAdvanceByIds(Long[] ids)
    {
        return memberProductAdvanceMapper.deleteMemberProductAdvanceByIds(ids);
    }

    /**
     * 删除为会员推荐过的项目记录信息
     * 
     * @param id 为会员推荐过的项目记录主键
     * @return 结果
     */
    @Override
    public int deleteMemberProductAdvanceById(Long id)
    {
        return memberProductAdvanceMapper.deleteMemberProductAdvanceById(id);
    }
}
