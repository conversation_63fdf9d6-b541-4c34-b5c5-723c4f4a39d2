package com.meiye.api.service;

import com.meiye.api.domain.SoldCard;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface SoldCardService
        extends IService<SoldCard>
{

    /**
     * 查询已售消费卡
     *
     * @param id 已售消费卡主键
     * @return 已售消费卡
     */
    public SoldCard selectSoldCardById(Long id);

    /**
     * 查询已售消费卡列表
     *
     * @param soldCard 已售消费卡
     * @return 已售消费卡集合
     */
    public List<SoldCard> selectSoldCardList(SoldCard soldCard);

    /**
     * 新增已售消费卡
     *
     * @param soldCard 已售消费卡
     * @return 结果
     */
    public int insertSoldCard(SoldCard soldCard);

    /**
     * 修改已售消费卡
     *
     * @param soldCard 已售消费卡
     * @return 结果
     */
    public int updateSoldCard(SoldCard soldCard);

    /**
     * 批量删除已售消费卡
     *
     * @param ids 需要删除的已售消费卡主键集合
     * @return 结果
     */
    public int deleteSoldCardByIds(Long[] ids);

    /**
     * 删除已售消费卡信息
     *
     * @param id 已售消费卡主键
     * @return 结果
     */
    public int deleteSoldCardById(Long id);
    
    /**
     * 根据店面销售物ID获取销售物名称
     *
     * @param storeProductId 店面销售物ID
     * @return 销售物名称
     */
    public String getProductNameByStoreProductId(Long storeProductId);
}