package com.meiye.api.service;

import com.meiye.api.domain.EmployeeInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.vo.EmployeeCommissionOptionVO;
import com.meiye.api.vo.EmployeeInfoVo;

import java.util.List;

public interface EmployeeInfoService extends IService<EmployeeInfo>{


    /**
     * 查询员工扩展信息
     *
     * @param id 员工扩展信息主键
     * @return 员工扩展信息
     */
    public EmployeeInfo selectEmployeeInfoById(Long id);

    /**
     * 查询员工扩展信息列表
     *
     * @param employeeInfo 员工扩展信息
     * @return 员工扩展信息集合
     */
    public List<EmployeeInfo> selectEmployeeInfoList(EmployeeInfo employeeInfo);

    /**
     * 新增员工扩展信息
     *
     * @param employeeInfo 员工扩展信息
     * @return 结果
     */
    public int insertEmployeeInfo(EmployeeInfo employeeInfo);

    /**
     * 修改员工扩展信息
     *
     * @param employeeInfo 员工扩展信息
     * @return 结果
     */
    public int updateEmployeeInfo(EmployeeInfo employeeInfo);

    /**
     * 批量删除员工扩展信息
     *
     * @param ids 需要删除的员工扩展信息主键集合
     * @return 结果
     */
    public int deleteEmployeeInfoByIds(Long[] ids);

    /**
     * 删除员工扩展信息信息
     *
     * @param id 员工扩展信息主键
     * @return 结果
     */
    public int deleteEmployeeInfoById(Long id);

    List<EmployeeInfoVo> selectEmployeeVoList(EmployeeInfoVo employeeInfo);

    EmployeeInfoVo selectEmployeeInfoVoById(Long id);

    List<EmployeeCommissionOptionVO> selectECOptions(Long storeId);
}
