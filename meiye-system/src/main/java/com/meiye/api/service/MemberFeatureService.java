package com.meiye.api.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.domain.MemberFeature;

/**
 * 会员特性Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface MemberFeatureService extends IService<MemberFeature>
{
    /**
     * 查询会员特性
     *
     * @param id 会员特性主键
     * @return 会员特性
     */
    public MemberFeature selectMemberFeatureById(Long id);

    /**
     * 查询会员特性列表
     *
     * @param memberFeature 会员特性
     * @return 会员特性集合
     */
    public List<MemberFeature> selectMemberFeatureList(MemberFeature memberFeature);

    /**
     * 新增会员特性
     *
     * @param memberFeature 会员特性
     * @return 结果
     */
    public int insertMemberFeature(MemberFeature memberFeature);

    /**
     * 修改会员特性
     *
     * @param memberFeature 会员特性
     * @return 结果
     */
    public int updateMemberFeature(MemberFeature memberFeature);

    /**
     * 批量删除会员特性
     *
     * @param ids 需要删除的会员特性主键集合
     * @return 结果
     */
    public int deleteMemberFeatureByIds(Long[] ids);

    /**
     * 删除会员特性信息
     *
     * @param id 会员特性主键
     * @return 结果
     */
    public int deleteMemberFeatureById(Long id);
}
