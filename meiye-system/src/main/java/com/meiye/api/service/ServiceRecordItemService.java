package com.meiye.api.service;

import com.meiye.api.domain.ServiceRecordItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ServiceRecordItemService
        extends IService<ServiceRecordItem>{

    /**
     * 查询服务记录的话术单元信息
     *
     * @param id 服务记录的话术单元信息主键
     * @return 服务记录的话术单元信息
     */
    public ServiceRecordItem selectServiceRecordItemById(Long id);

    /**
     * 查询服务记录的话术单元信息列表
     *
     * @param serviceRecordItem 服务记录的话术单元信息
     * @return 服务记录的话术单元信息集合
     */
    public List<ServiceRecordItem> selectServiceRecordItemList(ServiceRecordItem serviceRecordItem);

    /**
     * 新增服务记录的话术单元信息
     *
     * @param serviceRecordItem 服务记录的话术单元信息
     * @return 结果
     */
    public int insertServiceRecordItem(ServiceRecordItem serviceRecordItem);

    /**
     * 修改服务记录的话术单元信息
     *
     * @param serviceRecordItem 服务记录的话术单元信息
     * @return 结果
     */
    public int updateServiceRecordItem(ServiceRecordItem serviceRecordItem);

    /**
     * 批量删除服务记录的话术单元信息
     *
     * @param ids 需要删除的服务记录的话术单元信息主键集合
     * @return 结果
     */
    public int deleteServiceRecordItemByIds(Long[] ids);

    /**
     * 删除服务记录的话术单元信息信息
     *
     * @param id 服务记录的话术单元信息主键
     * @return 结果
     */
    public int deleteServiceRecordItemById(Long id);

}
