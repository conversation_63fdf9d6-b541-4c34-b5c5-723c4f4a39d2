package com.meiye.api.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.domain.Member;
import com.meiye.api.domain.MemberLevel;
import com.meiye.api.query.MemberQuery;
import com.meiye.api.vo.MemberVO;

/**
 * 会员Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface MemberService extends IService<Member>
{
    /**
     * 查询会员
     *
     * @param id 会员主键
     * @return 会员
     */
    public Member selectMemberById(Long id);

    /**
     * 查询会员列表
     *
     * @param member 会员
     * @return 会员集合
     */
    public List<Member> selectMemberList(Member member);

    /**
     * 查询所有会员列表（不分页）
     *
     * @return 会员集合
     */
    public List<Member> selectAllMember();

    /**
     * 获取会员详细信息（包含会员等级、最近订单、标签、推荐产品等）
     *
     * @param memberId 会员ID
     * @return 会员详细信息
     */
    Member getMemberDetailInfo(Long memberId);
    /**
     * 新增会员
     *
     * @param member 会员
     * @return 结果
     */
    public int insertMember(Member member);

    int addMember(Member member);

    /**
     * 修改会员
     *
     * @param member 会员
     * @return 结果
     */
    public int updateMember(Member member);

    /**
     * 批量删除会员
     *
     * @param ids 需要删除的会员主键集合
     * @return 结果
     */
    public int deleteMemberByIds(Long[] ids);

    /**
     * 删除会员信息
     *
     * @param id 会员主键
     * @return 结果
     */
    public int deleteMemberById(Long id);

    /**
     * 查询会员等级选项列表
     *
     * @return 会员等级集合
     */
    public List<MemberLevel> selectMemberLevelOptions();

    Long getStoreIdByName(String storeName);

    /**
     * 获取主店面ID
     *
     * @return 主店面ID
     */
    public Long getStoreRootId();
    List<MemberVO> queryMemberList(MemberQuery query);

    Integer getYearMemberNum();
}