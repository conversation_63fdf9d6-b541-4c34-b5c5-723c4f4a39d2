package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.mapper.SoldCardMapper;
import com.meiye.api.domain.SoldCard;
import com.meiye.api.service.SoldCardService;
import com.meiye.common.utils.DateUtils;
import com.meiye.api.domain.StoreProduct;
import com.meiye.api.service.StoreProductService;
import com.meiye.api.domain.Product;
import com.meiye.api.service.ProductService;
import java.util.Calendar;

@Service
public class SoldCardServiceImpl
        extends ServiceImpl<SoldCardMapper, SoldCard>
        implements SoldCardService
{
    @Autowired
    private SoldCardMapper soldCardMapper;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private ProductService productService;

    /**
     * 查询已售消费卡
     *
     * @param id 已售消费卡主键
     * @return 已售消费卡
     */
    @Override
    public SoldCard selectSoldCardById(Long id)
    {
        return soldCardMapper.selectSoldCardById(id);
    }

    /**
     * 查询已售消费卡列表
     *
     * @param soldCard 已售消费卡
     * @return 已售消费卡
     */
    @Override
    public List<SoldCard> selectSoldCardList(SoldCard soldCard)
    {
        return soldCardMapper.selectSoldCardList(soldCard);
    }

    /**
     * 新增已售消费卡
     *
     * @param soldCard 已售消费卡
     * @return 结果
     */
    @Override
    public int insertSoldCard(SoldCard soldCard)
    {
        // 自动填充注册时间
        if (soldCard.getCreatedAt() == null) {
            soldCard.setCreatedAt(DateUtils.getNowDate());
        }

        // 自动填充过期时间（注册时间两年后）
        if (soldCard.getExpireDate() == null && soldCard.getCreatedAt() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(soldCard.getCreatedAt());
            calendar.add(Calendar.YEAR, 2);
            soldCard.setExpireDate(calendar.getTime());
        }

        return soldCardMapper.insertSoldCard(soldCard);
    }

    /**
     * 修改已售消费卡
     *
     * @param soldCard 已售消费卡
     * @return 结果
     */
    @Override
    public int updateSoldCard(SoldCard soldCard)
    {
        // 查询原有记录
        SoldCard oldSoldCard = soldCardMapper.selectSoldCardById(soldCard.getId());

        // 保持原有的注册时间不变
        if (oldSoldCard != null && oldSoldCard.getCreatedAt() != null) {
            soldCard.setCreatedAt(oldSoldCard.getCreatedAt());
        } else if (soldCard.getCreatedAt() == null) {
            // 如果原有记录没有注册时间，则设置为当前时间
            soldCard.setCreatedAt(DateUtils.getNowDate());
        }

        // 如果过期时间未设置，则设置为注册时间两年后
        if (soldCard.getExpireDate() == null && soldCard.getCreatedAt() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(soldCard.getCreatedAt());
            calendar.add(Calendar.YEAR, 2);
            soldCard.setExpireDate(calendar.getTime());
        }

        return soldCardMapper.updateSoldCard(soldCard);
    }

    /**
     * 批量删除已售消费卡
     *
     * @param ids 需要删除的已售消费卡主键
     * @return 结果
     */
    @Override
    public int deleteSoldCardByIds(Long[] ids)
    {
        return soldCardMapper.deleteSoldCardByIds(ids);
    }

    /**
     * 删除已售消费卡信息
     *
     * @param id 已售消费卡主键
     * @return 结果
     */
    @Override
    public int deleteSoldCardById(Long id)
    {
        return soldCardMapper.deleteSoldCardById(id);
    }

    /**
     * 根据店面销售物ID获取销售物名称
     *
     * @param storeProductId 店面销售物ID
     * @return 销售物名称
     */
    @Override
    public String getProductNameByStoreProductId(Long storeProductId) {
        if (storeProductId == null) {
            return "未知产品";
        }

        // 先通过storeProductId查询StoreProduct
        StoreProduct storeProduct = new StoreProduct();
        if (storeProduct == null) {
            return "未知产品";
        }

        // 再通过StoreProduct中的productId查询Product
        Product product = productService.selectProductById(storeProduct.getProductId());
        if (product == null) {
            return "未知产品";
        }

        return product.getName();
    }
}