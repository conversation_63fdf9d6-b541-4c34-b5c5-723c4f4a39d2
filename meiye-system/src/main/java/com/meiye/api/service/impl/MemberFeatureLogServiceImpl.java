package com.meiye.api.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.MemberFeatureLog;
import com.meiye.api.mapper.MemberFeatureLogMapper;
import com.meiye.api.service.MemberFeatureLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 会员特性变更记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class MemberFeatureLogServiceImpl
        extends ServiceImpl<MemberFeatureLogMapper, MemberFeatureLog>
        implements MemberFeatureLogService
{
    @Autowired
    private MemberFeatureLogMapper memberFeatureLogMapper;

    /**
     * 查询会员特性变更记录
     * 
     * @param id 会员特性变更记录主键
     * @return 会员特性变更记录
     */
    @Override
    public MemberFeatureLog selectMemberFeatureLogById(Long id)
    {
        return memberFeatureLogMapper.selectMemberFeatureLogById(id);
    }

    /**
     * 查询会员特性变更记录列表
     * 
     * @param memberFeatureLog 会员特性变更记录
     * @return 会员特性变更记录
     */
    @Override
    public List<MemberFeatureLog> selectMemberFeatureLogList(MemberFeatureLog memberFeatureLog)
    {
        return memberFeatureLogMapper.selectMemberFeatureLogList(memberFeatureLog);
    }

    /**
     * 新增会员特性变更记录
     * 
     * @param memberFeatureLog 会员特性变更记录
     * @return 结果
     */
    @Override
    public int insertMemberFeatureLog(MemberFeatureLog memberFeatureLog)
    {
        return memberFeatureLogMapper.insertMemberFeatureLog(memberFeatureLog);
    }

    /**
     * 修改会员特性变更记录
     * 
     * @param memberFeatureLog 会员特性变更记录
     * @return 结果
     */
    @Override
    public int updateMemberFeatureLog(MemberFeatureLog memberFeatureLog)
    {
        return memberFeatureLogMapper.updateMemberFeatureLog(memberFeatureLog);
    }

    /**
     * 批量删除会员特性变更记录
     * 
     * @param ids 需要删除的会员特性变更记录主键
     * @return 结果
     */
    @Override
    public int deleteMemberFeatureLogByIds(Long[] ids)
    {
        return memberFeatureLogMapper.deleteMemberFeatureLogByIds(ids);
    }

    /**
     * 删除会员特性变更记录信息
     * 
     * @param id 会员特性变更记录主键
     * @return 结果
     */
    @Override
    public int deleteMemberFeatureLogById(Long id)
    {
        return memberFeatureLogMapper.deleteMemberFeatureLogById(id);
    }
}
