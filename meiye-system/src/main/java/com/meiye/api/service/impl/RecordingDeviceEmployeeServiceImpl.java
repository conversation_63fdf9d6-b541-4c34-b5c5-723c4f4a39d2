package com.meiye.api.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.RecordingDeviceEmployeeMapper;
import com.meiye.api.domain.RecordingDeviceEmployee;
import com.meiye.api.service.RecordingDeviceEmployeeService;

/**
 * 录音设备分配Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class RecordingDeviceEmployeeServiceImpl implements RecordingDeviceEmployeeService
{
    @Autowired
    private RecordingDeviceEmployeeMapper recordingDeviceEmployeeMapper;

    /**
     * 查询录音设备分配
     *
     * @param id 录音设备分配主键
     * @return 录音设备分配
     */
    @Override
    public RecordingDeviceEmployee selectRecordingDeviceEmployeeById(Long id)
    {
        return recordingDeviceEmployeeMapper.selectRecordingDeviceEmployeeById(id);
    }

    /**
     * 查询录音设备分配列表
     *
     * @param recordingDeviceEmployee 录音设备分配
     * @return 录音设备分配
     */
    @Override
    public List<RecordingDeviceEmployee> selectRecordingDeviceEmployeeList(RecordingDeviceEmployee recordingDeviceEmployee)
    {
        return recordingDeviceEmployeeMapper.selectRecordingDeviceEmployeeList(recordingDeviceEmployee);
    }

    /**
     * 新增录音设备分配
     *
     * @param recordingDeviceEmployee 录音设备分配
     * @return 结果
     */
    @Override
    public int insertRecordingDeviceEmployee(RecordingDeviceEmployee recordingDeviceEmployee)
    {
        return recordingDeviceEmployeeMapper.insertRecordingDeviceEmployee(recordingDeviceEmployee);
    }

    /**
     * 修改录音设备分配
     *
     * @param recordingDeviceEmployee 录音设备分配
     * @return 结果
     */
    @Override
    public int updateRecordingDeviceEmployee(RecordingDeviceEmployee recordingDeviceEmployee)
    {
        return recordingDeviceEmployeeMapper.updateRecordingDeviceEmployee(recordingDeviceEmployee);
    }

    /**
     * 批量删除录音设备分配
     *
     * @param ids 需要删除的录音设备分配主键
     * @return 结果
     */
    @Override
    public int deleteRecordingDeviceEmployeeByIds(Long[] ids)
    {
        return recordingDeviceEmployeeMapper.deleteRecordingDeviceEmployeeByIds(ids);
    }

    /**
     * 删除录音设备分配信息
     *
     * @param id 录音设备分配主键
     * @return 结果
     */
    @Override
    public int deleteRecordingDeviceEmployeeById(Long id)
    {
        return recordingDeviceEmployeeMapper.deleteRecordingDeviceEmployeeById(id);
    }
}
