package com.meiye.api.service.impl;

import com.meiye.api.query.StoreProductQuery;
import com.meiye.api.vo.StoreProductVO;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.StoreProduct;
import com.meiye.api.mapper.StoreProductMapper;
import com.meiye.api.service.StoreProductService;

@Service
public class StoreProductServiceImpl extends ServiceImpl<StoreProductMapper, StoreProduct> implements StoreProductService {

    @Autowired
    StoreProductMapper storeProductMapper;

    /**
     * 查询店面实际销售物
     *
     * @param id 店面实际销售物主键
     * @return 店面实际销售物
     */
    @Override
    public StoreProduct selectStoreProductById(Long id)
    {
        return storeProductMapper.selectStoreProductById(id);
    }

    /**
     * 查询店面实际销售物列表
     *
     * @param storeProduct 店面实际销售物
     * @return 店面实际销售物
     */
    @Override
    public List<StoreProduct> selectStoreProductList(StoreProduct storeProduct)
    {
        return storeProductMapper.selectStoreProductList(storeProduct);
    }

    /**
     * 新增店面实际销售物
     *
     * @param storeProduct 店面实际销售物
     * @return 结果
     */
    @Override
    public int insertStoreProduct(StoreProduct storeProduct)
    {
        return storeProductMapper.insertStoreProduct(storeProduct);
    }

    /**
     * 修改店面实际销售物
     *
     * @param storeProduct 店面实际销售物
     * @return 结果
     */
    @Override
    public int updateStoreProduct(StoreProduct storeProduct)
    {
        return storeProductMapper.updateStoreProduct(storeProduct);
    }

    /**
     * 批量删除店面实际销售物
     *
     * @param ids 需要删除的店面实际销售物主键
     * @return 结果
     */
    @Override
    public int deleteStoreProductByIds(Long[] ids)
    {
        return storeProductMapper.deleteStoreProductByIds(ids);
    }

    /**
     * 删除店面实际销售物信息
     *
     * @param id 店面实际销售物主键
     * @return 结果
     */
    @Override
    public int deleteStoreProductById(Long id)
    {
        return storeProductMapper.deleteStoreProductById(id);
    }

    /**
     * 删除店面实际销售物信息
     *
     * @param id 店面实际销售物主键
     * @return 结果
     */
    @Override
    public int deleteStoreProductByPId(Long id)
    {
        return storeProductMapper.deleteStoreProductById(id);
    }

    @Override
    public List<StoreProductVO> queryStoreProductList(StoreProductQuery storeProductQuery) {
        List<StoreProductVO> storeProductVOList = storeProductMapper.queryStoreProductList(storeProductQuery);
        for (StoreProductVO storeProductVO : storeProductVOList) {
            if (storeProductVO.getSalePrice() == null) {
                storeProductVO.setSalePrice(storeProductVO.getPrice());
            }
            if (storeProductVO.getStoreProductCommissionList().isEmpty()) {
                if (!storeProductVO.getProductCommissionList().isEmpty()) {
                    List<StoreProductVO.StoreProductCommissionVo> storeProductCommissionVoList = new ArrayList<>();
                    for (StoreProductVO.ProductCommissionVO productCommissionVO : storeProductVO.getProductCommissionList()) {
                        StoreProductVO.StoreProductCommissionVo storeProductCommissionVo = new StoreProductVO.StoreProductCommissionVo();
                        storeProductCommissionVo.setStoreProductId(storeProductVO.getStoreProductId());
                        storeProductCommissionVo.setDepartmentId(productCommissionVO.getDepartmentId());
                        storeProductCommissionVo.setPositionId(productCommissionVO.getPositionId());
                        storeProductCommissionVo.setBalanceCommissionRate(storeProductCommissionVo.getBalanceCommissionRate());
                        storeProductCommissionVo.setCashCommissionRate(storeProductCommissionVo.getCashCommissionRate());
                        storeProductCommissionVoList.add(storeProductCommissionVo);
                    }
                    storeProductVO.setStoreProductCommissionList(storeProductCommissionVoList);
                }
            }
        }
        return storeProductVOList;
    }


}
