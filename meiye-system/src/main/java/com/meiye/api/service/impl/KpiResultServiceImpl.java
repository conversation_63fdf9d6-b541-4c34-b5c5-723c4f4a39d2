package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.mapper.KpiResultMapper;
import com.meiye.api.domain.KpiResult;
import com.meiye.api.service.KpiResultService;
@Service
public class KpiResultServiceImpl
        extends ServiceImpl<KpiResultMapper, KpiResult>
        implements KpiResultService{

    @Autowired
    private KpiResultMapper kpiResultMapper;

    /**
     * 查询KPI结果
     *
     * @param id KPI结果主键
     * @return KPI结果
     */
    @Override
    public KpiResult selectKpiResultById(Long id)
    {
        return kpiResultMapper.selectKpiResultById(id);
    }

    /**
     * 查询KPI结果列表
     *
     * @param kpiResult KPI结果
     * @return KPI结果
     */
    @Override
    public List<KpiResult> selectKpiResultList(KpiResult kpiResult)
    {
        return kpiResultMapper.selectKpiResultList(kpiResult);
    }

    /**
     * 新增KPI结果
     *
     * @param kpiResult KPI结果
     * @return 结果
     */
    @Override
    public int insertKpiResult(KpiResult kpiResult)
    {
        return kpiResultMapper.insertKpiResult(kpiResult);
    }

    /**
     * 修改KPI结果
     *
     * @param kpiResult KPI结果
     * @return 结果
     */
    @Override
    public int updateKpiResult(KpiResult kpiResult)
    {
        return kpiResultMapper.updateKpiResult(kpiResult);
    }

    /**
     * 批量删除KPI结果
     *
     * @param ids 需要删除的KPI结果主键
     * @return 结果
     */
    @Override
    public int deleteKpiResultByIds(Long[] ids)
    {
        return kpiResultMapper.deleteKpiResultByIds(ids);
    }

    /**
     * 删除KPI结果信息
     *
     * @param id KPI结果主键
     * @return 结果
     */
    @Override
    public int deleteKpiResultById(Long id)
    {
        return kpiResultMapper.deleteKpiResultById(id);
    }

}
