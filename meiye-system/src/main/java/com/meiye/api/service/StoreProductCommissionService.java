package com.meiye.api.service;

import java.util.List;
import com.meiye.api.domain.StoreProductCommission;

/**
 * 店面商品提成设置Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface StoreProductCommissionService
{
    /**
     * 查询店面商品提成设置
     * 
     * @param id 店面商品提成设置主键
     * @return 店面商品提成设置
     */
    public StoreProductCommission selectStoreProductCommissionById(Long id);

    /**
     * 查询店面商品提成设置
     *
     * @param id 店面商品提成设置主键
     * @return 店面商品提成设置
     */
    public StoreProductCommission selectStoreProductCommissionBySId(Long id);

    /**
     * 根据店面销售物ID查询提成设置列表
     * 
     * @param storeProductId 店面销售物ID
     * @return 店面商品提成设置集合
     */
    public List<StoreProductCommission> selectByStoreProductId(Long storeProductId);

    /**
     * 查询店面商品提成设置列表
     * 
     * @param storeProductCommission 店面商品提成设置
     * @return 店面商品提成设置集合
     */
    public List<StoreProductCommission> selectStoreProductCommissionList(StoreProductCommission storeProductCommission);

    /**
     * 新增店面商品提成设置
     * 
     * @param storeProductCommission 店面商品提成设置
     * @return 结果
     */
    public int insertStoreProductCommission(StoreProductCommission storeProductCommission);

    /**
     * 修改店面商品提成设置
     * 
     * @param storeProductCommission 店面商品提成设置
     * @return 结果
     */
    public int updateStoreProductCommission(StoreProductCommission storeProductCommission);

    /**
     * 批量删除店面商品提成设置
     * 
     * @param ids 需要删除的店面商品提成设置主键集合
     * @return 结果
     */
    public int deleteStoreProductCommissionByIds(Long[] ids);

    /**
     * 删除店面商品提成设置信息
     * 
     * @param id 店面商品提成设置主键
     * @return 结果
     */
    public int deleteStoreProductCommissionById(Long id);
}