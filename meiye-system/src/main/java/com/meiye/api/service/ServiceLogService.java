package com.meiye.api.service;

import com.meiye.api.domain.ServiceLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ServiceLogService
        extends IService<ServiceLog>{

    /**
     * 查询服务实施
     *
     * @param id 服务实施主键
     * @return 服务实施
     */
    public ServiceLog selectServiceLogById(Long id);

    /**
     * 查询服务实施列表
     *
     * @param serviceLog 服务实施
     * @return 服务实施集合
     */
    public List<ServiceLog> selectServiceLogList(ServiceLog serviceLog);

    /**
     * 新增服务实施
     *
     * @param serviceLog 服务实施
     * @return 结果
     */
    public int insertServiceLog(ServiceLog serviceLog);

    /**
     * 修改服务实施
     *
     * @param serviceLog 服务实施
     * @return 结果
     */
    public int updateServiceLog(ServiceLog serviceLog);

    /**
     * 批量删除服务实施
     *
     * @param ids 需要删除的服务实施主键集合
     * @return 结果
     */
    public int deleteServiceLogByIds(Long[] ids);

    /**
     * 删除服务实施信息
     *
     * @param id 服务实施主键
     * @return 结果
     */
    public int deleteServiceLogById(Long id);



}
