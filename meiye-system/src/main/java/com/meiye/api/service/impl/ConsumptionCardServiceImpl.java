package com.meiye.api.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.meiye.api.domain.ProductItem;
import com.meiye.api.mapper.ProductItemMapper;
import com.meiye.api.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.ConsumptionCardMapper;
import com.meiye.api.domain.ConsumptionCard;
import com.meiye.api.service.ConsumptionCardService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 消费卡补充定义Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class ConsumptionCardServiceImpl implements ConsumptionCardService
{
    @Autowired
    private ConsumptionCardMapper consumptionCardMapper;
    @Autowired
    private ProductService productService;
    @Autowired
    private ProductItemMapper productItemMapper;

    /**
     * 查询消费卡补充定义
     * 
     * @param id 消费卡补充定义主键
     * @return 消费卡补充定义
     */
    @Override
    public ConsumptionCard selectConsumptionCardById(Long id)
    {
        return consumptionCardMapper.selectConsumptionCardByPId(id);
    }

    /**
     * 查询消费卡补充定义列表
     * 
     * @param consumptionCard 消费卡补充定义
     * @return 消费卡补充定义
     */
    @Override
    public List<ConsumptionCard> selectConsumptionCardList(ConsumptionCard consumptionCard)
    {
        return consumptionCardMapper.selectConsumptionCardList(consumptionCard);
    }

    /**
     * 新增消费卡补充定义
     * 
     * @param consumptionCard 消费卡补充定义
     * @return 结果
     */
    @Override
    @Transactional
    public int insertConsumptionCard(ConsumptionCard consumptionCard) {
        try {
            System.out.println("接收到的消费卡数据: " + consumptionCard);
            System.out.println("产品ID: " + consumptionCard.getProductId());
            System.out.println("服务列表: " + (consumptionCard.getServiceList() != null ? consumptionCard.getServiceList().size() : "null"));
            System.out.println("产品列表: " + (consumptionCard.getProjectList() != null ? consumptionCard.getProjectList().size() : "null"));

            if (consumptionCard.getServiceList() != null) {
                System.out.println("服务列表详情:");
                for (ProductItem item : consumptionCard.getServiceList()) {
                    System.out.println("  - " + item);
                }
            }

            if (consumptionCard.getProjectList() != null) {
                System.out.println("产品列表详情:");
                for (ProductItem item : consumptionCard.getProjectList()) {
                    System.out.println("  - " + item);
                }
            }

            // 1. 验证产品ID
            Long productId = consumptionCard.getProductId();
            if (productId == null) {
                throw new IllegalArgumentException("productId 不能为空");
            }

            // 2. 插入消费卡表
            consumptionCardMapper.insertConsumptionCard(consumptionCard);

            // 3. 插入服务项目/产品项目
            if (Arrays.asList("experience", "times").contains(consumptionCard.getType())) {
                if (consumptionCard.getServiceList() != null && !consumptionCard.getServiceList().isEmpty()) {
                    System.out.println("开始插入服务项目，数量: " + consumptionCard.getServiceList().size());
                    for (ProductItem item : consumptionCard.getServiceList()) {
                        item.setMainProductId(consumptionCard.getProductId());
                        item.setSubProductId(item.getId());
                        int itemResult = productItemMapper.insertProductItem(item);
                        System.out.println("插入服务项目: " + item + ", 结果: " + itemResult);
                    }
                } else {
                    System.out.println("服务列表为空或null");
                }

                if (consumptionCard.getProjectList() != null && !consumptionCard.getProjectList().isEmpty()) {
                    System.out.println("开始插入产品项目，数量: " + consumptionCard.getProjectList().size());
                    for (ProductItem item : consumptionCard.getProjectList()) {
                        item.setMainProductId(consumptionCard.getProductId());
                        item.setSubProductId(item.getId());
                        int itemResult = productItemMapper.insertProductItem(item);
                        System.out.println("插入产品项目: " + item + ", 结果: " + itemResult);
                    }
                } else {
                    System.out.println("产品列表为空或null");
                }
            } else {
                System.out.println("消费卡类型不是 experience 或 times，跳过插入项目");
            }

            return 1;
        } catch (Exception e) {
            System.out.println("插入消费卡时发生异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
    /**
     * 修改消费卡补充定义
     * 
     * @param card 消费卡补充定义
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateConsumptionCard(ConsumptionCard card) {
        // 1. 更新主表
        int rows = consumptionCardMapper.updateConsumptionCard(card);
        Long productId = card.getProductId();
        List<ProductItem> serviceList = card.getServiceList();
        List<ProductItem> projectList = card.getProjectList();

        // 2. 获取数据库中现有的所有子产品
        List<ProductItem> existingItems = productItemMapper.selectProductItemsByProductId(productId);

        // 3. 合并前端传来的服务列表和产品列表
        List<ProductItem> allNewItems = new ArrayList<>();
        if (serviceList != null) {
            allNewItems.addAll(serviceList);
        }
        if (projectList != null) {
            allNewItems.addAll(projectList);
        }

        // 4. 找出需要删除的子产品（存在于数据库但不在新列表中）
        List<Long> idsToDelete = existingItems.stream()
                .filter(existingItem -> allNewItems.stream()
                        .noneMatch(newItem -> newItem.getId() != null && newItem.getId().equals(existingItem.getSubProductId())))
                .map(ProductItem::getId)
                .collect(Collectors.toList());

        // 5. 删除不再需要的子产品
        if (!idsToDelete.isEmpty()) {
            productItemMapper.deleteBatchByIds(idsToDelete);
        }

        // 6. 更新或插入新的子产品
        if (!allNewItems.isEmpty()) {
            allNewItems.forEach(it -> {
                it.setMainProductId(productId);
                it.setSubProductId(it.getId());
                // 不再设置 type 字段
            });
            productItemMapper.upsertBatch(allNewItems);
        }

        return rows;
    }

    /**
     * 批量删除消费卡补充定义
     * 
     * @param ids 需要删除的消费卡补充定义主键
     * @return 结果
     */
    @Override
    public int deleteConsumptionCardByIds(Long[] ids)
    {
        productService.deleteProductById(ids[0]);
        System.out.println("删除消费卡关联的productId：" + ids[0]);
        productItemMapper.deleteProductItemByMPId(ids[0]);
        return consumptionCardMapper.deleteConsumptionCardByIds(ids);
    }

    /**
     * 删除消费卡补充定义信息
     *
     * @param id 消费卡补充定义主键
     * @return 结果
     */
    @Override
    public int deleteConsumptionCardById(Long id) {return consumptionCardMapper.deleteConsumptionCardById(id);}
}
