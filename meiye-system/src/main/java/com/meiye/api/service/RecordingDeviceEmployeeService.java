package com.meiye.api.service;

import java.util.List;
import com.meiye.api.domain.RecordingDeviceEmployee;

/**
 * 录音设备分配Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface RecordingDeviceEmployeeService
{
    /**
     * 查询录音设备分配
     *
     * @param id 录音设备分配主键
     * @return 录音设备分配
     */
    public RecordingDeviceEmployee selectRecordingDeviceEmployeeById(Long id);

    /**
     * 查询录音设备分配列表
     *
     * @param recordingDeviceEmployee 录音设备分配
     * @return 录音设备分配集合
     */
    public List<RecordingDeviceEmployee> selectRecordingDeviceEmployeeList(RecordingDeviceEmployee recordingDeviceEmployee);

    /**
     * 新增录音设备分配
     *
     * @param recordingDeviceEmployee 录音设备分配
     * @return 结果
     */
    public int insertRecordingDeviceEmployee(RecordingDeviceEmployee recordingDeviceEmployee);

    /**
     * 修改录音设备分配
     *
     * @param recordingDeviceEmployee 录音设备分配
     * @return 结果
     */
    public int updateRecordingDeviceEmployee(RecordingDeviceEmployee recordingDeviceEmployee);

    /**
     * 批量删除录音设备分配
     *
     * @param ids 需要删除的录音设备分配主键集合
     * @return 结果
     */
    public int deleteRecordingDeviceEmployeeByIds(Long[] ids);

    /**
     * 删除录音设备分配信息
     *
     * @param id 录音设备分配主键
     * @return 结果
     */
    public int deleteRecordingDeviceEmployeeById(Long id);
}
