package com.meiye.api.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.domain.MemberProduct;

/**
 * 适于该会员的推荐项目Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface MemberProductService extends IService<MemberProduct>
{
    /**
     * 查询适于该会员的推荐项目
     *
     * @param id 适于该会员的推荐项目主键
     * @return 适于该会员的推荐项目
     */
    public MemberProduct selectMemberProductById(Long id);

    /**
     * 查询适于该会员的推荐项目列表
     *
     * @param memberProduct 适于该会员的推荐项目
     * @return 适于该会员的推荐项目集合
     */
    public List<MemberProduct> selectMemberProductList(MemberProduct memberProduct);

    /**
     * 新增适于该会员的推荐项目
     *
     * @param memberProduct 适于该会员的推荐项目
     * @return 结果
     */
    public int insertMemberProduct(MemberProduct memberProduct);

    /**
     * 修改适于该会员的推荐项目
     *
     * @param memberProduct 适于该会员的推荐项目
     * @return 结果
     */
    public int updateMemberProduct(MemberProduct memberProduct);

    /**
     * 批量删除适于该会员的推荐项目
     *
     * @param ids 需要删除的适于该会员的推荐项目主键集合
     * @return 结果
     */
    public int deleteMemberProductByIds(Long[] ids);

    /**
     * 删除适于该会员的推荐项目信息
     *
     * @param id 适于该会员的推荐项目主键
     * @return 结果
     */
    public int deleteMemberProductById(Long id);
}

