package com.meiye.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.domain.Store;
import com.meiye.api.domain.StoreProduct;

import java.util.List;
import java.util.Map;

/**
 * 店面Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IStoreService extends IService<Store>
{
    /**
     * 查询店面
     * 
     * @param id 店面主键
     * @return 店面
     */
    public Store selectStoreById(Long id);

    /**
     * 查询店面
     *
     * @param id 店面主键
     * @return 店面
     */
    public List<Store> selectNoSysStoreById(Long id);

    /**
     * 查询子店面
     *
     * @param id 店面主键
     * @return 店面
     */
    public Store selectSonById(Long id);

    /**
     * 查询店面列表
     * 
     * @param store 店面
     * @return 店面集合
     */
    public List<Store> selectStoreList(Store store);

    /**
     * 新增店面
     * 
     * @param store 店面
     * @return 结果
     */
    public int insertStore(Store store);

    /**
     * 修改店面
     * 
     * @param store 店面
     * @return 结果
     */
    public int updateStore(Store store);


    /**
     * 获取所有店铺（不分页）
     */
    List<Store> listAllStores();

    /**
     * 批量删除店面
     * 
     * @param ids 需要删除的店面主键集合
     * @return 结果
     */
    public int deleteStoreByIds(Long[] ids);

    /**
     * 删除店面信息
     * 
     * @param id 店面主键
     * @return 结果
     */
    public int deleteStoreById(Long id);

    int insertStoreAndDept(Store store);


    int updateStoreAndDept(Store store);

    List<Store> unallocated();

    Store selectStoreInfo(Long id);

    Map<String, Object> selectStoreUserInfo(Long userId);
}
