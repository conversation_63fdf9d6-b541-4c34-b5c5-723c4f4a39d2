package com.meiye.api.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.MemberFeatureType;
import com.meiye.api.mapper.MemberFeatureTypeMapper;
import com.meiye.api.service.MemberFeatureTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 特性类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class MemberFeatureTypeServiceImpl 
        extends ServiceImpl<MemberFeatureTypeMapper, MemberFeatureType>
        implements MemberFeatureTypeService
{
    @Autowired
    private MemberFeatureTypeMapper memberFeatureTypeMapper;

    /**
     * 查询特性类型
     * 
     * @param id 特性类型主键
     * @return 特性类型
     */
    @Override
    public MemberFeatureType selectMemberFeatureTypeById(Long id)
    {
        return memberFeatureTypeMapper.selectMemberFeatureTypeById(id);
    }

    /**
     * 查询特性类型列表
     * 
     * @param memberFeatureType 特性类型
     * @return 特性类型
     */
    @Override
    public List<MemberFeatureType> selectMemberFeatureTypeList(MemberFeatureType memberFeatureType)
    {
        return memberFeatureTypeMapper.selectMemberFeatureTypeList(memberFeatureType);
    }
    /**
     * 查询所有特性类型（不分页）
     */
    @Override
    public List<MemberFeatureType> selectAllMemberFeatureTypes()
    {
        return memberFeatureTypeMapper.selectAllMemberFeatureTypes();
    }

    /**
     * 新增特性类型
     * 
     * @param memberFeatureType 特性类型
     * @return 结果
     */
    @Override
    public int insertMemberFeatureType(MemberFeatureType memberFeatureType)
    {
        return memberFeatureTypeMapper.insertMemberFeatureType(memberFeatureType);
    }

    /**
     * 修改特性类型
     * 
     * @param memberFeatureType 特性类型
     * @return 结果
     */
    @Override
    public int updateMemberFeatureType(MemberFeatureType memberFeatureType)
    {
        return memberFeatureTypeMapper.updateMemberFeatureType(memberFeatureType);
    }

    /**
     * 批量删除特性类型
     * 
     * @param ids 需要删除的特性类型主键
     * @return 结果
     */
    @Override
    public int deleteMemberFeatureTypeByIds(Long[] ids) 
    {
        return memberFeatureTypeMapper.deleteMemberFeatureTypeByIds(ids);
    }

    /**
     * 删除特性类型信息
     * 
     * @param id 特性类型主键
     * @return 结果
     */
    @Override
    public int deleteMemberFeatureTypeById(Long id)
    {
        return memberFeatureTypeMapper.deleteMemberFeatureTypeById(id);
    }
}
