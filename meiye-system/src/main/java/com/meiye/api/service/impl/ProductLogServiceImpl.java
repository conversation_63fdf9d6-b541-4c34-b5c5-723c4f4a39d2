package com.meiye.api.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.ProductLogMapper;
import com.meiye.api.domain.ProductLog;
import com.meiye.api.service.ProductLogService;

/**
 * 主店面销售物定义变更记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Service
public class ProductLogServiceImpl implements ProductLogService
{
    @Autowired
    private ProductLogMapper productLogMapper;

    /**
     * 查询主店面销售物定义变更记录
     * 
     * @param id 主店面销售物定义变更记录主键
     * @return 主店面销售物定义变更记录
     */
    @Override
    public ProductLog selectProductLogById(Long id)
    {
        return productLogMapper.selectProductLogById(id);
    }

    /**
     * 查询主店面销售物定义变更记录列表
     * 
     * @param productLog 主店面销售物定义变更记录
     * @return 主店面销售物定义变更记录
     */
    @Override
    public List<ProductLog> selectProductLogList(ProductLog productLog)
    {
        return productLogMapper.selectProductLogList(productLog);
    }

    /**
     * 新增主店面销售物定义变更记录
     * 
     * @param productLog 主店面销售物定义变更记录
     * @return 结果
     */
    @Override
    public int insertProductLog(ProductLog productLog)
    {
        return productLogMapper.insertProductLog(productLog);
    }

    /**
     * 修改主店面销售物定义变更记录
     * 
     * @param productLog 主店面销售物定义变更记录
     * @return 结果
     */
    @Override
    public int updateProductLog(ProductLog productLog)
    {
        return productLogMapper.updateProductLog(productLog);
    }

    /**
     * 批量删除主店面销售物定义变更记录
     * 
     * @param ids 需要删除的主店面销售物定义变更记录主键
     * @return 结果
     */
    @Override
    public int deleteProductLogByIds(Long[] ids)
    {
        return productLogMapper.deleteProductLogByIds(ids);
    }

    /**
     * 删除主店面销售物定义变更记录信息
     * 
     * @param id 主店面销售物定义变更记录主键
     * @return 结果
     */
    @Override
    public int deleteProductLogById(Long id)
    {
        return productLogMapper.deleteProductLogById(id);
    }
}
