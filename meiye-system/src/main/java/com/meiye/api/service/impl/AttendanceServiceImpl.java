package com.meiye.api.service.impl;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import com.meiye.api.domain.Store;
import com.meiye.api.mapper.StoreMapper;
import com.meiye.common.core.domain.entity.SysStore;
import com.meiye.common.exception.ServiceException;
import com.meiye.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.AttendanceMapper;
import com.meiye.api.domain.Attendance;
import com.meiye.api.service.AttendanceService;
import com.meiye.common.core.domain.entity.SysUser;
import org.springframework.transaction.annotation.Transactional;

import static com.meiye.common.utils.SecurityUtils.getUserId;

@Service
public class AttendanceServiceImpl implements AttendanceService
{
    @Autowired
    private AttendanceMapper attendanceMapper;
    @Autowired
    SysUserMapper sysUserMapper;
    @Autowired
    StoreMapper storeMapper;

    @Override
    public Attendance selectAttendanceById(Long id) {
        return attendanceMapper.selectAttendanceById(id);
    }

    @Override
    public List<Attendance> selectAttendanceList(Attendance attendance) {
        return attendanceMapper.selectAttendanceList(attendance);
    }

    @Override
    public int insertAttendance(Attendance attendance) {
        String type = attendance.getType();

        // 验证操作类型
        if (!"clockIn".equals(type) && !"clockOut".equals(type)) {
            throw new ServiceException("无效的操作类型: " + type);
        }

        Date now = new Date();
        LocalDate today = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Date todayDate = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());

        Long employeeId = getUserId();
        SysUser user = sysUserMapper.selectUserById(employeeId);
        if (user == null) {
            throw new ServiceException("员工不存在: " + employeeId);
        }

        Store store = storeMapper.selectById(user.getStoreId());
        if (store == null) {
            throw new ServiceException("店铺不存在: " + user.getStoreId());
        }

        Attendance record = attendanceMapper.selectAttendanceByUserAndDate(employeeId, todayDate);

        if (record == null) {
            record = new Attendance();
            record.setEmployeeId(employeeId);
            record.setDate(now);
        }

        if (attendance.getLat() != null && attendance.getLon() != null) {
            record.setLat(attendance.getLat());
            record.setLon(attendance.getLon());
        }

        if ("clockIn".equals(type)) {
            if (record.getCheckIn() != null) {
                throw new ServiceException("今日已签到，不能重复签到");
            }
            record.setCheckIn(now);
            // 只检查迟到状态
            checkLateStatus(record, store);
        } else {
            if (record.getCheckOut() != null) {
                throw new ServiceException("今日已签退，不能重复签退");
            }
            record.setCheckOut(now);
            // 只检查早退状态
            checkEarlyLeaveStatus(record, store);
        }

        // 检查外勤状态（适用于签到和签退）
        checkOutArea(record, store);

        if (record.getId() == null) {
            return attendanceMapper.insertAttendance(record);
        } else {
            return attendanceMapper.updateAttendance(record);
        }
    }

    @Override
    public int updateAttendance(Attendance attendance) {
        return attendanceMapper.updateAttendance(attendance);
    }

    @Override
    public int deleteAttendanceByIds(Long[] ids) {
        return attendanceMapper.deleteAttendanceByIds(ids);
    }

    @Override
    public int deleteAttendanceById(Long id) {
        return attendanceMapper.deleteAttendanceById(id);
    }

    private void checkLateStatus(Attendance record, Store store) {
        if (record.getCheckIn() != null && store.getStartTime() != null) {
            LocalTime checkInTime = record.getCheckIn().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalTime();
            LocalTime startTime = store.getStartTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalTime();

            record.setBeLate(checkInTime.isAfter(startTime));
        }
    }

    private void checkEarlyLeaveStatus(Attendance record, Store store) {
        if (record.getCheckOut() != null && store.getEndTime() != null) {
            LocalTime checkOutTime = record.getCheckOut().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalTime();
            LocalTime endTime = store.getEndTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalTime();

            record.setLeaveEarly(checkOutTime.isBefore(endTime));
        }
    }

    private void checkOutArea(Attendance record, Store store) {
        if (record.getLat() != null && record.getLon() != null &&
                store.getLat() != null && store.getLon() != null) {

            double distance = calculateDistance(
                    record.getLat(), record.getLon(),
                    store.getLat().doubleValue(), store.getLon().doubleValue()
            );

            float checkInRadius = store.getCheckInAround() != null ?
                    store.getCheckInAround().floatValue() : 1.0f;

            record.setOutArea(distance > checkInRadius);
        } else {
            record.setOutArea(false);
        }
    }

    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371;

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);

        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }
}