package com.meiye.api.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.StoreProductCommissionMapper;
import com.meiye.api.domain.StoreProductCommission;
import com.meiye.api.service.StoreProductCommissionService;

/**
 * 店面商品提成设置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Service
public class StoreProductCommissionServiceImpl implements StoreProductCommissionService
{
    @Autowired
    private StoreProductCommissionMapper storeProductCommissionMapper;

    /**
     * 查询店面商品提成设置
     * 
     * @param id 店面商品提成设置主键
     * @return 店面商品提成设置
     */
    @Override
    public StoreProductCommission selectStoreProductCommissionById(Long id)
    {
        return storeProductCommissionMapper.selectStoreProductCommissionById(id);
    }

    /**
     * 查询店面商品提成设置
     *
     * @param id 店面商品提成设置主键
     * @return 店面商品提成设置
     */
    @Override
    public StoreProductCommission selectStoreProductCommissionBySId(Long id)
    {
        return storeProductCommissionMapper.selectStoreProductCommissionBySId(id);
    }

    /**
     * 根据店面销售物ID查询提成设置列表
     * 
     * @param storeProductId 店面销售物ID
     * @return 店面商品提成设置集合
     */
    @Override
    public List<StoreProductCommission> selectByStoreProductId(Long storeProductId)
    {
        StoreProductCommission query = new StoreProductCommission();
        query.setStoreProductId(storeProductId);
        return storeProductCommissionMapper.selectStoreProductCommissionList(query);
    }

    /**
     * 查询店面商品提成设置列表
     * 
     * @param storeProductCommission 店面商品提成设置
     * @return 店面商品提成设置
     */
    @Override
    public List<StoreProductCommission> selectStoreProductCommissionList(StoreProductCommission storeProductCommission)
    {
        return storeProductCommissionMapper.selectStoreProductCommissionList(storeProductCommission);
    }

    /**
     * 新增店面商品提成设置
     * 
     * @param storeProductCommission 店面商品提成设置
     * @return 结果
     */
    @Override
    public int insertStoreProductCommission(StoreProductCommission storeProductCommission)
    {
        return storeProductCommissionMapper.insertStoreProductCommission(storeProductCommission);
    }

    /**
     * 修改店面商品提成设置
     * 
     * @param storeProductCommission 店面商品提成设置
     * @return 结果
     */
    @Override
    public int updateStoreProductCommission(StoreProductCommission storeProductCommission)
    {
        return storeProductCommissionMapper.updateStoreProductCommission(storeProductCommission);
    }

    /**
     * 批量删除店面商品提成设置
     * 
     * @param ids 需要删除的店面商品提成设置主键
     * @return 结果
     */
    @Override
    public int deleteStoreProductCommissionByIds(Long[] ids)
    {
        return storeProductCommissionMapper.deleteStoreProductCommissionByIds(ids);
    }

    /**
     * 删除店面商品提成设置信息
     * 
     * @param id 店面商品提成设置主键
     * @return 结果
     */
    @Override
    public int deleteStoreProductCommissionById(Long id)
    {
        return storeProductCommissionMapper.deleteStoreProductCommissionById(id);
    }
}