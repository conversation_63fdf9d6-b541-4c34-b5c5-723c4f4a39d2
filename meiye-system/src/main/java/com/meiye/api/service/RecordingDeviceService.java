package com.meiye.api.service;

import java.util.List;
import com.meiye.api.domain.RecordingDevice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meiye.api.vo.RecordingDeviceVo;

import java.util.List;

public interface RecordingDeviceService extends IService<RecordingDevice>{


    List<RecordingDeviceVo> getRecordDeviceVoList();
    /**
     * 查询录音设备
     *
     * @param id 录音设备主键
     * @return 录音设备
     */
    public RecordingDevice selectRecordingDeviceById(Long id);

    /**
     * 查询录音设备列表
     *
     * @param recordingDevice 录音设备
     * @return 录音设备集合
     */
    public List<RecordingDevice> selectRecordingDeviceList(RecordingDevice recordingDevice);

    /**
     * 新增录音设备
     *
     * @param recordingDevice 录音设备
     * @return 结果
     */
    public int insertRecordingDevice(RecordingDevice recordingDevice);

    /**
     * 修改录音设备
     *
     * @param recordingDevice 录音设备
     * @return 结果
     */
    public int updateRecordingDevice(RecordingDevice recordingDevice);

    /**
     * 批量删除录音设备
     *
     * @param ids 需要删除的录音设备主键集合
     * @return 结果
     */
    public int deleteRecordingDeviceByIds(Long[] ids);

    /**
     * 删除录音设备信息
     *
     * @param id 录音设备主键
     * @return 结果
     */
    public int deleteRecordingDeviceById(Long id);
}
