package com.meiye.api.service;

import java.util.List;
import com.meiye.api.domain.Product;
import com.meiye.api.query.StoreProductQuery;
import com.meiye.api.vo.OptionVo;

/**
 * 主店面销售物定义Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface ProductService
{
    /**
     * 查询主店面销售物定义
     *
     * @param id 主店面销售物定义主键
     * @return 主店面销售物定义
     */
    public Product selectProductById(Long id);

    /**
     * 查询主店面销售物定义列表
     *
     * @param product 主店面销售物定义
     * @return 主店面销售物定义集合
     */
    public List<Product> selectProductList(Product product);

    /**
     * 查询主店面销售物定义列表
     *
     * @param product 主店面销售物定义
     * @return 主店面销售物定义集合
     */
    public List<Product> selectServiceList(Product product);

    /**
     * 查询主店面销售物定义列表
     *
     * @param product 主店面销售物定义
     * @return 主店面销售物定义集合
     */
    public List<Product> selectCardList(Product product);

    /**
     * 新增主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    public int insertService(Product product);

    /**
     * 新增主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    public int insertProduct(Product product);

    /**
     * 新增主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    public int insertCard(Product product);

    /**
     * 修改主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    public int updateProduct(Product product);

    /**
     * 删除主店面销售物定义信息
     *
     * @param id 主店面销售物定义主键
     * @return 结果
     */
    public int deleteProductById(Long id);



    List<Product> selectProductOption(Product product);

    List<OptionVo> selectOptionSp(StoreProductQuery storeProductQuery);
}
