package com.meiye.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.EmployeeInfo;
import com.meiye.api.domain.Store;
import com.meiye.api.domain.UploadedImage;
import com.meiye.api.mapper.StoreMapper;
import com.meiye.api.service.IStoreService;
import com.meiye.api.service.UploadedImageService;
import com.meiye.common.core.domain.entity.SysDept;
import com.meiye.common.core.domain.entity.SysUser;
import com.meiye.common.utils.StringUtils;
import com.meiye.system.domain.SysPost;
import com.meiye.system.service.ISysDeptService;
import com.meiye.system.service.ISysPostService;
import com.meiye.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.meiye.common.utils.SecurityUtils.getUsername;

/**
 * 店面Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
public class StoreServiceImpl extends ServiceImpl<StoreMapper, Store> implements IStoreService {
    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private EmployeeInfoServiceImpl employeeInfoService;

    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysPostService sysPostService;
    @Autowired
    private UploadedImageService uploadedImageService;

    /**
     * 查询店面
     *
     * @param id 店面主键
     * @return 店面
     */
    @Override
    public Store selectStoreById(Long id) {
        return storeMapper.selectStoreById(id);
    }

    /**
     * 查询店面
     *
     * @param id 店面主键
     * @return 店面
     */
    @Override
    public List<Store> selectNoSysStoreById(Long id) {
        Store currentStore = storeMapper.selectNoSysStoreById(id);
        if (currentStore == null) {
            return new ArrayList<>();
        }

        // 确定主店id：如果当前是子店，先找到它的主店；如果是主店，直接用自身id
        Long mainStoreId;
        if ("sub".equals(currentStore.getType())) {
            // 子店：查询对应的主店信息，获取主店id
            Store mainStore = storeMapper.selectNoSysStoreById(currentStore.getParentId());
            mainStoreId = mainStore != null ? mainStore.getId() : currentStore.getId();
        } else {
            // 主店：直接用自身id
            mainStoreId = currentStore.getId();
        }

        // 查询主店的所有子店（包含主店自身）
        List<Store> chainStores = new ArrayList<>();

        // 先添加主店
        Store mainStore = storeMapper.selectNoSysStoreById(mainStoreId);
        if (mainStore != null) {
            chainStores.add(mainStore);
        }

        // 查询所有子店并添加到列表
        List<Store> subStores = storeMapper.selectSonsById(mainStoreId); // 修改为查询多个子店
        if (subStores != null && !subStores.isEmpty()) {
            chainStores.addAll(subStores);
        }

        return chainStores;
    }


    /**
     * 查询子店面
     *
     * @param id 店面主键
     * @return 店面
     */
    @Override
    public Store selectSonById(Long id)
    {
        return storeMapper.selectStoreById(id);
    }

    /**
     * 查询店面列表
     * 
     * @param store 店面
     * @return 店面
     */
    @Override
    public List<Store> selectStoreList(Store store)
    {
        return storeMapper.selectStoreList(store);
    }

    /**
     * 新增店面
     * 
     * @param store 店面
     * @return 结果
     */
    @Override
    public int insertStore(Store store)
    {
        return storeMapper.insertStore(store);
    }

    /**
     * 修改店面
     * 
     * @param store 店面
     * @return 结果
     */
    @Override
    public int updateStore(Store store)
    {
        return storeMapper.updateStore(store);
    }

    /**
     * 批量删除店面
     * 
     * @param ids 需要删除的店面主键
     * @return 结果
     */
    @Override
    public int deleteStoreByIds(Long[] ids)
    {
        return storeMapper.deleteStoreByIds(ids);
    }

    /**
     * 删除店面信息
     * 
     * @param id 店面主键
     * @return 结果
     */
    @Override
    public int deleteStoreById(Long id)
    {
        return storeMapper.deleteStoreById(id);
    }

    @Override
    @Transactional
    public int insertStoreAndDept(Store store) {
        storeMapper.insertStore(store);

        SysDept sysDept = new SysDept();
        sysDept.setStoreId(store.getId());
        sysDept.setParentId(100L);
        sysDept.setAncestors("0,100");
        sysDept.setDeptName(store.getName());
        sysDept.setOrderNum(0);
        SysPost sysPost = new SysPost();
        sysPost.setStoreId(store.getId());
        sysPost.setPostName("美发师");
        sysPost.setPostCode("hairdresser");
        sysPost.setPostSort(0);
        sysPost.setStatus("0");
        sysPost.setCreateBy(getUsername());
        sysPost.setCreateTime(new Date());
        sysPostService.insertPost(sysPost);

        if (!StringUtils.isEmpty(store.getContactPerson())) {
            sysDept.setLeader(store.getContactPerson());
        }

        if (!StringUtils.isEmpty(store.getContactPhone())) {
            sysDept.setPhone(store.getContactPhone());
        }

            return sysDeptService.insertDept(sysDept);
        }

    @Override
    @Transactional
    public int updateStoreAndDept(Store store) {
       int count = storeMapper.updateStore(store);
        sysDeptService.updateDeptByStoreId(store);
        return count;
    }

    @Override
    public List<Store> unallocated() {
        return storeMapper.unallocated();
    }

    @Override
    public Store selectStoreInfo(Long id) {
        Store store = storeMapper.selectById(id);
        if (store!=null && store.getParentId()!=null) {
            store.setRootStore(storeMapper.selectById(store.getParentId()));
        }
        if (store!=null && store.getLogoId()!=null){
            store.setLogo(uploadedImageService.selectUploadedImageById(store.getLogoId()));
        }
        if (store!=null && store.getPhotoIds()!=null){
            store.setPhotoList(uploadedImageService.listByIds(Arrays.asList(store.getPhotoIds().split(","))));
        }

        return store;
    }

    @Override
    public Map<String, Object> selectStoreUserInfo(Long userId) {
        SysUser user = sysUserService.selectUserById(userId);
        Map< String,Object> map = new HashMap<>();
        map.put("employee",user);
        EmployeeInfo employeeInfo = employeeInfoService.getOne(new QueryWrapper<EmployeeInfo>().eq("id", userId));
        map.put("employeeInfo",employeeInfo);
        if (employeeInfo != null){
            if (employeeInfo.getAvatar() != null){
                map.put("avatar",uploadedImageService.getById(employeeInfo.getAvatar()));
            }
            if (employeeInfo.getPhoto() != null){
                map.put("photo",uploadedImageService.getById((employeeInfo.getPhoto())));
            }
        }
        return map;
    }

    /**
     * 获取所有店铺（不分页）
     *
     * @return 店面集合
     */
    @Override
    public List<Store> listAllStores() {
        return storeMapper.selectStoreList(new Store());
    }

}
