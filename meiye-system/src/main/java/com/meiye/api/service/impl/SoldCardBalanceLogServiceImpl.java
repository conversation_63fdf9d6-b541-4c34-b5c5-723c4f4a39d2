package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.SoldCardBalanceLog;
import com.meiye.api.mapper.SoldCardBalanceLogMapper;
import com.meiye.api.service.SoldCardBalanceLogService;
@Service
public class SoldCardBalanceLogServiceImpl extends ServiceImpl<SoldCardBalanceLogMapper, SoldCardBalanceLog> implements SoldCardBalanceLogService{

}
