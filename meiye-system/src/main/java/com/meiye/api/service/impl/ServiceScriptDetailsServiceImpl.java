package com.meiye.api.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.ServiceScriptDetailsMapper;
import com.meiye.api.domain.ServiceScriptDetails;
import com.meiye.api.service.ServiceScriptDetailsService;

/**
 * 标准话术详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class ServiceScriptDetailsServiceImpl
        extends ServiceImpl<ServiceScriptDetailsMapper, ServiceScriptDetails>
        implements ServiceScriptDetailsService
{
    @Autowired
    private ServiceScriptDetailsMapper serviceScriptDetailsMapper;

    /**
     * 查询标准话术详情
     * 
     * @param serviceScriptId 标准话术详情主键
     * @return 标准话术详情
     */
    @Override
    public ServiceScriptDetails selectServiceScriptDetailsByServiceScriptId(Long serviceScriptId)
    {
        return serviceScriptDetailsMapper.selectServiceScriptDetailsByServiceScriptId(serviceScriptId);
    }

    /**
     * 查询标准话术详情列表
     * 
     * @param serviceScriptDetails 标准话术详情
     * @return 标准话术详情
     */
    @Override
    public List<ServiceScriptDetails> selectServiceScriptDetailsList(ServiceScriptDetails serviceScriptDetails)
    {
        return serviceScriptDetailsMapper.selectServiceScriptDetailsList(serviceScriptDetails);
    }

    /**
     * 新增标准话术详情
     * 
     * @param serviceScriptDetails 标准话术详情
     * @return 结果
     */
    @Override
    public int insertServiceScriptDetails(ServiceScriptDetails serviceScriptDetails)
    {
        return serviceScriptDetailsMapper.insertServiceScriptDetails(serviceScriptDetails);
    }

    /**
     * 修改标准话术详情
     * 
     * @param serviceScriptDetails 标准话术详情
     * @return 结果
     */
    @Override
    public int updateServiceScriptDetails(ServiceScriptDetails serviceScriptDetails)
    {
        return serviceScriptDetailsMapper.updateServiceScriptDetails(serviceScriptDetails);
    }

    /**
     * 批量删除标准话术详情
     * 
     * @param serviceScriptIds 需要删除的标准话术详情主键
     * @return 结果
     */
    @Override
    public int deleteServiceScriptDetailsByServiceScriptIds(Long[] serviceScriptIds)
    {
        return serviceScriptDetailsMapper.deleteServiceScriptDetailsByServiceScriptIds(serviceScriptIds);
    }

    /**
     * 删除标准话术详情信息
     * 
     * @param serviceScriptId 标准话术详情主键
     * @return 结果
     */
    @Override
    public int deleteServiceScriptDetailsByServiceScriptId(Long serviceScriptId)
    {
        return serviceScriptDetailsMapper.deleteServiceScriptDetailsByServiceScriptId(serviceScriptId);
    }
}
