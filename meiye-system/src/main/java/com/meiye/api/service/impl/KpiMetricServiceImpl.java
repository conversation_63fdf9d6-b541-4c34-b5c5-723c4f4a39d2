package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.KpiMetric;
import com.meiye.api.mapper.KpiMetricMapper;
import com.meiye.api.service.KpiMetricService;
@Service
public class KpiMetricServiceImpl
        extends ServiceImpl<KpiMetricMapper, KpiMetric>
        implements KpiMetricService{
    @Autowired
    private KpiMetricMapper kpiMetricMapper;

    /**
     * 查询KPI指标
     *
     * @param id KPI指标主键
     * @return KPI指标
     */
    @Override
    public KpiMetric selectKpiMetricById(Long id)
    {
        return kpiMetricMapper.selectKpiMetricById(id);
    }

    /**
     * 查询KPI指标列表
     *
     * @param kpiMetric KPI指标
     * @return KPI指标
     */
    @Override
    public List<KpiMetric> selectKpiMetricList(KpiMetric kpiMetric)
    {
        return kpiMetricMapper.selectKpiMetricList(kpiMetric);
    }

    /**
     * 新增KPI指标
     *
     * @param kpiMetric KPI指标
     * @return 结果
     */
    @Override
    public int insertKpiMetric(KpiMetric kpiMetric)
    {
        return kpiMetricMapper.insertKpiMetric(kpiMetric);
    }

    /**
     * 修改KPI指标
     *
     * @param kpiMetric KPI指标
     * @return 结果
     */
    @Override
    public int updateKpiMetric(KpiMetric kpiMetric)
    {
        return kpiMetricMapper.updateKpiMetric(kpiMetric);
    }

    /**
     * 批量删除KPI指标
     *
     * @param ids 需要删除的KPI指标主键
     * @return 结果
     */
    @Override
    public int deleteKpiMetricByIds(Long[] ids)
    {
        return kpiMetricMapper.deleteKpiMetricByIds(ids);
    }

    /**
     * 删除KPI指标信息
     *
     * @param id KPI指标主键
     * @return 结果
     */
    @Override
    public int deleteKpiMetricById(Long id)
    {
        return kpiMetricMapper.deleteKpiMetricById(id);
    }

}
