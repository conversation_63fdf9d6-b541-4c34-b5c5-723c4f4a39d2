package com.meiye.api.service.impl;

import com.meiye.api.domain.Product;
import com.meiye.api.domain.Store;
import com.meiye.api.domain.StoreProductCommission;
import com.meiye.api.mapper.ProductMapper;
import com.meiye.api.mapper.StoreProductCommissionMapper;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.ProductCategory;
import com.meiye.api.mapper.ProductCategoryMapper;
import com.meiye.api.service.ProductCategoryService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class ProductCategoryServiceImpl extends ServiceImpl<ProductCategoryMapper, ProductCategory> implements ProductCategoryService{
    @Resource
    private ProductCategoryMapper productCategoryMapper;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private StoreProductCommissionMapper storeProductCommissionMapper;

    @Override
    public List<ProductCategory> selectProductCategoryList(ProductCategory productCategory) {
        storeProductCommissionMapper.selectStoreProductCommissionById(productCategory.getId());

        return productCategoryMapper.selectProductCategoryList(productCategory);
    }

    @Override
    public int updateProductCategoryList(ProductCategory productCategory){
        return productCategoryMapper.updateProductCategoryList(productCategory);
    }

    @Override
    public ProductCategory selectProductCategoryById(Long id){
        return productCategoryMapper.selectProductCategoryById(id);
    }

    /**
     * 新增销售物分类
     *
     * @param productCategory 销售物分类
     * @return 结果
     */
    @Override
    public int insertProductCategory(ProductCategory productCategory)
    {
        int mainResult=productCategoryMapper.insertProductCategory(productCategory);
        Product product=new Product();
        product.setCategoryId(productCategory.getId());
        return mainResult;
    }

    /**
     * 删除销售物分类信息
     *
     * @param id 销售物分类主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteProductCategoryById(Long id)
    {
        Product product = new Product();
        product.setCategoryId(id);
        List<Product> productList = productMapper.selectProductList(product);

        if (productList != null && !productList.isEmpty()) {
            Product relatedProduct = productList.get(0);
            productMapper.deleteProductById(relatedProduct.getId());
        }
        return productCategoryMapper.deleteProductCategoryById(id);
    }

    /**
     * 批量删除销售物分类
     *
     * @param ids 需要删除的销售物分类主键
     * @return 结果
     */
    @Override
    public int deleteProductCategoryByIds(Long[] ids)
    {
        Product product = new Product();
        product.setCategoryId(ids[0]);
        List<Product> productList = productMapper.selectProductList(product);

        if (productList != null && !productList.isEmpty()) {
            Product relatedProduct = productList.get(0);
            productMapper.deleteProductById(relatedProduct.getId());
        }
        return productCategoryMapper.deleteStoreByIds(ids);
    }
}
