package com.meiye.api.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.mapper.ServiceRecordMapper;
import com.meiye.api.domain.ServiceRecord;
import com.meiye.api.service.ServiceRecordService;
@Service
public class ServiceRecordServiceImpl
        extends ServiceImpl<ServiceRecordMapper, ServiceRecord>
        implements ServiceRecordService{


    @Autowired
    private ServiceRecordMapper serviceRecordMapper;

    /**
     * 查询服务记录
     *
     * @param id 服务记录主键
     * @return 服务记录
     */
    @Override
    public ServiceRecord selectServiceRecordById(Long id)
    {
        return serviceRecordMapper.selectServiceRecordById(id);
    }

    /**
     * 查询服务记录列表
     *
     * @param serviceRecord 服务记录
     * @return 服务记录
     */
    @Override
    public List<ServiceRecord> selectServiceRecordList(ServiceRecord serviceRecord)
    {
        return serviceRecordMapper.selectServiceRecordList(serviceRecord);
    }

    /**
     * 新增服务记录
     *
     * @param serviceRecord 服务记录
     * @return 结果
     */
    @Override
    public int insertServiceRecord(ServiceRecord serviceRecord)
    {
        return serviceRecordMapper.insertServiceRecord(serviceRecord);
    }

    /**
     * 修改服务记录
     *
     * @param serviceRecord 服务记录
     * @return 结果
     */
    @Override
    public int updateServiceRecord(ServiceRecord serviceRecord)
    {
        return serviceRecordMapper.updateServiceRecord(serviceRecord);
    }

    /**
     * 批量删除服务记录
     *
     * @param ids 需要删除的服务记录主键
     * @return 结果
     */
    @Override
    public int deleteServiceRecordByIds(Long[] ids)
    {
        return serviceRecordMapper.deleteServiceRecordByIds(ids);
    }

    /**
     * 删除服务记录信息
     *
     * @param id 服务记录主键
     * @return 结果
     */
    @Override
    public int deleteServiceRecordById(Long id)
    {
        return serviceRecordMapper.deleteServiceRecordById(id);
    }

}
