package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 特性类型表
 */
@ApiModel(description="特性类型表")
@Data
@TableName(value = "member_feature_type")
public class MemberFeatureType {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 主店面ID
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="主店面ID")
    private Long rootStoreId;

    /**
     * 类型名称（如"发质"）
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="类型名称（如'发质'）")
    private String name;

    /**
     * 默认特性
     */
    @TableField(value = "is_default")
    @ApiModelProperty(value="默认特性")
    private Boolean isDefault;
    /**
     * 主店面名称（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="主店面名称")
    private String rootStoreName;
}