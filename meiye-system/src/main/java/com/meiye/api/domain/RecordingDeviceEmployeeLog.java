package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 录音设备分配记录表
 */
@ApiModel(description="录音设备分配记录表")
@Data
@TableName(value = "recording_device_employee_log")
public class RecordingDeviceEmployeeLog {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 所属店面
     */
    @TableField(value = "store_id")
    @Excel(name = "所属店面")
    @ApiModelProperty(value="所属店面")
    private Long storeId;

    /**
     * 设备ID
     */
    @TableField(value = "device_id")
    @Excel(name = "设备ID")
    @ApiModelProperty(value="设备ID")
    private Long deviceId;

    /**
     * 员工ID
     */
    @TableField(value = "employee_id")
    @Excel(name = "员工ID")
    @ApiModelProperty(value="员工ID")
    private Long employeeId;

    /**
     * 操作人id
     */
    @TableField(value = "created_by")
    @Excel(name = "操作人id")
    @ApiModelProperty(value="操作人id")
    private String createdBy;

    /**
     * 操作时间
     */
    @TableField(value = "created_at")
    @Excel(name = "操作时间")
    @ApiModelProperty(value="操作时间")
    private Date createdAt;

    /**
     * 操作
     */
    @TableField(value = "action")
    @Excel(name = "操作")
    @ApiModelProperty(value="操作")
    private String action;
}