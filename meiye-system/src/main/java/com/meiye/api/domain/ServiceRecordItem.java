package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 服务记录的话术单元信息表
 */
@ApiModel(description="服务记录的话术单元信息表")
@Data
@TableName(value = "service_record_item")
public class ServiceRecordItem {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="记录ID")
    private Long id;

    /**
     * 关联服务记录ID
     */
    @TableField(value = "service_record_id")
    @ApiModelProperty(value="关联服务记录ID")
    private Long serviceRecordId;

    /**
     * 关联评价设置ID
     */
    @TableField(value = "service_evaluation_setting_id")
    @ApiModelProperty(value="关联评价设置ID")
    private Long serviceEvaluationSettingId;

    /**
     * 录音转文本
     */
    @TableField(value = "audio_text")
    @ApiModelProperty(value="录音转文本")
    private String audioText;

    /**
     * AI打分
     */
    @TableField(value = "ai_level")
    @ApiModelProperty(value="AI打分")
    private Object aiLevel;

    /**
     * 大模型建议反馈
     */
    @TableField(value = "ai_evaluation")
    @ApiModelProperty(value="大模型建议反馈")
    private String aiEvaluation;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private Date createdAt;
}