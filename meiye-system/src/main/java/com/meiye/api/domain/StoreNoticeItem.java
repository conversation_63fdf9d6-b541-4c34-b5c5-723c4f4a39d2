package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 店铺公告发布记录表
 */
@ApiModel(description="店铺公告发布记录表")
@Data
@TableName(value = "store_notice_item")
public class StoreNoticeItem {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 店铺公告id
     */
    @TableField(value = "store_notice_id")
    @ApiModelProperty(value="店铺公告id")
    private Long storeNoticeId;

    /**
     * 接受公告的店铺ID
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="接受公告的店铺ID")
    private Long storeId;

    /**
     * 接收公告的员工ID
     */
    @TableField(value = "employee_id")
    @ApiModelProperty(value="接收公告的员工ID")
    private Long employeeId;

    /**
     * 是否已读
     */
    @TableField(value = "is_read")
    @ApiModelProperty(value="是否已读")
    private Boolean isRead;

    /**
     * 阅读时间
     */
    @TableField(value = "read_time")
    @ApiModelProperty(value="阅读时间")
    private Date readTime;
}