package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 产品子项表
 */
@ApiModel(description="产品子项表")
@Data
@TableName(value = "product_item")
public class ProductItem {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 主销售物ID
     */
    @TableField(value = "main_product_id")
    @ApiModelProperty(value="主销售物ID")
    private Long mainProductId;

    /**
     * 子销售物ID
     */
    @TableField(value = "sub_product_id")
    @ApiModelProperty(value="子销售物ID")
    private Long subProductId;

    /**
     * 视同售价，用于套餐内容物进行按比例提成时的售价参照
     */
    @TableField(value = "equal_price")
    @ApiModelProperty(value="视同售价，用于套餐内容物进行按比例提成时的售价参照")
    private BigDecimal equalPrice;

    /**
     * 包含数量
     */
    @TableField(value = "quantity")
    @ApiModelProperty(value="包含数量")
    private Integer quantity;
}