package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 店面实际销售物定义变更记录
 */
@ApiModel(description="店面实际销售物定义变更记录")
@Data
@TableName(value = "store_product_log")
public class StoreProductLog {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="ID")
    private Long id;

    /**
     * 适用的店面ID
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="适用的店面ID")
    private Long storeId;

    /**
     * 店面销售物ID
     */
    @TableField(value = "store_product_id")
    @ApiModelProperty(value="店面销售物ID")
    private Long storeProductId;

    /**
     * 变更类型
     */
    @TableField(value = "`type`")
    @ApiModelProperty(value="变更类型")
    private Object type;

    /**
     * 原始数据JSON
     */
    @TableField(value = "origin_json")
    @ApiModelProperty(value="原始数据JSON")
    private String originJson;

    /**
     * 修改后的数据JSON
     */
    @TableField(value = "current_json")
    @ApiModelProperty(value="修改后的数据JSON")
    private String currentJson;

    /**
     * 修改时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="修改时间")
    private Date createdAt;

    /**
     * 修改的员工ID
     */
    @TableField(value = "created_by_employee_id")
    @ApiModelProperty(value="修改的员工ID")
    private Long createdByEmployeeId;
}