package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * 会员特性变更记录表
 */
@ApiModel(description="会员特性变更记录表")
@Data
@TableName(value = "member_feature_log")
public class MemberFeatureLog {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     *  特性id
     */
    @TableField(value = "feature_type_id")
    @ApiModelProperty(value=" 特性id")
    private Long featureTypeId;

    /**
     * 原特性名称
     */
    @TableField(value = "origin_feature_name")
    @ApiModelProperty(value="原特性名称")
    private String originFeatureName;

    /**
     * 当前特性名称
     */
    @TableField(value = "current_feature_name")
    @ApiModelProperty(value="当前特性名称")
    private String currentFeatureName;

    /**
     * 原特性值（如"油性"）
     */
    @TableField(value = "origin_value")
    @ApiModelProperty(value="原特性值（如'油性'）")
    private String originValue;

    /**
     * 当前特性值（如"干性性"）
     */
    @TableField(value = "current_value")
    @ApiModelProperty(value="当前特性值（如'干性性'）")
    private String currentValue;

    /**
     * 注册会员信息的员工
     */
    @TableField(value = "created_by_employee_id")
    @ApiModelProperty(value="注册会员信息的员工")
    private Long createdByEmployeeId;

    /**
     * 注册时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="注册时间")
    private Date createdAt;

    /**
     * 请求参数
     */
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();

    public MemberFeatureLog() {
        this.params = new HashMap<>();
    }
}
