package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * KPI设置表
 */
@ApiModel(description="KPI设置表")
@Data
@TableName(value = "kpi_setting")
public class KpiSetting {
    /**
     * 设置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="设置ID")
    private Long id;

    /**
     * 主店面
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="主店面")
    private Long rootStoreId;

    /**
     * 目标店面
     */
    @TableField(value = "target_store_id")
    @ApiModelProperty(value="目标店面")
    private Long targetStoreId;

    /**
     * 目标职位
     */
    @TableField(value = "target_position_id")
    @ApiModelProperty(value="目标职位")
    private Long targetPositionId;

    /**
     * 目标员工
     */
    @TableField(value = "target_employee_id")
    @ApiModelProperty(value="目标员工")
    private Long targetEmployeeId;

    /**
     * KPI指标编码
     */
    @TableField(value = "metric_code")
    @ApiModelProperty(value="KPI指标编码")
    private String metricCode;

    /**
     * 目标值
     */
    @TableField(value = "target_value")
    @ApiModelProperty(value="目标值")
    private BigDecimal targetValue;

    /**
     * 考核周期（格式：YYYY-MM）
     */
    @TableField(value = "`cycle`")
    @ApiModelProperty(value="考核周期（格式：YYYY-MM）")
    private String cycle;

    /**
     * 关联销售物ID
     */
    @TableField(value = "ref_product_id")
    @ApiModelProperty(value="关联销售物ID")
    private Long refProductId;

    /**
     * 状态
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value="状态")
    private Object status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private Date createdAt;
}