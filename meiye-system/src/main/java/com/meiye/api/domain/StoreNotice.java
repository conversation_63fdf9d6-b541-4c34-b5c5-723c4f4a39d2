package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 店铺公告
 */
@ApiModel(description="店铺公告")
@Data
@TableName(value = "store_notice")
public class StoreNotice extends BaseEntity {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 发布店铺id
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="发布店铺id")
    private Long storeId;

    /**
     * 标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value="标题")
    private String title;

    /**
     * 内容
     */
    @TableField(value = "content")
    @ApiModelProperty(value="内容")
    private String content;

    /**
     * 发布时间
     */
    @TableField(value = "public_time")
    @ApiModelProperty(value="发布时间")
    private Date publicTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private Date createdAt;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    @TableField(exist = false)
    private Long[] employeeIds;

    @TableField(exist = false)
    private Long[] storeIds;
}