package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 月出勤统计表
 */
@ApiModel(description="月出勤统计表")
@Data
@TableName(value = "monthly_attendance")
public class MonthlyAttendance {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="记录ID")
    private Long id;

    /**
     * 员工ID
     */
    @TableField(value = "employee_id")
    @ApiModelProperty(value="员工ID")
    private Long employeeId;

    /**
     * 年份
     */
    @TableField(value = "`year`")
    @ApiModelProperty(value="年份")
    private Integer year;

    /**
     * 月份（1-12）
     */
    @TableField(value = "`month`")
    @ApiModelProperty(value="月份（1-12）")
    private Integer month;

    /**
     * 迟到次数
     */
    @TableField(value = "be_late_times")
    @ApiModelProperty(value="迟到次数")
    private Integer beLateTimes;

    /**
     * 早退次数
     */
    @TableField(value = "leave_early_times")
    @ApiModelProperty(value="早退次数")
    private Integer leaveEarlyTimes;

    /**
     * 外勤打卡次数
     */
    @TableField(value = "out_area_times")
    @ApiModelProperty(value="外勤打卡次数")
    private Integer outAreaTimes;

    /**
     * 出勤天数（打过卡的天数）
     */
    @TableField(value = "job_days")
    @ApiModelProperty(value="出勤天数（打过卡的天数）")
    private Integer jobDays;

    /**
     * 正常出勤天数（早晚打卡且无迟到早退）
     */
    @TableField(value = "good_days")
    @ApiModelProperty(value="正常出勤天数（早晚打卡且无迟到早退）")
    private Integer goodDays;
}