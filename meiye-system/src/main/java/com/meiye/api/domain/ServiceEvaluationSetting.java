package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.meiye.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 服务评价设置表
 */
@ApiModel(description="服务评价设置表")
@Data
@TableName(value = "service_evaluation_setting")
public class ServiceEvaluationSetting {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="记录ID")
    private Long id;

    /**
     * 评价标准名称，与root_store_id以及store_id组合为唯一键
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="评价标准名称，与root_store_id以及store_id组合为唯一键")
    private String name;

    /**
     * 主店面ID，为NULL表示通用设置
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="主店面ID，为NULL表示通用设置")
    private Long rootStoreId;

    /**
     * 店面ID
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="店面ID")
    private Long storeId;

    /**
     * 部门ID
     */
    @TableField(value = "department_id")
    @ApiModelProperty(value="部门ID")
    private Long departmentId;

    /**
     * 职务ID
     */
    @TableField(value = "position_id")
    @ApiModelProperty(value="职务ID")
    private Long positionId;

    /**
     * 状态enabled-启动，disabled-停用，draft-草稿
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value="状态enabled-启动，disabled-停用，draft-草稿")
    private String status;

    /**
     * 总权重
     */
    @TableField(value = "total_weight")
    @ApiModelProperty(value="总权重")
    private Double totalWeight;

    /**
     * 创建人（如果root_store_id为NULL则应该是由运营方创建的）
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人（如果root_store_id为NULL则应该是由运营方创建的）")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /**
     * 主店面名称（用于前端展示）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="主店面名称")
    @Excel(name = "主店面名称")
    private String rootStoreName;

    /**
     * 店面名称（用于前端展示）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="店面名称")
    @Excel(name = "店面名称")
    private String storeName;
}