package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 会员表
 */
@ApiModel(description="会员表")
@Data
@TableName(value = "`member`")
public class Member extends BaseEntity {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;
    @TableField(value = "sn")
    @ApiModelProperty(value="会员编号")
    private String sn;

    /**
     * 会员等级ID
     */
    @TableField(value = "member_level_id")
    @ApiModelProperty(value="会员等级ID")
    private Long memberLevelId;

    /**
     * 主店面ID
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="主店面ID")
    private Long rootStoreId;

    /**
     * 注册店面
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="注册店面")
    private Long storeId;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="姓名")
    private String name;

    /**
     * 性别
     */
    @TableField(value = "gender")
    @ApiModelProperty(value="性别")
    private Object gender;

    /**
     * 手机号（联合root_store_id构成唯一索引）
     */
    @TableField(value = "phone")
    @ApiModelProperty(value="手机号（联合root_store_id构成唯一索引）")
    private String phone;

    /**
     * 登录密码
     */
    @TableField(value = "`password`")
    @ApiModelProperty(value="登录密码")
    private String password;

    /**
     * 微信身份码
     */
    @TableField(value = "oauth")
    @ApiModelProperty(value="微信身份码")
    private String oauth;

    /**
     * 直充余额（备用
     */
    @TableField(value = "real_balance")
    @ApiModelProperty(value="直充余额（备用")
    private BigDecimal realBalance;
    /**
     * 会员等级信息
     */
    @TableField(exist = false)
    @ApiModelProperty(value="会员等级信息")
    private MemberLevel memberLevel;

    /**
     * 最近订单
     */
    @TableField(exist = false)
    @ApiModelProperty(value="最近订单")
    private SaleOrder lastOrder;

    /**
     * 会员标签列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value="会员标签列表")
    private List<MemberFeature> tags;

    /**
     * 会员推荐产品列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value="会员推荐产品列表")
    private List<MemberProduct> advanceProductList;

    /**
     * 开始注册时间（查询用）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="开始注册时间（查询用）")
    private Date beginCreatedAt;

    /**
     * 结束注册时间（查询用）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="结束注册时间（查询用）")
    private Date endCreatedAt;
    
    // 相应的getter和setter方法
    public MemberLevel getMemberLevel() {
        return memberLevel;
    }

    public void setMemberLevel(MemberLevel memberLevel) {
        this.memberLevel = memberLevel;
    }

    public SaleOrder getLastOrder() {
        return lastOrder;
    }

    public BigDecimal getGiftBalance() {
        return giftBalance;
    }

    public void setGiftBalance(BigDecimal giftBalance) {
        this.giftBalance = giftBalance;
    }

    public String getCreatedByEmployeeName() {
        return createdByEmployeeName;
    }

    public void setCreatedByEmployeeName(String createdByEmployeeName) {
        this.createdByEmployeeName = createdByEmployeeName;
    }

    public String getMemberLevelName() {
        return memberLevelName;
    }

    public void setMemberLevelName(String memberLevelName) {
        this.memberLevelName = memberLevelName;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Long getCreatedByEmployeeId() {
        return createdByEmployeeId;
    }

    public void setCreatedByEmployeeId(Long createdByEmployeeId) {
        this.createdByEmployeeId = createdByEmployeeId;
    }

    public Boolean getActive() {
        return isActive;
    }

    public void setActive(Boolean active) {
        isActive = active;
    }

    public Date getLastConsumption() {
        return lastConsumption;
    }

    public void setLastConsumption(Date lastConsumption) {
        this.lastConsumption = lastConsumption;
    }

    public BigDecimal getTotalCash() {
        return totalCash;
    }

    public void setTotalCash(BigDecimal totalCash) {
        this.totalCash = totalCash;
    }

    public BigDecimal getRealBalance() {
        return realBalance;
    }

    public void setRealBalance(BigDecimal realBalance) {
        this.realBalance = realBalance;
    }

    public String getOauth() {
        return oauth;
    }

    public void setOauth(String oauth) {
        this.oauth = oauth;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Object getGender() {
        return gender;
    }

    public void setGender(Object gender) {
        this.gender = gender;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getRootStoreId() {
        return rootStoreId;
    }

    public void setRootStoreId(Long rootStoreId) {
        this.rootStoreId = rootStoreId;
    }

    public Long getMemberLevelId() {
        return memberLevelId;
    }

    public void setMemberLevelId(Long memberLevelId) {
        this.memberLevelId = memberLevelId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 赠送余额（备用
     */
    @TableField(value = "gift_balance")
    @ApiModelProperty(value="赠送余额（备用")
    private BigDecimal giftBalance;

    /**
     * 现金消费总额
     */
    @TableField(value = "total_cash")
    @ApiModelProperty(value="现金消费总额")
    private BigDecimal totalCash;

    /**
     * 最近一次消费日期
     */
    @TableField(value = "last_consumption")
    @ApiModelProperty(value="最近一次消费日期")
    private Date lastConsumption;

    /**
     * 是否为活跃会员
     */
    @TableField(value = "is_active")
    @ApiModelProperty(value="是否为活跃会员")
    private Boolean isActive;

    /**
     * 注册会员信息的员工
     */
    @TableField(value = "created_by_employee_id")
    @ApiModelProperty(value="注册会员信息的员工")
    private Long createdByEmployeeId;

    /**
     * 注册时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="注册时间")
    private Date createdAt;

    /**
     * 会员等级名称（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="会员等级名称")
    private String memberLevelName;

    /**
     * 注册员工姓名（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="注册员工姓名")
    private String createdByEmployeeName;
    
    /**
     * 主店面名称（用于前端展示）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="主店面名称")
    private String rootStoreName;
    
    /**
     * 注册店面名称（用于前端展示）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="注册店面名称")
    private String storeName;

    @TableField(exist = false)
    @ApiModelProperty(value="主店面ID")
    private String getStoreRootId;
}
