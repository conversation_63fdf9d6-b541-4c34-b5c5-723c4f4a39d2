package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 已售消费卡表
 */
@ApiModel(description="已售消费卡表")
@Data
@TableName(value = "sold_card")
public class SoldCard {
    /**
     * 实例ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="实例ID")
    private Long id;

    /**
     * 卡片的店面销售物ID
     */
    @TableField(value = "store_product_id")
    @ApiModelProperty(value="卡片的店面销售物ID")
    private Long storeProductId;

    /**
     * 所属会员ID
     */
    @TableField(value = "member_id")
    @ApiModelProperty(value="所属会员ID")
    private Long memberId;

    /**
     * 卡片唯一编码
     */
    @TableField(value = "card_sn")
    @ApiModelProperty(value="卡片唯一编码")
    private String cardSn;

    /**
     * 当前直充余额
     */
    @TableField(value = "real_balance")
    @ApiModelProperty(value="当前直充余额")
    private BigDecimal realBalance;

    /**
     * 当前赠送余额
     */
    @TableField(value = "gift_balance")
    @ApiModelProperty(value="当前赠送余额")
    private BigDecimal giftBalance;

    /**
     * 消费时直充与赠送余额扣除比例
     */
    @TableField(value = "real_gift_ratio")
    @ApiModelProperty(value="消费时直充与赠送余额扣除比例")
    private BigDecimal realGiftRatio;

    /**
     * 折扣比例
     */
    @TableField(value = "discount_ratio")
    @ApiModelProperty(value="折扣比例")
    private BigDecimal discountRatio;

    /**
     * 过期日期
     */
    @TableField(value = "expire_date")
    @ApiModelProperty(value="过期日期")
    private Date expireDate;

    /**
     * 状态: 0-有效 1-已过期/已废弃/已消费完毕
     */
    @TableField(value = "disabled")
    @ApiModelProperty(value="状态: 0-有效 1-已过期/已废弃/已消费完毕")
    private Boolean disabled;

    /**
     * 注册会员信息的员工ID
     */
    @TableField(value = "created_by_employee_id")
    @ApiModelProperty(value="注册会员信息的员工ID")
    private Long createdByEmployeeId;

    /**
     * 注册时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="注册时间")
    private Date createdAt;

    /**
     * 开始创建时间
     */
    @TableField(exist = false)
    @ApiModelProperty(value="开始创建时间")
    private Date beginCreatedAt;

    /**
     * 结束创建时间
     */
    @TableField(exist = false)
    @ApiModelProperty(value="结束创建时间")
    private Date endCreatedAt;

    /**
     * 卡类型查询参数
     */
    @TableField(exist = false)
    @ApiModelProperty(value="卡类型查询参数")
    private String cardType;
    
    /**
     * 会员卡名称查询参数
     */
    @TableField(exist = false)
    @ApiModelProperty(value="会员卡名称查询参数")
    private String productName;
    
    /**
     * 最小直充金额查询参数
     */
    @TableField(exist = false)
    @ApiModelProperty(value="最小直充金额查询参数")
    private BigDecimal minRealBalance;
    
    /**
     * 最大直充金额查询参数
     */
    @TableField(exist = false)
    @ApiModelProperty(value="最大直充金额查询参数")
    private BigDecimal maxRealBalance;
}