package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 为会员推荐过的项目记录
 */
@ApiModel(description="为会员推荐过的项目记录")
@Data
@TableName(value = "member_product_advance")
public class MemberProductAdvance {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 会员ID
     */
    @TableField(value = "member_id")
    @ApiModelProperty(value="会员ID")
    private Long memberId;

    /**
     * 推荐项目
     */
    @TableField(value = "store_product_id")
    @ApiModelProperty(value="推荐项目")
    private Long storeProductId;

    /**
     * 建议间隔时长
     */
    @TableField(value = "cycle_length")
    @ApiModelProperty(value="建议间隔时长")
    private String cycleLength;

    /**
     * 推荐是否成功
     */
    @TableField(value = "accept")
    @ApiModelProperty(value="推荐是否成功")
    private Boolean accept;

    /**
     * 是否明确拒绝
     */
    @TableField(value = "refuse")
    @ApiModelProperty(value="是否明确拒绝")
    private Boolean refuse;

    /**
     * 填写备注
     */
    @TableField(value = "description")
    @ApiModelProperty(value="填写备注")
    private String description;

    /**
     * 注册会员信息的员工
     */
    @TableField(value = "created_by_employee_id")
    @ApiModelProperty(value="注册会员信息的员工")
    private Long createdByEmployeeId;

    /**
     * 注册时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="注册时间")
    private Date createdAt;
    
    /**
     * 注册员工姓名（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="注册员工姓名")
    private String createdByEmployeeName;
    
    /**
     * 会员姓名（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="会员姓名")
    private String memberName;
}