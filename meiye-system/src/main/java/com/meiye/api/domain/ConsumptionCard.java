package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 消费卡补充定义表
 */
@ApiModel(description="消费卡补充定义表")
@Data
@TableName(value = "consumption_card")
public class ConsumptionCard {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    @Excel(name = "ID")
    private Long id;

    /**
     * 卡片的销售物ID
     */
    @TableField(value = "product_id")
    @ApiModelProperty(value="卡片的销售物ID")
    @Excel(name = "卡片的销售物ID")
    private Long productId;

    /**
     * 卡类型:balance-储值卡, discount-优惠卡,times-次卡,experience-体验卡
     */
    @TableField(value = "`type`")
    @ApiModelProperty(value="卡类型:balance-储值卡, discount-优惠卡,times-次卡,experience-体验卡")
    @Excel(name = "卡类型")
    private String type;

    /**
     * 有效期（天）
     */
    @TableField(value = "validity_days")
    @ApiModelProperty(value="有效期（天）")
    @Excel(name = "有效期(天)")
    private Integer validityDays;

    /**
     * 直充余额（储值卡直充余额默认等于售价）
     */
    @TableField(value = "real_balance")
    @Excel(name = "直充余额")
    @ApiModelProperty(value="直充余额（储值卡直充余额默认等于售价）")
    private BigDecimal realBalance;

    /**
     * 赠送余额
     */
    @TableField(value = "gift_balance")
    @ApiModelProperty(value="赠送余额")
    @Excel(name = "赠送余额")
    private BigDecimal giftBalance;

    /**
     * 消费时直充余额与赠送余额的扣除比例（默认为：赠送余额/(直充余额+赠送余额）
     */
    @TableField(value = "real_gift_ratio")
    @ApiModelProperty(value="消费时直充余额与赠送余额的扣除比例（默认为：赠送余额/(直充余额+赠送余额）")
    @Excel(name = "消费时直充余额与赠送余额的扣除比例")
    private BigDecimal realGiftRatio;

    /**
     * 是否全品类适用
     */
    @TableField(value = "fully")
    @ApiModelProperty(value="是否全品类适用")
    @Excel(name = "是否全品类适用")
    private Integer fully;

    /**
     * 是否全品类适用
     */
    @TableField(value = "discount_ratio")
    @ApiModelProperty(value="折扣率")
    @Excel(name = "折扣率")
    private BigDecimal discountRatio;

    @TableField(exist = false)
    private List<ProductItem> serviceList;
    @TableField(exist = false)
    private List<ProductItem> projectList;

    @TableField(exist = false)
    private Long[] productIds;
}