package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 店面商品提成设置
 */
@ApiModel(description="店面商品提成设置")
@Data
@TableName(value = "store_product_commission")
public class StoreProductCommission {
    /**
     * 实例ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="实例ID")
    private Long id;

    /**
     * 店面销售物ID
     */
    @TableField(value = "store_product_id")
    @ApiModelProperty(value="店面销售物ID")
    private Long storeProductId;

    /**
     * 支付类型: cash-现金, card-消费卡
     */
    @TableField(value = "payment_type")
    @ApiModelProperty(value="支付类型: cash-现金, card-消费卡")
    private Object paymentType;

    /**
     * 支付使用的消费卡的店面销售物ID
     */
    @TableField(value = "store_card_product_id")
    @ApiModelProperty(value="支付使用的消费卡的店面销售物ID")
    private Long storeCardProductId;

    /**
     * 部门ID
     */
    @TableField(value = "department_id")
    @ApiModelProperty(value="部门ID")
    private Long departmentId;

    /**
     * 职务ID
     */
    @TableField(value = "position_id")
    @ApiModelProperty(value="职务ID")
    private Long positionId;

    /**
     * 提成类型: fixed-固定, ratio-比例
     */
    @TableField(value = "commission_type")
    @ApiModelProperty(value="提成类型: fixed-固定, ratio-比例")
    private Object commissionType;

    /**
     * 固定提成金额
     */
    @TableField(value = "commission_fixed")
    @ApiModelProperty(value="固定提成金额")
    private BigDecimal commissionFixed;

    /**
     * 提成比例(%)
     */
    @TableField(value = "commission_rate")
    @ApiModelProperty(value="提成比例(%)")
    private BigDecimal commissionRate;

    /**
     * 现金提成比例(%)
     */
    @TableField(value = "cash_commission_rate")
    @ApiModelProperty(value="现金提成比例(%)")
    private BigDecimal cashCommissionRate;

    /**
     * 提成比例(%)
     */
    @TableField(value = "balance_commission_rate")
    @ApiModelProperty(value="提成比例(%)")
    private BigDecimal balanceCommissionRate;

    @TableField(exist = false)
    private String name;

    @TableField(exist = false)
    private String type;
}