package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.meiye.common.annotation.Excel;
import com.meiye.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 店面对象 store
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
public class Store extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    @TableField(value = "store_code")
    @Excel(name = "店铺编号")
    private String storeCode;

    /**
     * Logo图片，可选
     */
    @TableField(value = "logo_id")
    @Excel(name = "Logo图片，可选")
    private Long logoId;

    /**
     * 店面照片，可选
     */
    @TableField(value = "photo_ids")
    @Excel(name = "店面照片，可选")
    private String photoIds;

    /**
     * 父店面ID（连锁层级）
     */
    @TableField(value = "parent_id")
    @Excel(name = "父店面ID", readConverterExp = "连=锁层级")
    private Long parentId;

    /**
     * 店面名称
     */
    @TableField(value = "name")
    @Excel(name = "店面名称")
    private String name;

    /**
     * 店面类型: main-总店, sub-分店
     */
    @TableField(value = "type")
    @Excel(name = "店面类型: main-总店, sub-分店")
    private String type;

    /**
     * 行政区划代码6位
     */
    @TableField(value = "city_code")
    @Excel(name = "行政区划代码6位")
    private String cityCode;

    /**
     * 地址
     */
    @TableField(value = "address")
    @Excel(name = "地址")
    private String address;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Excel(name = "描述")
    private String description;

    /**
     * 是否提供对外服务（仅对总店有意义）
     */
    @TableField(value = "is_active")
    @Excel(name = "是否提供对外服务", readConverterExp = "仅=对总店有意义")
    private Integer isActive;

    /**
     * 非活跃会员判定天数，如果会员连续未消费会被判定为非活跃
     */
    @TableField(value = "non_active_days")
    @Excel(name = "非活跃会员判定天数，如果会员连续未消费会被判定为非活跃")
    private Long nonActiveDays;

    /**
     * 各分店使用与主店一致的话术标准
     */
    @TableField(value = "use_same_script")
    @Excel(name = "各分店使用与主店一致的话术标准")
    private Integer useSameScript;

    /**
     * 纬度（腾讯地图API
     */
    @TableField(value = "lat")
    @Excel(name = "纬度", readConverterExp = "纬度（腾讯地图API")
    private BigDecimal lat;

    /**
     * 经度（腾讯地图API）
     */
    @TableField(value = "lon")
    @Excel(name = "经度", readConverterExp = "腾=讯地图API")
    private BigDecimal lon;

    /**
     * 打卡半径（公里），默认1。
     */
    @TableField(value = "check_in_around")
    @Excel(name = "打卡半径", readConverterExp = "公=里")
    private BigDecimal checkInAround;

    /**
     * 开始营业时间，默认10:00
     */
    @TableField(value = "start_time")
    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "开始营业时间，默认10:00", width = 30, dateFormat = "HH:mm")
    private Date startTime;

    /**
     * 结束营业时间，默认22:00
     */
    @TableField(value = "end_time")
    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "结束营业时间，默认22:00", width = 30, dateFormat = "HH:mm")
    private Date endTime;

    /**
     * 启用AI话术评价
     */
    @TableField(value = "evaluation_by_ai")
    @Excel(name = "启用AI话术评价")
    private Boolean evaluationByAi;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /**
     * logo地址
     */
    @TableField(value = "logo_url")
    @Excel(name = "logo地址")
    private String logoUrl;

    /**
     * 图片地址
     */
    @TableField(value = "photo_url")
    @Excel(name = "图片地址")
    private String photoUrl;

    /**
     * 状态：正常、冻结
     */
    @TableField(value = "status")
    @Excel(name = "状态：正常、冻结")
    private String status;

    /**
     * 冻结原因
     */
    @TableField(value = "locked_reason")
    @Excel(name = "冻结原因")
    private String lockedReason;

    /**
     * 联系人
     */
    @TableField(value = "contact_person")
    @Excel(name = "联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField(value = "contact_phone")
    @Excel(name = "联系电话")
    private String contactPhone;

    /**
     * 创建人，运营方人员ID
     */
    @TableField(value = "created_by")
    @Excel(name = "创建人，运营方人员ID")
    private Long createdBy;

    @TableField(exist = false)
    private UploadedImage logo;

    @TableField(exist = false)
    private List<UploadedImage> photoList;


    @TableField(exist = false)
    private Store rootStore;








}
