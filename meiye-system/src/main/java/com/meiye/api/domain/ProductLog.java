package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 主店面销售物定义变更记录
 */
@ApiModel(description="主店面销售物定义变更记录")
@Data
@TableName(value = "product_log")
public class ProductLog {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 适用的主店面
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="适用的主店面")
    private Long rootStoreId;

    /**
     * 卡类型:balance-储值卡, discount-优惠卡,times-次卡,experience-体验
     */
    @TableField(value = "product_id")
    @ApiModelProperty(value="卡类型:balance-储值卡, discount-优惠卡,times-次卡,experience-体验")
    private Long productId;

    /**
     * 变更类型
     */
    @TableField(value = "`type`")
    @ApiModelProperty(value="变更类型")
    private Object type;

    /**
     * 原始数据JSON
     */
    @TableField(value = "origin_json")
    @ApiModelProperty(value="原始数据JSON")
    private String originJson;

    /**
     * 修改后的数据JSON
     */
    @TableField(value = "current_json")
    @ApiModelProperty(value="修改后的数据JSON")
    private String currentJson;

    /**
     * 修改时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="修改时间")
    private Date createdAt;

    /**
     * 修改的员工
     */
    @TableField(value = "created_by_employee_id")
    @ApiModelProperty(value="修改的员工")
    private Long createdByEmployeeId;
}