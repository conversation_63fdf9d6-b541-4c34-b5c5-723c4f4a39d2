package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 标准话术详情表
 */
@ApiModel(description="标准话术详情表")
@Data
@TableName(value = "service_script_details")
public class ServiceScriptDetails {
    /**
     * 话术ID
     */
    @TableId(value = "service_script_id")
    @ApiModelProperty(value="话术ID")
    private Long serviceScriptId;

    /**
     * 话术单元ID
     */
    @TableField(value = "service_script_item_id")
    @ApiModelProperty(value="话术单元ID")
    private Long serviceScriptItemId;

    /**
     * 排序顺序
     */
    @TableField(value = "sort_order")
    @ApiModelProperty(value="排序顺序")
    private Integer sortOrder;
}