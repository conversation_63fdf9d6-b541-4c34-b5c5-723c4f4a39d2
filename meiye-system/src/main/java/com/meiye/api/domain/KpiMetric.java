package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * KPI指标表
 */
@ApiModel(description="KPI指标表")
@Data
@TableName(value = "kpi_metric")
public class KpiMetric {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键ID")
    private Long id;

    /**
     * 指标编码
     */
    @TableField(value = "code")
    @ApiModelProperty(value="指标编码")
    private String code;

    /**
     * 指标名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="指标名称")
    private String name;

    /**
     * 单位（元/小时/人等）
     */
    @TableField(value = "unit")
    @ApiModelProperty(value="单位（元/小时/人等）")
    private String unit;
}