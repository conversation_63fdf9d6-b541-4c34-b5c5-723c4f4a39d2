package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 录音设备分配表
 */
@ApiModel(description="录音设备分配表")
@Data
@TableName(value = "recording_device_employee")
public class RecordingDeviceEmployee {
    /**
     * 分配表id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="分配表id")
    private Long id;

    /**
     * 所属店面
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="所属店面")
    private Long storeId;

    /**
     * 设备ID
     */
    @TableField(value = "device_id")
    @ApiModelProperty(value="设备ID")
    private Long deviceId;

    /**
     * 员工ID
     */
    @TableField(value = "employee_id")
    @ApiModelProperty(value="员工ID")
    private Long employeeId;
}