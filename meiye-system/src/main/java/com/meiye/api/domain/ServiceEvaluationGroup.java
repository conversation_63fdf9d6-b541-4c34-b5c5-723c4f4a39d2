package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 服务评价维度分组
 */
@ApiModel(description="服务评价维度分组")
@Data
@TableName(value = "service_evaluation_group")
public class ServiceEvaluationGroup extends BaseEntity {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="记录ID")
    private Long id;

    /**
     * 隶属的服务评价标准
     */
    @TableField(value = "service_evaluation_setting_id")
    @ApiModelProperty(value="隶属的服务评价标准")
    private Long serviceEvaluationSettingId;

    /**
     * 维度名称。service_evaluation_setting_id 和 name 构成唯一键
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="维度名称。service_evaluation_setting_id 和 name 构成唯一键")
    private String name;

    /**
     * 总权重
     */
    @TableField(value = "total_weight")
    @ApiModelProperty(value="总权重")
    private BigDecimal totalWeight;

    /**
     * 该维度分组下的具体维度设置数组的JSON
     */
    @TableField(value = "dimension_list")
    @ApiModelProperty(value="该维度分组下的具体维度设置数组的JSON")
    private String dimensionList;
}