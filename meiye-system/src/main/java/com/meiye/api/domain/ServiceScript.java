package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 标准话术表
 */
@ApiModel(description="标准话术表")
@Data
@TableName(value = "service_script")
public class ServiceScript {
    @Override
    public String toString() {
        return "ServiceScript{" +
                "id=" + id +
                ", rootStoreId=" + rootStoreId +
                ", storeId=" + storeId +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", createdBy=" + createdBy +
                ", createdAt=" + createdAt +
                '}';
    }

    /**
     * 话术ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="话术ID")
    private Long id;

    /**
     * 主店面ID（NULL为预定义话术）
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="主店面ID（NULL为预定义话术）")
    private Long rootStoreId;

    /**
     * 应用店面ID
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="应用店面ID")
    private Long storeId;

    /**
     * 话术标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value="话术标题")
    private String title;

    /**
     * 话术目标
     */
    @TableField(value = "description")
    @ApiModelProperty(value="话术目标")
    private String description;

    /**
     * 状态
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value="状态")
    private Object status;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private Date createdAt;

    /**
     * 话术分类
     */
    @TableField(value = "script_category")
    @ApiModelProperty(value="话术分类")
    private String scriptCategory;

    /** 标准话术单元信息 */
    private List<ServiceScriptItem> serviceScriptItemList;
    
    /**
     * 主店面名称（用于前端展示）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="主店面名称")
    private String rootStoreName;
    
    /**
     * 应用店面名称（用于前端展示）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="应用店面名称")
    private String storeName;
    
    /**
     * 当前用户店面ID（用于数据权限过滤）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="当前用户店面ID")
    private Long currentUserStoreId;

    public Long getRootStoreId() {
        return rootStoreId;
    }

    public void setRootStoreId(Long rootStoreId) {
        this.rootStoreId = rootStoreId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public Object getStatus() {
        return status;
    }

    public void setStatus(Object status) {
        this.status = status;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
    public List<ServiceScriptItem> getServiceScriptItemList()
    {
        return serviceScriptItemList;
    }

    public void setServiceScriptItemList(List<ServiceScriptItem> serviceScriptItemList)
    {
        this.serviceScriptItemList = serviceScriptItemList;
    }

    public String getScriptCategory() {
        return scriptCategory;
    }

    public void setScriptCategory(String scriptCategory) {
        this.scriptCategory = scriptCategory;
    }
    
    public String getRootStoreName() {
        return rootStoreName;
    }

    public void setRootStoreName(String rootStoreName) {
        this.rootStoreName = rootStoreName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    
    public Long getCurrentUserStoreId() {
        return currentUserStoreId;
    }
    
    public void setCurrentUserStoreId(Long currentUserStoreId) {
        this.currentUserStoreId = currentUserStoreId;
    }
}
