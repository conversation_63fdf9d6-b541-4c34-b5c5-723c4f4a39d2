package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 适于该会员的推荐项目
 */
@ApiModel(description="适于该会员的推荐项目")
@Data
@TableName(value = "member_product")
public class MemberProduct {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 会员ID
     */
    @TableField(value = "member_id")
    @ApiModelProperty(value="会员ID")
    private Long memberId;

    /**
     * 推荐项目
     */
    @TableField(value = "store_product_id")
    @ApiModelProperty(value="推荐项目")
    private Long storeProductId;

    /**
     * 建议间隔时长
     */
    @TableField(value = "cycle_length")
    @ApiModelProperty(value="建议间隔时长")
    private String cycleLength;

    /**
     * 该项目最后执行时间
     */
    @TableField(value = "last_do")
    @ApiModelProperty(value="该项目最后执行时间")
    private Date lastDo;

    /**
     * 注册会员信息的员工
     */
    @TableField(value = "created_by_employee_id")
    @ApiModelProperty(value="注册会员信息的员工")
    private Long createdByEmployeeId;

    /**
     * 注册时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="注册时间")
    private Date createdAt;

    /**
     * 会员姓名（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="会员姓名")
    private String memberName;

    /**
     * 注册员工姓名（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="注册员工姓名")
    private String createdByEmployeeName;

}
