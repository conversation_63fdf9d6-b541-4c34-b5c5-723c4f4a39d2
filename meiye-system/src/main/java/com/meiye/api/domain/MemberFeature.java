package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 会员特性表
 */
@ApiModel(description="会员特性表")
@Data
@TableName(value = "member_feature")
public class MemberFeature {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 会员id
     */
    @TableField(value = "member_id")
    @ApiModelProperty(value="会员id")
    private Long memberId;

    /**
     * 特性ID
     */
    @TableField(value = "member_feature_id")
    @ApiModelProperty(value="特性ID")
    private Long memberFeatureId;

    /**
     * 特性值（如"油性"）
     */
    @TableField(value = "`value`")
    @ApiModelProperty(value="特性值（如'油性'）")
    private String value;

    /**
     * 录入人
     */
    @TableField(value = "input_by")
    @ApiModelProperty(value="录入人")
    private Long inputBy;

    /**
     * 录入人姓名（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="录入人姓名")
    private String inputByName;

    public String getInputByName() {
        return inputByName;
    }

    public void setInputByName(String inputByName) {
        this.inputByName = inputByName;
    }

    /**
     * 会员姓名（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="会员姓名")
    private String memberName;

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    /**
     * 录入时间
     */
    @TableField(value = "input_time")
    @ApiModelProperty(value="录入时间")
    private Date inputTime;

    /**
     * 删除标记 0 -未删除 2 -删除
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="删除标记 0 -未删除 2 -删除")
    private Long isDeleted;

    /**
     * 特性类型名称（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="特性类型名称")
    private String featureTypeName;

    public String getFeatureTypeName() {
        return featureTypeName;
    }

    public void setFeatureTypeName(String featureTypeName) {
        this.featureTypeName = featureTypeName;
    }
}
