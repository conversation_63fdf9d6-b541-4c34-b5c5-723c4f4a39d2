package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 已售消费卡消费余量变更记录
 */
@ApiModel(description="已售消费卡消费余量变更记录")
@Data
@TableName(value = "sold_card_balance_log")
public class SoldCardBalanceLog {
    /**
     * 实例ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="实例ID")
    private Long id;

    /**
     * 已售消费卡ID
     */
    @TableField(value = "sold_card_id")
    @ApiModelProperty(value="已售消费卡ID")
    private Long soldCardId;

    /**
     * 包含的店面销售物ID
     */
    @TableField(value = "sale_store_product_id")
    @ApiModelProperty(value="包含的店面销售物ID")
    private Long saleStoreProductId;

    /**
     * 销售物数量
     */
    @TableField(value = "quantity")
    @ApiModelProperty(value="销售物数量")
    private Integer quantity;

    /**
     * 关联订单ID
     */
    @TableField(value = "ref_order_id")
    @ApiModelProperty(value="关联订单ID")
    private Long refOrderId;

    /**
     * 关联订单的具体详情项
     */
    @TableField(value = "ref_order_detail_id")
    @ApiModelProperty(value="关联订单的具体详情项")
    private Long refOrderDetailId;

    /**
     * 记录时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="记录时间")
    private Date createdAt;
}