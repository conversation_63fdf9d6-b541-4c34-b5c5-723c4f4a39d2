package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 主店面销售物默认提成设置表
 */
@ApiModel(description="主店面销售物默认提成设置表")
@Data
@TableName(value = "product_commission")
public class ProductCommission {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 销售物ID
     */
    @TableField(value = "product_id")
    @ApiModelProperty(value="销售物ID")
    private Long productId;

    /**
     * 支付类型：cash-现金, card-消费卡
     */
    @TableField(value = "payment_type")
    @ApiModelProperty(value="支付类型：cash-现金, card-消费卡")
    private Object paymentType;

    /**
     * 消费产品时使用消费卡支付时的卡片类型，为NULL表示现金支付。
     */
    @TableField(value = "card_product_id")
    @ApiModelProperty(value="消费产品时使用消费卡支付时的卡片类型，为NULL表示现金支付。")
    private Long cardProductId;

    /**
     * 部门
     */
    @TableField(value = "department_id")
    @ApiModelProperty(value="部门")
    private Long departmentId;

    /**
     * 职务
     */
    @TableField(value = "position_id")
    @ApiModelProperty(value="职务")
    private Long positionId;

    /**
     * 固定提成或比例提成
     */
    @TableField(value = "commission_type")
    @ApiModelProperty(value="固定提成或比例提成")
    private Object commissionType;

    /**
     * 固定提成金额
     */
    @TableField(value = "commission_fixed")
    @ApiModelProperty(value="固定提成金额")
    private BigDecimal commissionFixed;

    /**
     * 提成比例（%）
     */
    @TableField(value = "commission_rate")
    @ApiModelProperty(value="提成比例（%）")
    private BigDecimal commissionRate;
}