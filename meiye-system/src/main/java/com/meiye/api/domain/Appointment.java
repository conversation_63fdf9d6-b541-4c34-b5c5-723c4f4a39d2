package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 预约表
 */
@ApiModel(description="预约表")
@Data
@TableName(value = "appointment")
public class Appointment {
    /**
     * 预约ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="预约ID")
    private Long id;

    /**
     * 店面ID
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="店面ID")
    private Long storeId;

    /**
     * 会员ID
     */
    @TableField(value = "member_id")
    @ApiModelProperty(value="会员ID")
    private Long memberId;

    /**
     * 服务员工
     */
    @TableField(value = "employee_id")
    @ApiModelProperty(value="服务员工")
    private Long employeeId;

    /**
     * 服务项目
     */
    @TableField(value = "product_id")
    @ApiModelProperty(value="服务项目")
    private Long productId;

    /**
     * 预约时间
     */
    @TableField(value = "scheduled_time")
    @ApiModelProperty(value="预约时间")
    private Date scheduledTime;

    /**
     * 状态
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value="状态")
    private Object status;

    /**
     * 关联服务记录ID
     */
    @TableField(value = "service_record_id")
    @ApiModelProperty(value="关联服务记录ID")
    private Long serviceRecordId;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private Date createdAt;
}