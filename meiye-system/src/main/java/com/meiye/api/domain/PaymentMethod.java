package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支付方式表
 */
@ApiModel(description="支付方式表")
@Data
public class PaymentMethod {


    /**
     * 名称（如"微信"、"储值卡余额"）
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="名称（如'微信'、'储值卡余额'）")
    private String name;

    /**
     * 类型（现金/卡支付）
     */
    @TableField(value = "`code`")
    @ApiModelProperty(value="类型（现金/卡支付）")
    private String code;
}