package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 店面实际销售物表
 */
@ApiModel(description="店面实际销售物表")
@Data
@TableName(value = "store_product")
public class StoreProduct {
    /**
     * 销售物ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="销售物ID")
    private Long id;

    /**
     * 店面ID
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="店面ID")
    private Long storeId;

    /**
     * 销售物ID
     */
    @TableField(value = "product_id")
    @ApiModelProperty(value="销售物ID")
    private Long productId;

    /**
     * 店面调整后价格
     */
    @TableField(value = "sale_price")
    @ApiModelProperty(value="店面调整后价格")
    private BigDecimal salePrice;

    /**
     * 是否上架: 0-否 1-是
     */
    @TableField(value = "is_available")
    @ApiModelProperty(value="是否上架: 0-否 1-是")
    private Boolean isAvailable;
}