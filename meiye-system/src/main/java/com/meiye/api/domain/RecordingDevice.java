package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 录音设备表
 */
@ApiModel(description="录音设备表")
@Data
@TableName(value = "recording_device")
public class RecordingDevice {
    /**
     * 设备id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="设备id")
    private Long id;

    /**
     * 所属店面
     */
    @TableField(value = "store_id")
    @Excel(name = "所属店面")
    @ApiModelProperty(value="所属店面")
    private Long storeId;

    /**
     * 设备序列号
     */
    @TableField(value = "device_sn")
    @Excel(name = "设备序列号")
    @ApiModelProperty(value="设备序列号")
    private String deviceSn;

    /**
     * 购买时间
     */
    @TableField(value = "buy_date")
    @Excel(name = "购买时间")
    @ApiModelProperty(value="购买时间")
    private Date buyDate;

    /**
     * 购买时间
     */
    @TableField(value = "status")
    @Excel(name = "设备使用状态")
    @ApiModelProperty(value="设备使用状态")
    private String status;

    /**
     * 购买时间
     */
    @TableField(value = "bound_status")
    @Excel(name = "绑定状态")
    @ApiModelProperty(value="绑定状态")
    private String bound_status;

    /**
     * 员工id
     */
    private Long tempEmployeeId;
}