package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 服务记录表
 */
@ApiModel(description="服务记录表")
@Data
@TableName(value = "service_record")
public class ServiceRecord {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="记录ID")
    private Long id;

    /**
     * 关联订单ID
     */
    @TableField(value = "order_id")
    @ApiModelProperty(value="关联订单ID")
    private Long orderId;

    /**
     * 关联订单明细ID
     */
    @TableField(value = "order_detail_id")
    @ApiModelProperty(value="关联订单明细ID")
    private Long orderDetailId;

    /**
     * 服务员工ID
     */
    @TableField(value = "employee_id")
    @ApiModelProperty(value="服务员工ID")
    private Long employeeId;

    /**
     * 关联话术ID
     */
    @TableField(value = "script_id")
    @ApiModelProperty(value="关联话术ID")
    private Long scriptId;

    /**
     * AI打分
     */
    @TableField(value = "ai_level")
    @ApiModelProperty(value="AI打分")
    private Object aiLevel;

    /**
     * 大模型评价结果（整体）
     */
    @TableField(value = "ai_evaluation")
    @ApiModelProperty(value="大模型评价结果（整体）")
    private String aiEvaluation;

    /**
     * 提取的预约信息
     */
    @TableField(value = "appointment_info")
    @ApiModelProperty(value="提取的预约信息")
    private String appointmentInfo;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private Date createdAt;
}