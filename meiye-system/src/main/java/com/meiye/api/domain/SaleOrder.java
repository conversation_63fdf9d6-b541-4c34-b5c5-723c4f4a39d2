package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 订单表
 */
@ApiModel(description="订单表")
@Data
@TableName(value = "sale_order")
public class SaleOrder {
    /**
     * 订单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="订单ID")
    private Long id;

    @TableField(value = "sn")
    @ApiModelProperty(value="订单号")
    private String sn;

    /**
     * 主店面ID
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="主店面ID")
    private Long rootStoreId;

    /**
     * 店面ID
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="店面ID")
    private Long storeId;

    /**
     * 会员ID（可为非会员）
     */
    @TableField(value = "member_id")
    @ApiModelProperty(value="会员ID（可为非会员）")
    private Long memberId;

    @TableField(value = "temp_name")
    @ApiModelProperty(value="非会员标识")
    private String tempName;

    /**
     * 订单状态
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value="订单状态")
    private Object status;

    /**
     * 订单总额
     */
    @TableField(value = "total_amount")
    @ApiModelProperty(value="订单总额")
    private BigDecimal totalAmount;

    /**
     * 下单时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="下单时间")
    private Date createdAt;

    /**
     * 结算时间
     */
    @TableField(value = "settled_at")
    @ApiModelProperty(value="结算时间")
    private Date settledAt;

    /**
     * 人工折扣
     */
    @TableField(value = "replace_discount")
    @ApiModelProperty(value="人工折扣")
    private BigDecimal replaceDiscount;

    /**
     * 人工折扣执行人
     */
    @TableField(value = "replace_discount_by")
    @ApiModelProperty(value="人工折扣执行人")
    private Long replaceDiscountBy;

    /**
     * 折扣原因
     */
    @TableField(value = "replace_desciption")
    @ApiModelProperty(value="折扣原因")
    private String replaceDesciption;

    /**
     * 支付总额
     */
    @TableField(value = "paid_amount")
    @ApiModelProperty(value="支付总额")
    private BigDecimal paidAmount;
}