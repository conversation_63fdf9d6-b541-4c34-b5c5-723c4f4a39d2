package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 出勤表
 */
@ApiModel(description="出勤表")
@Data
@TableName(value = "attendance")
public class Attendance {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "记录ID")
    @ApiModelProperty(value="记录ID")
    private Long id;

    /**
     * 员工ID
     */
    @TableField(value = "employee_id")
    @Excel(name = "员工ID")
    @ApiModelProperty(value="员工ID")
    private Long employeeId;

    /**
     * 打卡时间
     */
    @TableField(value = "check_in")
    @Excel(name = "打卡时间")
    @ApiModelProperty(value="打卡时间")
    private Date checkIn;

    /**
     * 是否迟到（0-否 1-是）
     */
    @TableField(value = "be_late")
    @Excel(name = "是否迟到")
    @ApiModelProperty(value="是否迟到（0-否 1-是）")
    private Boolean beLate;

    /**
     * 签退时间
     */
    @TableField(value = "check_out")
    @Excel(name = "签退时间")
    @ApiModelProperty(value="签退时间")
    private Date checkOut;

    /**
     * 是否早退（0-否 1-是）
     */
    @TableField(value = "leave_early")
    @Excel(name = "是否早退")
    @ApiModelProperty(value="是否早退（0-否 1-是）")
    private Boolean leaveEarly;

    /**
     * 日期
     */
    @TableField(value = "`date`")
    @Excel(name = "日期")
    @ApiModelProperty(value="日期")
    private Date date;

    /**
     * 纬度
     */
    @TableField(value = "lat")
    @Excel(name = "纬度")
    @ApiModelProperty(value="纬度")
    private Double lat;

    /**
     * 经度
     */
    @TableField(value = "lon")
    @Excel(name = "经度")
    @ApiModelProperty(value="经度")
    private Double lon;

    /**
     * 外勤打卡（0-否 1-是）
     */
    @TableField(value = "out_area")
    @Excel(name = "外勤打卡")
    @ApiModelProperty(value="外勤打卡（0-否 1-是）")
    private Boolean outArea;

    @TableField(exist = false) // 表示该字段不在数据库中存在
    private String type; // 操作类型: clockIn/clockOut

    @TableField(exist = false)
    private Date checkInStart;

    @TableField(exist = false)
    private Date checkInEnd;
}