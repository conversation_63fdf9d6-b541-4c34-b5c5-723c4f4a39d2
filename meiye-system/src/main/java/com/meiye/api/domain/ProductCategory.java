package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售物分类表
 */
@ApiModel(description="销售物分类表")
@Data
@TableName(value = "product_category")
public class ProductCategory {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 适用的主店面
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="适用的主店面")
    private Long rootStoreId;

    /**
     * 根分类 （一级分类为NULL）
     */
    @TableField(value = "root_id")
    @ApiModelProperty(value="根分类 （一级分类为NULL）")
    private Long rootId;

    /**
     * 父分类ID（一级分类为NULL）
     */
    @TableField(value = "parent_id")
    @ApiModelProperty(value="父分类ID（一级分类为NULL）")
    private Long parentId;

    /**
     * 分类名称（如"服务类→美发"）
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="分类名称（如'服务类→美发'）")
    private String name;

    /**
     * 层级（1=一级，2=二级）
     */
    @TableField(value = "`level`")
    @ApiModelProperty(value="层级（1=一级，2=二级）")
    private Integer level;

    /**
     * 销售状态
     */
    @TableField(value = "`type`")
    @ApiModelProperty(value="销售状态")
    private Object type;

    /**
     * 状态;0=正常,1=停用
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value="状态;0=正常,1=停用")
    private String status;

    @TableField(exist = false)
    private List<ProductCategory> children ;

    @TableField(exist = false)
    private List<StoreProductCommission> commissionList;


    /**
     * 将平铺的分类列表构建成树状结构
     * @param categories 平铺的分类列表
     * @return 树状结构的根节点列表
     */
    public static List<ProductCategory> buildTree(List<ProductCategory> categories) {
        // 用于存储所有节点的Map，key为id，value为对应的ProductCategory对象
        Map<Long, ProductCategory> categoryMap = new HashMap<>();
        // 用于存储根节点的列表
        List<ProductCategory> rootCategories = new ArrayList<>();

        // 先将所有分类放入Map中，便于后续查找
        for (ProductCategory category : categories) {
            categoryMap.put(category.getId(), category);
        }

        // 遍历所有分类，构建父子关系
        for (ProductCategory category : categories) {
            // 如果parent_id为null或者为0，说明是一级分类，加入根节点列表
            if (category.getParentId() == null || category.getParentId() == 0) {
                rootCategories.add(category);
            } else {
                // 否则找到父节点，并将当前节点加入父节点的children列表中
                ProductCategory parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(category);
                }
            }
        }

        return rootCategories;
    }
}