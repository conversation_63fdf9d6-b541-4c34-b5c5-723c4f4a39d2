package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 订单明细表
 */
@ApiModel(description="订单明细表")
@Data
@TableName(value = "sale_order_detail")
public class SaleOrderDetail {
    /**
     * 明细ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="明细ID")
    private Long id;

    /**
     * 订单ID
     */
    @TableField(value = "sale_order_id")
    @ApiModelProperty(value="订单ID")
    private Long saleOrderId;

    /**
     * 店面销售物ID
     */
    @TableField(value = "store_product_id")
    @ApiModelProperty(value="店面销售物ID")
    private Long storeProductId;

    /**
     * 消费卡ID（指该条目是购买购物卡）
     */
    @TableField(value = "sold_card_id")
    @ApiModelProperty(value="消费卡ID（指该条目是购买购物卡）")
    private Long soldCardId;

    /**
     * 销售物快照
     */
    @TableField(value = "product_snapshot")
    @ApiModelProperty(value="销售物快照")
    private String productSnapshot;

    /**
     * 数量
     */
    @TableField(value = "quantity")
    @ApiModelProperty(value="数量")
    private Integer quantity;

    /**
     * 单价
     */
    @TableField(value = "price")
    @ApiModelProperty(value="单价")
    private BigDecimal price;

    /**
     * 来自购物卡的促销折扣金额
     */
    @TableField(value = "consumption_card_discount")
    @ApiModelProperty(value="来自购物卡的促销折扣金额")
    private BigDecimal consumptionCardDiscount;

    /**
     * 实际支付（数量*单价-折扣金额合计）
     */
    @TableField(value = "amount")
    @ApiModelProperty(value="实际支付（数量*单价-折扣金额合计）")
    private BigDecimal amount;

    /**
     * 支付类型（余额、现金）
     */
    @TableField(value = "payment_type")
    @ApiModelProperty(value="支付类型（余额、现金）")
    private Object paymentType;

    /**
     * 消费卡支付时，支付使用的消费卡
     */
    @TableField(value = "payment_card_id")
    @ApiModelProperty(value="消费卡支付时，支付使用的消费卡")
    private Long paymentCardId;

    /**
     * 支付方式
     */
    @TableField(value = "payment_method_id")
    @ApiModelProperty(value="支付方式")
    private Long paymentMethodId;

    /**
     * 挂单员工ID
     */
    @TableField(value = "from_employee_id")
    @ApiModelProperty(value="挂单员工ID")
    private Long fromEmployeeId;

    /**
     * 是否移除（0-否 1-是）
     */
    @TableField(value = "removed")
    @ApiModelProperty(value="是否移除（0-否 1-是）")
    private Boolean removed;
}