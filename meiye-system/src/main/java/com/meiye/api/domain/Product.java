package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 主店面销售物定义表
 */
@ApiModel(description="主店面销售物定义表")
@Data
@TableName(value = "product")
public class Product {
    /**
     * 销售物ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="销售物ID")
    private Long id;

    /**
     * 适用的主店面
     */
    @TableField(value = "root_store_id")
    @Excel(name = "适用的主店面")
    @ApiModelProperty(value="适用的主店面")
    private Long rootStoreId;

    /**
     * 分类码
     */
    @TableField(value = "category_code")
    @Excel(name = "分类码")
    @ApiModelProperty(value="分类码")
    private Long categoryCode;

    /**
     * 名称
     */
    @TableField(value = "name")
    @Excel(name = "名称")
    @ApiModelProperty(value="名称")
    private String name;

    /**
     * 基础现金价格
     */
    @TableField(value = "price")
    @Excel(name = "基础现金价格")
    @ApiModelProperty(value="基础现金价格")
    private BigDecimal price;

    /**
     * 是否允许储值卡支付
     */
    @TableField(value = "can_use_balance")
    @Excel(name = "是否允许储值卡支付")
    @ApiModelProperty(value="是否允许储值卡支付")
    private Long canUseBalance;

    /**
     * 销售状态
     */
    @TableField(value = "status")
    @Excel(name = "销售状态")
    @ApiModelProperty(value="销售状态")
    private Object status;

    /**
     * 注册会员信息的员工
     */
    @TableField(value = "created_by_employee_id")
    @Excel(name = "注册会员信息的员工")
    @ApiModelProperty(value="注册会员信息的员工")
    private Long createdByEmployeeId;

    /**
     * 注册时间
     */
    @TableField(value = "created_at")
    @Excel(name = "注册时间")
    @ApiModelProperty(value="注册时间")
    private Date createdAt;

    @TableField(value = "category_id")
    @Excel(name = "category_id")
    @ApiModelProperty(value = "")
    private Long categoryId;

    @TableField(value = "unit")
    @Excel(name = "单位")
    @ApiModelProperty(value = "unit")
    private String  unit;

    @TableField(value = "is_main")
    @Excel(name = "是否主推")
    @ApiModelProperty(value = "isMain")
    private Boolean isMain;

    @TableField(exist = false)
    private ConsumptionCard  consumptionCard;

    @TableField(exist = false)
    private String type;

    @TableField(exist = false)
    private List<ProductItem> serviceList;

    @TableField(exist = false)
    private List<ProductItem> projectList;

    @TableField(exist = false)
    private Integer validityDays;

    @TableField(exist = false)
    private BigDecimal realBalance;

    @TableField(exist = false)
    private BigDecimal giftBalance;

    @TableField(exist = false)
    private BigDecimal realGiftRatio;

    @TableField(exist = false)
    private Integer fully;

    @TableField(exist = false)
    private BigDecimal discountRatio;

    @TableField(exist = false)
    private Date startDate;

    @TableField(exist = false)
    private Date endDate;

    @TableField(exist = false)
    private BigDecimal priceMin;

    @TableField(exist = false)
    private BigDecimal priceMax;

    @TableField(exist = false)
    private Long[] StoreIds;
}