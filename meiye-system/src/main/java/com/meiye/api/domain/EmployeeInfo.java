package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 员工扩展信息表
 */
@ApiModel(description="员工扩展信息表")
@Data
@TableName(value = "employee_info")
public class EmployeeInfo {
    /**
     * 员工ID
     */
    @TableId(value = "id")
    @ApiModelProperty(value="员工ID")
    @JsonProperty("userId")
    private Long id;

    /**
     * 出生日期
     */
    @TableField(value = "birthday")
    @ApiModelProperty(value="出生日期")
    private Date birthday;

    @TableField(value = "employment_date")
    @ApiModelProperty(value="入职日期")
    private Date employmentDate;

    /**
     * 头像
     */
    @TableField(value = "avatar")
    @ApiModelProperty(value="头像")
    @JsonProperty("avatarId")
    private String avatar;

    /**
     * 照片
     */
    @TableField(value = "photo")
    @ApiModelProperty(value="照片")
    @JsonProperty("photoId")
    private String photo;

    /**
     * 身份证号码
     */
    @TableField(value = "identity_card")
    @ApiModelProperty(value="身份证号码")
    private String identityCard;

    /**
     * 联系电话
     */
    @TableField(value = "phone")
    @ApiModelProperty(value="联系电话")
    private String phone;

    /**
     * 自我介绍
     */
    @TableField(value = "summary")
    @ApiModelProperty(value="自我介绍")
    private String summary;

    /**
     * 特长
     */
    @TableField(value = "specialty")
    @ApiModelProperty(value="特长")
    private String specialty;

    /**
     * 学习能力 C-普通,B-良好,A-优秀,S-极强
     */
    @TableField(value = "learning_ability")
    @ApiModelProperty(value="学习能力 C-普通,B-良好,A-优秀,S-极强")
    private Object learningAbility;

    /**
     * 教育背景
     */
    @TableField(value = "education")
    @ApiModelProperty(value="教育背景")
    private String education;

    /**
     * 毕业时间
     */
    @TableField(value = "edu_time")
    @ApiModelProperty(value="毕业时间")
    private Date eduTime;

    /**
     * 证书，多项使用逗号分隔
     */
    @TableField(value = "certificate")
    @ApiModelProperty(value="证书，多项使用逗号分隔")
    private String certificate;
}