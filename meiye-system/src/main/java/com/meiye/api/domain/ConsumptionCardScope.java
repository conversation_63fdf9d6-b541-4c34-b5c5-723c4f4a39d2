package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 消费卡适用范围
 */
@ApiModel(description="消费卡适用范围")
@Data
@TableName(value = "consumption_card_scope")
public class ConsumptionCardScope {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 卡片的销售物ID
     */
    @TableField(value = "product_id")
    @ApiModelProperty(value="卡片的销售物ID")
    private Long productId;

    /**
     * 支持的销售物
     */
    @TableField(value = "support_product_id")
    @ApiModelProperty(value="支持的销售物")
    private Long supportProductId;

    /**
     * 折扣方式（固定金额，比例）
     */
    @TableField(value = "discount_type")
    @ApiModelProperty(value="折扣方式（固定金额，比例）")
    private Object discountType;

    /**
     * 固定折扣金额
     */
    @TableField(value = "discount_fixed")
    @ApiModelProperty(value="固定折扣金额")
    private BigDecimal discountFixed;

    /**
     * 比例折扣（9折为90）
     */
    @TableField(value = "discount_ratio")
    @ApiModelProperty(value="比例折扣（9折为90）")
    private BigDecimal discountRatio;
}