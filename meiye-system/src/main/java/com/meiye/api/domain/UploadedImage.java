package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 用户图片信息表
 */
@ApiModel(description="用户图片信息表")
@Data
@TableName(value = "uploaded_image")
public class UploadedImage {
    /**
     * 图片id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="图片id")
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "employee_id")
    @ApiModelProperty(value="用户id")
    private Long employeeId;

    /**
     * 名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="名称")
    private String name;

    /**
     * 描述
     */
    @TableField(value = "description")
    @ApiModelProperty(value="描述")
    private String description;

    /**
     * 图片地址
     */
    @TableField(value = "file_url")
    @ApiModelProperty(value="图片地址")
    private String fileUrl;


    /**
     * 图片原名
     */
    @TableField(value = "file_name")
    @ApiModelProperty(value="图片原名")
    private String fileName;

    /**
     * 文件大小
     */
    @TableField(value = "file_size")
    @ApiModelProperty(value="文件大小")
    private Integer fileSize;

    /**
     * 文件类型 （mime类）
     */
    @TableField(value = "file_type")
    @ApiModelProperty(value="文件类型 （mime类）")
    private String fileType;

    /**
     * 宽
     */
    @TableField(value = "image_width")
    @ApiModelProperty(value="宽")
    private Integer imageWidth;

    /**
     * 高
     */
    @TableField(value = "image_height")
    @ApiModelProperty(value="高")
    private Integer imageHeight;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private Date createdAt;

    /**
     * 创建者
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建者")
    private String createdBy;
}