package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 标准话术单元表
 */
@ApiModel(description="标准话术单元表")
@Data
@TableName(value = "service_script_item")
public class ServiceScriptItem  extends BaseEntity {
    /**
     * 话术单元ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="话术单元ID")
    private Long id;

    /**
     * 主店面ID（NULL为预定义话术）
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="主店面ID（NULL为预定义话术）")
    private Long rootStoreId;

    /**
     * 应用店面ID
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="应用店面ID")
    private Long storeId;

    /**
     * 单元标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value="单元标题")
    private String title;

    /**
     * 单元内容
     */
    @TableField(value = "content")
    @ApiModelProperty(value="单元内容")
    private String content;

    /**
     * 单元目标
     */
    @TableField(value = "target")
    @ApiModelProperty(value="单元目标")
    private String target;

    /**
     * 达标要求
     */
    @TableField(value = "requirement")
    @ApiModelProperty(value="达标要求")
    private String requirement;

    /**
     * 状态
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value="状态")
    private Object status;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    private Date createdAt;
    
    /**
     * 主店面名称（用于前端展示）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="主店面名称")
    private String rootStoreName;
    
    /**
     * 应用店面名称（用于前端展示）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="应用店面名称")
    private String storeName;
}