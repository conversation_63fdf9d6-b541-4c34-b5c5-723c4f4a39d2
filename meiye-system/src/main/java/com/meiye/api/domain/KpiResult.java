package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * KPI结果表
 */
@ApiModel(description="KPI结果表")
@Data
@TableName(value = "kpi_result")
public class KpiResult {
    /**
     * 结果ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="结果ID")
    private Long id;

    /**
     * KPI设置ID
     */
    @TableField(value = "setting_id")
    @ApiModelProperty(value="KPI设置ID")
    private Long settingId;

    /**
     * 实际完成值
     */
    @TableField(value = "actual_value")
    @ApiModelProperty(value="实际完成值")
    private BigDecimal actualValue;

    /**
     * 统计时间
     */
    @TableField(value = "calculated_at")
    @ApiModelProperty(value="统计时间")
    private Date calculatedAt;
}