package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 已售消费卡销售物余量表
 */
@ApiModel(description="已售消费卡销售物余量表")
@Data
@TableName(value = "sold_card_product_times")
public class SoldCardProductTimes {
    /**
     * 实例ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="实例ID")
    private Long id;

    /**
     * 已售消费卡ID
     */
    @TableField(value = "sold_card_id")
    @ApiModelProperty(value="已售消费卡ID")
    private Long soldCardId;

    /**
     * 包含的店面销售物ID
     */
    @TableField(value = "sale_store_product_id")
    @ApiModelProperty(value="包含的店面销售物ID")
    private Long saleStoreProductId;

    /**
     * 销售物剩余数量
     */
    @TableField(value = "quantity")
    @ApiModelProperty(value="销售物剩余数量")
    private Integer quantity;
}