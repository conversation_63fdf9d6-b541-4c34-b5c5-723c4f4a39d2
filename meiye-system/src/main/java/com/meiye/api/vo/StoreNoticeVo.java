package com.meiye.api.vo;
import com.meiye.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class StoreNoticeVo {
    // 来自 store_notice 表的字段
    @Excel(name = "公告ID")
    private Long id;
    @Excel(name = "店铺名称")
    private Long storeName;
    @Excel(name = "公告标题")
    private String title;
    @Excel(name = "公告内容")
    private String content;
    @Excel(name = "发布时间")
    private Date publicTime;
    @Excel(name = "创建时间")
    private Date createdAt;
    @Excel(name = "创建人")
    private String creator;

    // 来自 store_notice_item 表的字段
    @Excel(name = "员工ID")
    private Long employeeId;
    @Excel(name = "是否已读")
    private Boolean isRead;
    @Excel(name = "阅读时间")
    private Date readTime;
}