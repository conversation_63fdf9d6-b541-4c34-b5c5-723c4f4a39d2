package com.meiye.api.vo;

import com.meiye.api.domain.EmployeeInfo;
import com.meiye.api.domain.Store;
import com.meiye.common.annotation.Excel;
import com.meiye.common.annotation.Excel.ColumnType;
import com.meiye.common.annotation.Excel.Type;
import com.meiye.common.annotation.Excels;
import com.meiye.common.core.domain.BaseEntity;
import com.meiye.common.core.domain.entity.SysDept;
import lombok.Data;

import java.util.Date;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@Data
public class EmployeeInfoVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Excel(name = "用户序号", type = Type.EXPORT, cellType = ColumnType.NUMERIC, prompt = "用户编号")
    private Long userId;

    /**
     * 部门ID
     */
    @Excel(name = "部门编号", type = Type.IMPORT)
    private Long deptId;

    /**
     * 用户账号
     */
    @Excel(name = "登录名称")
    private String userName;

    /**
     * 用户昵称
     */
    @Excel(name = "用户名称")
    private String nickName;

    /**
     * 用户邮箱
     */
    @Excel(name = "用户邮箱")
    private String email;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码", cellType = ColumnType.TEXT)
    private String phonenumber;

    /**
     * 用户性别
     */
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 账号状态（0正常 1停用）
     */
    @Excel(name = "账号状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 最后登录IP
     */
    @Excel(name = "最后登录IP", type = Type.EXPORT)
    private String loginIp;

    /**
     * 最后登录时间
     */
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
    private Date loginDate;

    /**
     * 密码最后更新时间
     */
    private Date pwdUpdateDate;

    /**
     * 部门对象
     */
    @Excels({
            @Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT),
            @Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT)
    })
    private SysDept dept;

    private Store store;
    private Store rootStore;

    private EmployeeInfo employeeInfo;

    /**
     * 岗位组
     */
    private Long[] postIds;
    private Long postId;


    /**
     * 主店铺Id
     */
    private Long rootStoreId;
    /**
     * 店铺Id
     */
    private Long storeId;

    private Boolean isManager;




}
