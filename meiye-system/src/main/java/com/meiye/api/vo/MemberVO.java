package com.meiye.api.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MemberVO {
    /**
     * id
     */
    private Long id;

    /**
     * 会员等级ID
     */
    private Long memberLevelId;

    /**
     * 会员等级名称
     */
    private String memberLevelName;

    /**
     * 会员编号
     */
    private String sn;

    /**
     * 主店面ID
     */
    private Long rootStoreId;

    /**
     * 注册店面
     */
    private Long storeId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String gender;

    /**
     * 手机号（联合root_store_id构成唯一索引）
     */
    private String phone;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 微信身份码
     */
    private String oauth;

    /**
     * 直充余额（备用
     */
    private BigDecimal realBalance;

    /**
     * 赠送余额（备用
     */
    private BigDecimal giftBalance;

    /**
     * 现金消费总额
     */
    private BigDecimal totalCash;

    /**
     * 最近一次消费日期
     */
    private Date lastConsumption;

    /**
     * 是否为活跃会员
     */
    private Boolean isActive;

    /**
     * 注册会员信息的员工
     */
    private Long createdByEmployeeId;

    /**
     * 注册时间
     */
    private Date createdAt;
}
