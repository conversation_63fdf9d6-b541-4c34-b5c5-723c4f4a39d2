package com.meiye.api.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meiye.api.domain.RecordingDeviceEmployee;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 录音设备表
 */
@ApiModel(description="录音设备表")
@Data
@TableName(value = "recording_device")
public class RecordingDeviceVo {
    /**
     * 设备id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="设备id")
    private Long id;

    /**
     * 所属店面
     */
    @TableField(value = "store_id")
    @ApiModelProperty(value="所属店面")
    private Long storeId;

    /**
     * 设备序列号
     */
    @TableField(value = "device_sn")
    @ApiModelProperty(value="设备序列号")
    private String deviceSn;

    /**
     * 购买时间
     */
    @TableField(value = "buy_date")
    @ApiModelProperty(value="购买时间")
    private Date buyDate;

    /**
     * 分配关系
     */
    @TableField(exist = false)
    @ApiModelProperty(value="分配关系")
    private RecordingDeviceEmployee assignTo;

}