package com.meiye.api.dto;

import lombok.Data;
import lombok.NonNull;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PendingOrderDto {
    /*会员id*/

    private Long memberId;
    private String tempName;
    private Long storeId;
    private Long userId;
    private BigDecimal totalAmount;

    /*操作类型
    *  'pendingOrder': '挂单',
        'checkout': '结账',
        'addCard': '开卡',
        'renewalCard': '续卡',
        'cancelCard': '退卡',
        'transferCard': '转卡',
    *
    * */
    private String type;
    private String remark;
    private List<OrderItemDto> orderItemList;


}
