package com.meiye.api.dto;

import com.meiye.api.vo.StoreProductVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 店面实际物销售
 */
@Data
public class StoreProductDTO {
    /**
     * 实际销售物ID
     */
    private Long storeProductId;

    /**
     * 店面ID
     */
    private Long storeId;

    /**
     * 销售物ID
     */
    private Long productId;


    /**
     * 基础现金价格
     */
    private Double price;

    /**
     * 店面调整后价格
     */
    private Double salePrice;

    /**
     * 是否上架
     */
    private Boolean isAvailable;

    /**
     * 销售物分类ID
     */
    private Long categoryId;


    /**
     * 销售状态 ENUM('card','type','serve')
     */
    private String categoryType;

    /**
     * 消费卡ID
     */
    private String consumptionCardId;

    /**
     * 消费卡类型  卡类型:balance-储值卡, discount-优惠卡,times-次卡,experience-体验卡
     */
    private String consumptionCardType;

    /**
     * 消费卡有效期（天）
     */
    private Long consumptionCardValidityDays;

    /**
     * 直充余额（储值卡直充余额默认等于售价）
     */
    private BigDecimal realBalance;

    /**
     * 赠送余额
     */
    private BigDecimal giftBalance;

    /**
     * 消费时直充余额与赠送余额的扣除比例（默认为：赠送余额/(直充余额+赠送余额）
     */
    private Double realGiftRatio;

    /**
     * 折扣比例
     */
    private BigDecimal discountRatio;

    /**
     * 数量
     */
    private Long num;


    private List<ProductItemVO> productItemList;

    private List<StoreProductCommissionVo> storeProductCommissionList;

    @Data
    public static class ProductItemVO {
        /**
         * id
         */
        private Long id;

        /**
         * 主销售物ID
         */
        private Long mainProductId;

        /**
         * 子销售物ID
         */
        private Long subProductId;

        /**
         * 视同售价，用于套餐内容物进行按比例提成时的售价参照
         */
        private BigDecimal equalPrice;

        /**
         * 包含数量
         */
        private Integer quantity;
    }


    @Data
    public static class StoreProductCommissionVo {
        /**
         * 实例ID
         */
        private Long id;

        /**
         * 店面销售物ID
         */
        private Long storeProductId;


        /**
         * 部门ID
         */
        private Long departmentId;

        /**
         * 职务ID
         */
        private Long positionId;


        /**
         * 现金提成比例（%）
         */
        private BigDecimal cashCommissionRate;

        /**
         * 余额提成比例（%）
         */
        private BigDecimal balanceCommissionRate;
    }
}
