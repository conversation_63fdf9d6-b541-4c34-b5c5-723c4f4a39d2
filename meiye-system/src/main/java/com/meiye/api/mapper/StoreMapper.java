package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.Store;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 店面Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface StoreMapper  extends BaseMapper<Store>
{
    /**
     * 查询店面
     * 
     * @param id 店面主键
     * @return 店面
     */
    public Store selectStoreById(Long id);

    /**
     * 查询店面
     *
     * @param id 店面主键
     * @return 店面
     */
    public Store selectNoSysStoreById(Long id);

    /**
     * 查询子店面
     *
     * @param id 店面主键
     * @return 店面
     */
    public Store selectSonById(Long id);

    /**
     * 查询子店面
     *
     * @param id 店面主键
     * @return 店面
     */
    public List<Store> selectSonsById(Long id);

    /**
     * 查询子店面
     *
     * @param id 店面主键
     * @return 店面
     */
    public Store selectTypeById(Long id);

    /**
     * 查询店面列表
     * 
     * @param store 店面
     * @return 店面集合
     */
    public List<Store> selectStoreList(Store store);

    /**
     * 新增店面
     * 
     * @param store 店面
     * @return 结果
     */
    public int insertStore(Store store);

    /**
     * 修改店面
     * 
     * @param store 店面
     * @return 结果
     */
    public int updateStore(Store store);

    /**
     * 删除店面
     * 
     * @param id 店面主键
     * @return 结果
     */
    public int deleteStoreById(Long id);

    /**
     * 批量删除店面
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStoreByIds(Long[] ids);

    @Select("SELECT * FROM store \n" +
            "WHERE id NOT IN (\n" +
            "    SELECT store_id \n" +
            "    FROM sys_user \n" +
            "    WHERE store_id IS NOT NULL\n" +
            ");")
    List<Store> unallocated();
}
