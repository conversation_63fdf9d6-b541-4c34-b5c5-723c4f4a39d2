package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.KpiResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface KpiResultMapper extends BaseMapper<KpiResult> {

    /**
     * 查询KPI结果
     *
     * @param id KPI结果主键
     * @return KPI结果
     */
    public KpiResult selectKpiResultById(Long id);

    /**
     * 查询KPI结果列表
     *
     * @param kpiResult KPI结果
     * @return KPI结果集合
     */
    public List<KpiResult> selectKpiResultList(KpiResult kpiResult);

    /**
     * 新增KPI结果
     *
     * @param kpiResult KPI结果
     * @return 结果
     */
    public int insertKpiResult(KpiResult kpiResult);

    /**
     * 修改KPI结果
     *
     * @param kpiResult KPI结果
     * @return 结果
     */
    public int updateKpiResult(KpiResult kpiResult);

    /**
     * 删除KPI结果
     *
     * @param id KPI结果主键
     * @return 结果
     */
    public int deleteKpiResultById(Long id);

    /**
     * 批量删除KPI结果
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKpiResultByIds(Long[] ids);

}