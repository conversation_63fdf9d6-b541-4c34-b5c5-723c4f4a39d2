package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.ProductCategory;
import com.meiye.api.domain.Store;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ProductCategoryMapper extends BaseMapper<ProductCategory> {
    /**
     * 查询店面
     *
     * @param id 店面主键
     * @return 店面
     */
    public ProductCategory selectProductCategoryById(Long id);
    /**
     * 查询销售物分类列表
     *
     * @param productCategory 销售物分类信息
     * @return 销售物分类集合
     */
    List<ProductCategory> selectProductCategoryList(ProductCategory productCategory);

    /**
     * 新增店面
     *
     * @param productCategory 店面
     * @return 结果
     */
    public int insertProductCategory(ProductCategory productCategory);

    /**
     * 修改销售物分类列表
     *
     * @param productCategory 销售物分类
     * @return 图片集合
     */
    int updateProductCategoryList(ProductCategory productCategory);

    /**
     * 删除店面
     *
     * @param id 店面主键
     * @return 结果
     */
    public int deleteProductCategoryById(Long id);

    /**
     * 批量删除店面
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStoreByIds(Long[] ids);
}