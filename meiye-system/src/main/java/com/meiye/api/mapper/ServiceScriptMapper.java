package com.meiye.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.ServiceScript;
import com.meiye.api.domain.ServiceScriptItem;

/**
 * 标准话术Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ServiceScriptMapper extends BaseMapper<ServiceScript>
{
    /**
     * 查询标准话术
     * 
     * @param id 标准话术主键
     * @return 标准话术
     */
    public ServiceScript selectServiceScriptById(Long id);

    /**
     * 查询标准话术列表
     * 
     * @param serviceScript 标准话术
     * @return 标准话术集合
     */
    public List<ServiceScript> selectServiceScriptList(ServiceScript serviceScript);

    /**
     * 新增标准话术
     * 
     * @param serviceScript 标准话术
     * @return 结果
     */
    public int insertServiceScript(ServiceScript serviceScript);

    /**
     * 修改标准话术
     * 
     * @param serviceScript 标准话术
     * @return 结果
     */
    public int updateServiceScript(ServiceScript serviceScript);

    /**
     * 删除标准话术
     * 
     * @param id 标准话术主键
     * @return 结果
     */
    public int deleteServiceScriptById(Long id);

    /**
     * 批量删除标准话术
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceScriptByIds(Long[] ids);

    /**
     * 批量删除标准话术单元
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceScriptItemByIds(Long[] ids);
    
    /**
     * 批量新增标准话术单元
     * 
     * @param serviceScriptItemList 标准话术单元列表
     * @return 结果
     */
    public int batchServiceScriptItem(List<ServiceScriptItem> serviceScriptItemList);
    

    /**
     * 通过标准话术主键删除标准话术单元信息
     * 
     * @param id 标准话术ID
     * @return 结果
     */
    public int deleteServiceScriptItemById(Long id);
}
