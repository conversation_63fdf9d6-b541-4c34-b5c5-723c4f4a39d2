package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.ServiceEvaluationSetting;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ServiceEvaluationSettingMapper extends BaseMapper<ServiceEvaluationSetting> {

    /**
     * 查询服务评价设置
     *
     * @param id 服务评价设置主键
     * @return 服务评价设置
     */
    public ServiceEvaluationSetting selectServiceEvaluationSettingById(Long id);

    /**
     * 查询服务评价设置列表
     *
     * @param serviceEvaluationSetting 服务评价设置
     * @return 服务评价设置集合
     */
    public List<ServiceEvaluationSetting> selectServiceEvaluationSettingList(ServiceEvaluationSetting serviceEvaluationSetting);

    /**
     * 新增服务评价设置
     *
     * @param serviceEvaluationSetting 服务评价设置
     * @return 结果
     */
    public int insertServiceEvaluationSetting(ServiceEvaluationSetting serviceEvaluationSetting);

    /**
     * 修改服务评价设置
     *
     * @param serviceEvaluationSetting 服务评价设置
     * @return 结果
     */
    public int updateServiceEvaluationSetting(ServiceEvaluationSetting serviceEvaluationSetting);

    /**
     * 删除服务评价设置
     *
     * @param id 服务评价设置主键
     * @return 结果
     */
    public int deleteServiceEvaluationSettingById(Long id);

    /**
     * 批量删除服务评价设置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceEvaluationSettingByIds(Long[] ids);

}