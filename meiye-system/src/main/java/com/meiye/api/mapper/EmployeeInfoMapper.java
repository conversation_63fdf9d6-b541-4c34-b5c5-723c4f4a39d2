package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.EmployeeInfo;
import com.meiye.api.vo.EmployeeCommissionOptionVO;
import com.meiye.api.vo.EmployeeInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EmployeeInfoMapper extends BaseMapper<EmployeeInfo> {
    /**
     * 查询员工扩展信息
     *
     * @param id 员工扩展信息主键
     * @return 员工扩展信息
     */
    public EmployeeInfo selectEmployeeInfoById(Long id);

    /**
     * 查询员工扩展信息列表
     *
     * @param employeeInfo 员工扩展信息
     * @return 员工扩展信息集合
     */
    public List<EmployeeInfo> selectEmployeeInfoList(EmployeeInfo employeeInfo);

    /**
     * 新增员工扩展信息
     *
     * @param employeeInfo 员工扩展信息
     * @return 结果
     */
    public int insertEmployeeInfo(EmployeeInfo employeeInfo);

    /**
     * 修改员工扩展信息
     *
     * @param employeeInfo 员工扩展信息
     * @return 结果
     */
    public int updateEmployeeInfo(EmployeeInfo employeeInfo);

    /**
     * 删除员工扩展信息
     *
     * @param id 员工扩展信息主键
     * @return 结果
     */
    public int deleteEmployeeInfoById(Long id);

    /**
     * 批量删除员工扩展信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmployeeInfoByIds(Long[] ids);

    List<EmployeeInfoVo> selectEmployeeVoList( EmployeeInfoVo employeeInfo);

    EmployeeInfoVo selectEmployeeVoById(Long id);

    List<EmployeeCommissionOptionVO> selectECOptions(@Param("storeId") Long storeId);
}