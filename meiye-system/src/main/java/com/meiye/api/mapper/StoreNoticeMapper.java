package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.StoreNotice;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface StoreNoticeMapper extends BaseMapper<StoreNotice> {
    /**
     * 查询店铺公告
     *
     * @param id 店铺公告主键
     * @return 店铺公告
     */
    public StoreNotice selectStoreNoticeById(Long id);

    /**
     * 查询店铺公告列表
     *
     * @param storeNotice 店铺公告
     * @return 店铺公告集合
     */
    public List<StoreNotice> selectStoreNoticeList(StoreNotice storeNotice);

    /**
     * 查询店铺公告列表
     *
     * @param storeNotice 店铺公告
     * @return 店铺公告集合
     */
    public List<StoreNotice> selectStoreNoticeListNoTime(StoreNotice storeNotice);

    /**
     * 新增店铺公告
     *
     * @param storeNotice 店铺公告
     * @return 结果
     */
    public int insertStoreNotice(StoreNotice storeNotice);

    /**
     * 修改店铺公告
     *
     * @param storeNotice 店铺公告
     * @return 结果
     */
    public int updateStoreNotice(StoreNotice storeNotice);

    /**
     * 删除店铺公告
     *
     * @param id 店铺公告主键
     * @return 结果
     */
    public int deleteStoreNoticeById(Long id);

    /**
     * 批量删除店铺公告
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStoreNoticeByIds(Long[] ids);
}