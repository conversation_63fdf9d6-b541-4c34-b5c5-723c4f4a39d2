package com.meiye.api.mapper;

import java.util.List;
import com.meiye.api.domain.Product;
import com.meiye.api.query.StoreProductQuery;
import com.meiye.api.vo.OptionVo;

/**
 * 主店面销售物定义Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface ProductMapper
{
    /**
     * 查询主店面销售物定义
     *
     * @param id 主店面销售物定义主键
     * @return 主店面销售物定义
     */
    public Product selectProductById(Long id);

    /**
     * 查询主店面销售物定义列表
     *
     * @param product 主店面销售物定义
     * @return 主店面销售物定义集合
     */
    public List<Product> selectServiceList(Product product);

    /**
     * 查询主店面销售物定义列表
     *
     * @param product 主店面销售物定义
     * @return 主店面销售物定义集合
     */
    public List<Product> selectProductList(Product product);

    /**
     * 查询主店面销售物定义列表
     *
     * @param product 主店面销售物定义
     * @return 主店面销售物定义集合
     */
    public List<Product> selectCardList(Product product);

    /**
     * 新增主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    public int insertProduct(Product product);

    /**
     * 修改主店面销售物定义
     *
     * @param product 主店面销售物定义
     * @return 结果
     */
    public int updateProduct(Product product);

    /**
     * 删除主店面销售物定义
     *
     * @param id 主店面销售物定义主键
     * @return 结果
     */
    public int deleteProductById(Long id);

    /**
     * 批量删除主店面销售物定义
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProductByIds(Long[] ids);

    List<Product> selectProductOption(Product product);

    List<OptionVo> selectOptionSp(StoreProductQuery storeProductQuery);
}
