package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.MemberFeatureType;

import java.util.List;


/**
 * 特性类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface MemberFeatureTypeMapper extends BaseMapper<MemberFeatureType>
{
    /**
     * 查询特性类型
     * 
     * @param id 特性类型主键
     * @return 特性类型
     */
    public MemberFeatureType selectMemberFeatureTypeById(Long id);

    /**
     * 查询特性类型列表
     * 
     * @param memberFeatureType 特性类型
     * @return 特性类型集合
     */
    public List<MemberFeatureType> selectMemberFeatureTypeList(MemberFeatureType memberFeatureType);

    /**
     * 新增特性类型
     * 
     * @param memberFeatureType 特性类型
     * @return 结果
     */
    public int insertMemberFeatureType(MemberFeatureType memberFeatureType);
    List<MemberFeatureType> selectAllMemberFeatureTypes();
    /**
     * 修改特性类型
     * 
     * @param memberFeatureType 特性类型
     * @return 结果
     */
    public int updateMemberFeatureType(MemberFeatureType memberFeatureType);

    /**
     * 删除特性类型
     * 
     * @param id 特性类型主键
     * @return 结果
     */
    public int deleteMemberFeatureTypeById(Long id);

    /**
     * 批量删除特性类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMemberFeatureTypeByIds(Long[] ids);
}
