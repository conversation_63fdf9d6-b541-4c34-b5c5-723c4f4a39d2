package com.meiye.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.RecordingDevice;
import com.meiye.api.vo.RecordingDeviceVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 录音设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-04
 */

@Mapper
public interface RecordingDeviceMapper extends BaseMapper<RecordingDevice> {
    List<RecordingDeviceVo> selectDeviceWithAssignInfo();
    /**
     * 删除录音设备
     *
     * @param id 录音设备主键
     * @return 结果
     */
    public int deleteRecordingDeviceById(Long id);

    /**
     * 批量删除录音设备
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecordingDeviceByIds(Long[] ids);
    /**
     * 查询录音设备
     *
     * @param id 录音设备主键
     * @return 录音设备
     */
    public RecordingDevice selectRecordingDeviceById(Long id);

    /**
     * 查询录音设备列表
     *
     * @param recordingDevice 录音设备
     * @return 录音设备集合
     */
    public List<RecordingDevice> selectRecordingDeviceList(RecordingDevice recordingDevice);

    /**
     * 新增录音设备
     *
     * @param recordingDevice 录音设备
     * @return 结果
     */
    public int insertRecordingDevice(RecordingDevice recordingDevice);

    /**
     * 修改录音设备
     *
     * @param recordingDevice 录音设备
     * @return 结果
     */
    public int updateRecordingDevice(RecordingDevice recordingDevice);

}
