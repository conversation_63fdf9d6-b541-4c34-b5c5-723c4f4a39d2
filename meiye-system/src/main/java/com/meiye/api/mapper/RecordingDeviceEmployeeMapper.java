package com.meiye.api.mapper;

import java.util.List;
import com.meiye.api.domain.RecordingDeviceEmployee;

/**
 * 录音设备分配Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface RecordingDeviceEmployeeMapper
{
    /**
     * 查询录音设备分配
     *
     * @param id 录音设备分配主键
     * @return 录音设备分配
     */
    public RecordingDeviceEmployee selectRecordingDeviceEmployeeById(Long id);

    /**
     * 查询录音设备分配列表
     *
     * @param recordingDeviceEmployee 录音设备分配
     * @return 录音设备分配集合
     */
    public List<RecordingDeviceEmployee> selectRecordingDeviceEmployeeList(RecordingDeviceEmployee recordingDeviceEmployee);

    /**
     * 新增录音设备分配
     *
     * @param recordingDeviceEmployee 录音设备分配
     * @return 结果
     */
    public int insertRecordingDeviceEmployee(RecordingDeviceEmployee recordingDeviceEmployee);

    /**
     * 修改录音设备分配
     *
     * @param recordingDeviceEmployee 录音设备分配
     * @return 结果
     */
    public int updateRecordingDeviceEmployee(RecordingDeviceEmployee recordingDeviceEmployee);

    /**
     * 删除录音设备分配
     *
     * @param id 录音设备分配主键
     * @return 结果
     */
    public int deleteRecordingDeviceEmployeeById(Long id);
    public int deleteRecordingDeviceEmployeeByDeviceId(Long id);

    /**
     * 批量删除录音设备分配
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRecordingDeviceEmployeeByIds(Long[] ids);
    public int deleteRecordingDeviceEmployeeByDeviceIds(Long[] ids);


    RecordingDeviceEmployee selectByEmployeeId(Long employeeId);

    RecordingDeviceEmployee selectByDeviceId(Long deviceId);
}
