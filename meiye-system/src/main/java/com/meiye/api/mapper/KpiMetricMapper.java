package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.KpiMetric;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface KpiMetricMapper extends BaseMapper<KpiMetric> {

    /**
     * 查询KPI指标
     *
     * @param id KPI指标主键
     * @return KPI指标
     */
    public KpiMetric selectKpiMetricById(Long id);

    /**
     * 查询KPI指标列表
     *
     * @param kpiMetric KPI指标
     * @return KPI指标集合
     */
    public List<KpiMetric> selectKpiMetricList(KpiMetric kpiMetric);

    /**
     * 新增KPI指标
     *
     * @param kpiMetric KPI指标
     * @return 结果
     */
    public int insertKpiMetric(KpiMetric kpiMetric);

    /**
     * 修改KPI指标
     *
     * @param kpiMetric KPI指标
     * @return 结果
     */
    public int updateKpiMetric(KpiMetric kpiMetric);

    /**
     * 删除KPI指标
     *
     * @param id KPI指标主键
     * @return 结果
     */
    public int deleteKpiMetricById(Long id);

    /**
     * 批量删除KPI指标
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKpiMetricByIds(Long[] ids);

}