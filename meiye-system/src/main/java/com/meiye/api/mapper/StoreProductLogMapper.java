package com.meiye.api.mapper;

import java.util.List;
import com.meiye.api.domain.StoreProductLog;

/**
 * 店面实际销售物定义变更记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface StoreProductLogMapper 
{
    /**
     * 查询店面实际销售物定义变更记录
     * 
     * @param id 店面实际销售物定义变更记录主键
     * @return 店面实际销售物定义变更记录
     */
    public StoreProductLog selectStoreProductLogById(Long id);

    /**
     * 查询店面实际销售物定义变更记录列表
     * 
     * @param storeProductLog 店面实际销售物定义变更记录
     * @return 店面实际销售物定义变更记录集合
     */
    public List<StoreProductLog> selectStoreProductLogList(StoreProductLog storeProductLog);

    /**
     * 新增店面实际销售物定义变更记录
     * 
     * @param storeProductLog 店面实际销售物定义变更记录
     * @return 结果
     */
    public int insertStoreProductLog(StoreProductLog storeProductLog);

    /**
     * 修改店面实际销售物定义变更记录
     * 
     * @param storeProductLog 店面实际销售物定义变更记录
     * @return 结果
     */
    public int updateStoreProductLog(StoreProductLog storeProductLog);

    /**
     * 删除店面实际销售物定义变更记录
     * 
     * @param id 店面实际销售物定义变更记录主键
     * @return 结果
     */
    public int deleteStoreProductLogById(Long id);

    /**
     * 删除店面实际销售物定义变更记录
     *
     * @param id 店面实际销售物定义变更记录主键
     * @return 结果
     */
    public int deleteStoreProductLogBySPId(Long id);

    /**
     * 批量删除店面实际销售物定义变更记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStoreProductLogByIds(Long[] ids);
}
