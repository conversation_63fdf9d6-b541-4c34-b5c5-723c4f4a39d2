package com.meiye.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.ServiceScriptDetails;

/**
 * 标准话术详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ServiceScriptDetailsMapper extends BaseMapper<ServiceScriptDetails>
{
    /**
     * 查询标准话术详情
     * 
     * @param serviceScriptId 标准话术详情主键
     * @return 标准话术详情
     */
    public ServiceScriptDetails selectServiceScriptDetailsByServiceScriptId(Long serviceScriptId);

    /**
     * 查询标准话术详情列表
     * 
     * @param serviceScriptDetails 标准话术详情
     * @return 标准话术详情集合
     */
    public List<ServiceScriptDetails> selectServiceScriptDetailsList(ServiceScriptDetails serviceScriptDetails);

    /**
     * 新增标准话术详情
     * 
     * @param serviceScriptDetails 标准话术详情
     * @return 结果
     */
    public int insertServiceScriptDetails(ServiceScriptDetails serviceScriptDetails);

    /**
     * 修改标准话术详情
     * 
     * @param serviceScriptDetails 标准话术详情
     * @return 结果
     */
    public int updateServiceScriptDetails(ServiceScriptDetails serviceScriptDetails);

    /**
     * 删除标准话术详情
     * 
     * @param serviceScriptId 标准话术详情主键
     * @return 结果
     */
    public int deleteServiceScriptDetailsByServiceScriptId(Long serviceScriptId);

    /**
     * 批量删除标准话术详情
     * 
     * @param serviceScriptIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceScriptDetailsByServiceScriptIds(Long[] serviceScriptIds);
}
