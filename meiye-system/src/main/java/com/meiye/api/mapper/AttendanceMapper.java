package com.meiye.api.mapper;

import java.util.Date;
import java.util.List;
import com.meiye.api.domain.Attendance;
import org.apache.ibatis.annotations.Param;

/**
 * 出勤Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface AttendanceMapper 
{
    /**
     * 查询出勤
     * 
     * @param id 出勤主键
     * @return 出勤
     */
    public Attendance selectAttendanceById(Long id);

    /**
     * 查询出勤列表
     * 
     * @param attendance 出勤
     * @return 出勤集合
     */
    public List<Attendance> selectAttendanceList(Attendance attendance);

    /**
     * 新增出勤
     * 
     * @param attendance 出勤
     * @return 结果
     */
    public int insertAttendance(Attendance attendance);

    /**
     * 修改出勤
     * 
     * @param attendance 出勤
     * @return 结果
     */
    public int updateAttendance(Attendance attendance);

    /**
     * 删除出勤
     * 
     * @param id 出勤主键
     * @return 结果
     */
    public int deleteAttendanceById(Long id);

    /**
     * 批量删除出勤
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAttendanceByIds(Long[] ids);

    public Attendance selectAttendanceByUserAndDate(@Param("employeeId") Long employeeId,@Param("date") Date date);
}
