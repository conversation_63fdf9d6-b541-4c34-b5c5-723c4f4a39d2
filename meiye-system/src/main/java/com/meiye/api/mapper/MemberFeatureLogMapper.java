package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.MemberFeatureLog;

import java.util.List;


/**
 * 会员特性变更记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface MemberFeatureLogMapper extends BaseMapper<MemberFeatureLog>
{
    /**
     * 查询会员特性变更记录
     * 
     * @param id 会员特性变更记录主键
     * @return 会员特性变更记录
     */
    public MemberFeatureLog selectMemberFeatureLogById(Long id);

    /**
     * 查询会员特性变更记录列表
     * 
     * @param memberFeatureLog 会员特性变更记录
     * @return 会员特性变更记录集合
     */
    public List<MemberFeatureLog> selectMemberFeatureLogList(MemberFeatureLog memberFeatureLog);

    /**
     * 新增会员特性变更记录
     * 
     * @param memberFeatureLog 会员特性变更记录
     * @return 结果
     */
    public int insertMemberFeatureLog(MemberFeatureLog memberFeatureLog);

    /**
     * 修改会员特性变更记录
     * 
     * @param memberFeatureLog 会员特性变更记录
     * @return 结果
     */
    public int updateMemberFeatureLog(MemberFeatureLog memberFeatureLog);

    /**
     * 删除会员特性变更记录
     * 
     * @param id 会员特性变更记录主键
     * @return 结果
     */
    public int deleteMemberFeatureLogById(Long id);

    /**
     * 批量删除会员特性变更记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMemberFeatureLogByIds(Long[] ids);
}
