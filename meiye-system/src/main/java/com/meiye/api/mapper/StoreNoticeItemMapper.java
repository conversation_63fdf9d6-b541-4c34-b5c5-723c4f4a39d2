package com.meiye.api.mapper;

import java.util.List;
import com.meiye.api.domain.StoreNoticeItem;

/**
 * 店铺公告发布记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface StoreNoticeItemMapper 
{
    /**
     * 查询店铺公告发布记录
     * 
     * @param id 店铺公告发布记录主键
     * @return 店铺公告发布记录
     */
    public StoreNoticeItem selectStoreNoticeItemById(Long id);

    /**
     * 查询店铺公告发布记录列表
     * 
     * @param storeNoticeItem 店铺公告发布记录
     * @return 店铺公告发布记录集合
     */
    public List<StoreNoticeItem> selectStoreNoticeItemList(StoreNoticeItem storeNoticeItem);

    /**
     * 新增店铺公告发布记录
     * 
     * @param storeNoticeItem 店铺公告发布记录
     * @return 结果
     */
    public int insertStoreNoticeItem(StoreNoticeItem storeNoticeItem);

    /**
     * 修改店铺公告发布记录
     * 
     * @param storeNoticeItem 店铺公告发布记录
     * @return 结果
     */
    public int updateStoreNoticeItem(StoreNoticeItem storeNoticeItem);

    /**
     * 删除店铺公告发布记录
     * 
     * @param id 店铺公告发布记录主键
     * @return 结果
     */
    public int deleteStoreNoticeItemById(Long id);

    /**
     * 删除店铺公告发布记录
     *
     * @param id 店铺公告发布记录主键
     * @return 结果
     */
    public int deleteStoreNoticeItemByNId(Long id);

    /**
     * 批量删除店铺公告发布记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStoreNoticeItemByIds(Long[] ids);
}
