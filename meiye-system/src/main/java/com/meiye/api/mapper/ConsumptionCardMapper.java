package com.meiye.api.mapper;

import java.util.List;
import com.meiye.api.domain.ConsumptionCard;
import com.meiye.api.domain.ProductItem;
import org.apache.ibatis.annotations.Param;

/**
 * 消费卡补充定义Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface ConsumptionCardMapper 
{
    /**
     * 查询消费卡补充定义
     * 
     * @param id 消费卡补充定义主键
     * @return 消费卡补充定义
     */
    public ConsumptionCard selectConsumptionCardByPId(Long id);

    /**
     * 查询消费卡补充定义列表
     * 
     * @param consumptionCard 消费卡补充定义
     * @return 消费卡补充定义集合
     */
    public List<ConsumptionCard> selectConsumptionCardList(ConsumptionCard consumptionCard);

    /**
     * 新增消费卡补充定义
     * 
     * @param consumptionCard 消费卡补充定义
     * @return 结果
     */
    public int insertConsumptionCard(ConsumptionCard consumptionCard);

    /**
     * 修改消费卡补充定义
     * 
     * @param consumptionCard 消费卡补充定义
     * @return 结果
     */
    public int updateConsumptionCard(ConsumptionCard consumptionCard);

    /**
     * 删除消费卡补充定义
     * 
     * @param id 消费卡补充定义主键
     * @return 结果
     */
    public int deleteConsumptionCardById(Long id);

    /**
     * 批量删除消费卡补充定义
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteConsumptionCardByIds(Long[] ids);
}
