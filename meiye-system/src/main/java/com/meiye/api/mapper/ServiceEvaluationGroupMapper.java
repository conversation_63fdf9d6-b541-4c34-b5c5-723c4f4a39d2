package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.ServiceEvaluationGroup;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ServiceEvaluationGroupMapper extends BaseMapper<ServiceEvaluationGroup> {
    /**
     * 查询服务评价维度分组
     *
     * @param id 服务评价维度分组主键
     * @return 服务评价维度分组
     */
    public ServiceEvaluationGroup selectServiceEvaluationGroupById(Long id);

    /**
     * 查询服务评价维度分组列表
     *
     * @param serviceEvaluationGroup 服务评价维度分组
     * @return 服务评价维度分组集合
     */
    public List<ServiceEvaluationGroup> selectServiceEvaluationGroupList(ServiceEvaluationGroup serviceEvaluationGroup);

    /**
     * 新增服务评价维度分组
     *
     * @param serviceEvaluationGroup 服务评价维度分组
     * @return 结果
     */
    public int insertServiceEvaluationGroup(ServiceEvaluationGroup serviceEvaluationGroup);

    /**
     * 修改服务评价维度分组
     *
     * @param serviceEvaluationGroup 服务评价维度分组
     * @return 结果
     */
    public int updateServiceEvaluationGroup(ServiceEvaluationGroup serviceEvaluationGroup);

    /**
     * 删除服务评价维度分组
     *
     * @param id 服务评价维度分组主键
     * @return 结果
     */
    public int deleteServiceEvaluationGroupById(Long id);

    /**
     * 批量删除服务评价维度分组
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceEvaluationGroupByIds(Long[] ids);
}