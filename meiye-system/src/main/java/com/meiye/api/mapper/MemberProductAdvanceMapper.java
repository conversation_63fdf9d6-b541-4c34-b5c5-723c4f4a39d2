package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.MemberProductAdvance;

import java.util.List;


/**
 * 为会员推荐过的项目记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface MemberProductAdvanceMapper
        extends BaseMapper<MemberProductAdvance>
{
    /**
     * 查询为会员推荐过的项目记录
     * 
     * @param id 为会员推荐过的项目记录主键
     * @return 为会员推荐过的项目记录
     */
    public MemberProductAdvance selectMemberProductAdvanceById(Long id);

    /**
     * 查询为会员推荐过的项目记录列表
     * 
     * @param memberProductAdvance 为会员推荐过的项目记录
     * @return 为会员推荐过的项目记录集合
     */
    public List<MemberProductAdvance> selectMemberProductAdvanceList(MemberProductAdvance memberProductAdvance);

    /**
     * 新增为会员推荐过的项目记录
     * 
     * @param memberProductAdvance 为会员推荐过的项目记录
     * @return 结果
     */
    public int insertMemberProductAdvance(MemberProductAdvance memberProductAdvance);

    /**
     * 修改为会员推荐过的项目记录
     * 
     * @param memberProductAdvance 为会员推荐过的项目记录
     * @return 结果
     */
    public int updateMemberProductAdvance(MemberProductAdvance memberProductAdvance);

    /**
     * 删除为会员推荐过的项目记录
     * 
     * @param id 为会员推荐过的项目记录主键
     * @return 结果
     */
    public int deleteMemberProductAdvanceById(Long id);

    /**
     * 批量删除为会员推荐过的项目记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMemberProductAdvanceByIds(Long[] ids);
}
