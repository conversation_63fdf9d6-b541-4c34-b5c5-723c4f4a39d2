package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.UploadedImage;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UploadedImageMapper extends BaseMapper<UploadedImage> {

        /**
         * 查询用户图片信息
         *
         * @param id 用户图片信息主键
         * @return 用户图片信息
         */
        public UploadedImage selectUploadedImageById(Long id);

        /**
         * 查询用户图片信息列表
         *
         * @param uploadedImage 用户图片信息
         * @return 用户图片信息集合
         */
        public List<UploadedImage> selectUploadedImageList(UploadedImage uploadedImage);

        /**
         * 新增用户图片信息
         *
         * @param uploadedImage 用户图片信息
         * @return 结果
         */
        public int insertUploadedImage(UploadedImage uploadedImage);

        /**
         * 修改用户图片信息
         *
         * @param uploadedImage 用户图片信息
         * @return 结果
         */
        public int updateUploadedImage(UploadedImage uploadedImage);

        /**
         * 删除用户图片信息
         *
         * @param id 用户图片信息主键
         * @return 结果
         */
        public int deleteUploadedImageById(Long id);

        /**
         * 批量删除用户图片信息
         *
         * @param ids 需要删除的数据主键集合
         * @return 结果
         */
        public int deleteUploadedImageByIds(Long[] ids);
}