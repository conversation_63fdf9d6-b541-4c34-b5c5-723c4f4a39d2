package com.meiye.api.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.MemberFeature;

/**
 * 会员特性Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface MemberFeatureMapper extends BaseMapper<MemberFeature>
{
    /**
     * 查询会员特性
     * 
     * @param id 会员特性主键
     * @return 会员特性
     */
    public MemberFeature selectMemberFeatureById(Long id);

    /**
     * 查询会员特性列表
     * 
     * @param memberFeature 会员特性
     * @return 会员特性集合
     */
    public List<MemberFeature> selectMemberFeatureList(MemberFeature memberFeature);

    /**
     * 新增会员特性
     * 
     * @param memberFeature 会员特性
     * @return 结果
     */
    public int insertMemberFeature(MemberFeature memberFeature);

    /**
     * 修改会员特性
     * 
     * @param memberFeature 会员特性
     * @return 结果
     */
    public int updateMemberFeature(MemberFeature memberFeature);

    /**
     * 删除会员特性
     * 
     * @param id 会员特性主键
     * @return 结果
     */
    public int deleteMemberFeatureById(Long id);

    /**
     * 批量删除会员特性
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMemberFeatureByIds(Long[] ids);
}
