package com.meiye.api.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.ServiceScriptItem;

/**
 * 标准话术单元Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ServiceScriptItemMapper extends BaseMapper<ServiceScriptItem>
{
    /**
     * 查询标准话术单元
     * 
     * @param id 标准话术单元主键
     * @return 标准话术单元
     */
    public ServiceScriptItem selectServiceScriptItemById(Long id);

    /**
     * 查询标准话术单元列表
     * 
     * @param serviceScriptItem 标准话术单元
     * @return 标准话术单元集合
     */
    public List<ServiceScriptItem> selectServiceScriptItemList(ServiceScriptItem serviceScriptItem);

    /**
     * 新增标准话术单元
     * 
     * @param serviceScriptItem 标准话术单元
     * @return 结果
     */
    public int insertServiceScriptItem(ServiceScriptItem serviceScriptItem);

    /**
     * 修改标准话术单元
     * 
     * @param serviceScriptItem 标准话术单元
     * @return 结果
     */
    public int updateServiceScriptItem(ServiceScriptItem serviceScriptItem);

    /**
     * 批量删除标准话术单元
     * 
     * @param ids 需要删除的标准话术单元主键集合
     * @return 结果
     */
    public int deleteServiceScriptItemByIds(Long[] ids);

    /**
     * 删除标准话术单元信息
     * 
     * @param id 标准话术单元主键
     * @return 结果
     */
    public int deleteServiceScriptItemById(Long id);
    
    /**
     * 获取主店面ID
     *
     * @return 主店面ID
     */
    public Long getStoreRootId();
    
    /**
     * 根据店面名称获取店面ID
     *
     * @param storeName 店面名称
     * @return 店面ID
     */
    public Long getStoreIdByName(String storeName);
}