package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.ServiceRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ServiceRecordMapper
        extends BaseMapper<ServiceRecord> {

    /**
     * 查询服务记录
     *
     * @param id 服务记录主键
     * @return 服务记录
     */
    public ServiceRecord selectServiceRecordById(Long id);

    /**
     * 查询服务记录列表
     *
     * @param serviceRecord 服务记录
     * @return 服务记录集合
     */
    public List<ServiceRecord> selectServiceRecordList(ServiceRecord serviceRecord);

    /**
     * 新增服务记录
     *
     * @param serviceRecord 服务记录
     * @return 结果
     */
    public int insertServiceRecord(ServiceRecord serviceRecord);

    /**
     * 修改服务记录
     *
     * @param serviceRecord 服务记录
     * @return 结果
     */
    public int updateServiceRecord(ServiceRecord serviceRecord);

    /**
     * 删除服务记录
     *
     * @param id 服务记录主键
     * @return 结果
     */
    public int deleteServiceRecordById(Long id);

    /**
     * 批量删除服务记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteServiceRecordByIds(Long[] ids);

}