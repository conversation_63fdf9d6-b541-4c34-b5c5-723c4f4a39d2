package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.SoldCard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.List;

@Mapper
public interface SoldCardMapper extends BaseMapper<SoldCard> {

        /**
         * 查询已售消费卡
         *
         * @param id 已售消费卡主键
         * @return 已售消费卡
         */
        public SoldCard selectSoldCardById(Long id);

        /**
         * 查询已售消费卡列表
         *
         * @param soldCard 已售消费卡
         * @return 已售消费卡集合
         */
        public List<SoldCard> selectSoldCardList(SoldCard soldCard);

        /**
         * 新增已售消费卡
         *
         * @param soldCard 已售消费卡
         * @return 结果
         */
        public int insertSoldCard(SoldCard soldCard);

        /**
         * 修改已售消费卡
         *
         * @param soldCard 已售消费卡
         * @return 结果
         */
        public int updateSoldCard(SoldCard soldCard);



        /**
         * 删除已售消费卡
         *
         * @param id 已售消费卡主键
         * @return 结果
         */
        public int deleteSoldCardById(Long id);

        /**
         * 批量删除已售消费卡
         *
         * @param ids 需要删除的数据主键集合
         * @return 结果
         */
        public int deleteSoldCardByIds(Long[] ids);

    }