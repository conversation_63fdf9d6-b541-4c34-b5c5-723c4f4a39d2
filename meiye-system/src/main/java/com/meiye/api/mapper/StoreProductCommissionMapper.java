package com.meiye.api.mapper;

import java.util.List;
import com.meiye.api.domain.StoreProductCommission;

/**
 * 店面商品提成设置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface StoreProductCommissionMapper 
{
    /**
     * 查询店面商品提成设置
     * 
     * @param id 店面商品提成设置主键
     * @return 店面商品提成设置
     */
    public StoreProductCommission selectStoreProductCommissionById(Long id);

    /**
     * 查询店面商品提成设置
     *
     * @param id 店面商品提成设置主键
     * @return 店面商品提成设置
     */
    public StoreProductCommission selectStoreProductCommissionBySId(Long id);

    /**
     * 查询店面商品提成设置列表
     * 
     * @param storeProductCommission 店面商品提成设置
     * @return 店面商品提成设置集合
     */
    public List<StoreProductCommission> selectStoreProductCommissionList(StoreProductCommission storeProductCommission);

    /**
     * 新增店面商品提成设置
     * 
     * @param storeProductCommission 店面商品提成设置
     * @return 结果
     */
    public int insertStoreProductCommission(StoreProductCommission storeProductCommission);

    /**
     * 修改店面商品提成设置
     * 
     * @param storeProductCommission 店面商品提成设置
     * @return 结果
     */
    public int updateStoreProductCommission(StoreProductCommission storeProductCommission);

    /**
     * 删除店面商品提成设置
     * 
     * @param id 店面商品提成设置主键
     * @return 结果
     */
    public int deleteStoreProductCommissionById(Long id);

    /**
     * 批量删除店面商品提成设置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStoreProductCommissionByIds(Long[] ids);
}
