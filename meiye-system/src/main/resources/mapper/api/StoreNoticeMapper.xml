<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.StoreNoticeMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.StoreNotice">
    <!--@mbg.generated-->
    <!--@Table store_notice-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="public_time" jdbcType="TIMESTAMP" property="publicTime" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, store_id, title, content, public_time, created_at, created_by
  </sql>

  <resultMap type="StoreNotice" id="StoreNoticeResult">
    <result property="id"    column="id"    />
    <result property="storeId"    column="store_id"    />
    <result property="title"    column="title"    />
    <result property="content"    column="content"    />
    <result property="publicTime"    column="public_time"    />
    <result property="createdAt"    column="created_at"    />
    <result property="createdBy"    column="created_by"    />
  </resultMap>

  <sql id="selectStoreNoticeVo">
    select id, store_id, title, content, public_time, created_at, created_by from store_notice
  </sql>

  <select id="selectStoreNoticeList" parameterType="StoreNotice" resultMap="StoreNoticeResult">
    <include refid="selectStoreNoticeVo"/>
    <where>
      <if test="id != null  and id != ''"> and id =#{id}</if>
      <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
      <if test="params.beginPublicTime != null and params.beginPublicTime != '' and params.endPublicTime != null and params.endPublicTime != ''"> and public_time between #{params.beginPublicTime} and #{params.endPublicTime}</if>
    </where>
  </select>

  <select id="selectStoreNoticeListNoTime" parameterType="StoreNotice" resultMap="StoreNoticeResult">
    <include refid="selectStoreNoticeVo"/>
    <where>
      <if test="id != null  and id != ''"> and id =#{id}</if>
      <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
    </where>
  </select>

  <select id="selectStoreNoticeById" parameterType="Long" resultMap="StoreNoticeResult">
    <include refid="selectStoreNoticeVo"/>
    where id = #{id}
  </select>

  <insert id="insertStoreNotice" parameterType="StoreNotice" useGeneratedKeys="true" keyProperty="id">
    insert into store_notice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">store_id,</if>
      <if test="title != null and title != ''">title,</if>
      <if test="content != null and content != ''">content,</if>
      <if test="publicTime != null">public_time,</if>
      <if test="createdAt != null">created_at,</if>
      <if test="createdBy != null">created_by,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">#{storeId},</if>
      <if test="title != null and title != ''">#{title},</if>
      <if test="content != null and content != ''">#{content},</if>
      <if test="publicTime != null">#{publicTime},</if>
      <if test="createdAt != null">#{createdAt},</if>
      <if test="createdBy != null">#{createdBy},</if>
    </trim>
  </insert>

  <update id="updateStoreNotice" parameterType="StoreNotice">
    update store_notice
    <trim prefix="SET" suffixOverrides=",">
      <if test="storeId != null">store_id = #{storeId},</if>
      <if test="title != null and title != ''">title = #{title},</if>
      <if test="content != null and content != ''">content = #{content},</if>
      <if test="publicTime != null">public_time = #{publicTime},</if>
      <if test="createdAt != null">created_at = #{createdAt},</if>
      <if test="createdBy != null">created_by = #{createdBy},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteStoreNoticeById" parameterType="Long">
    delete from store_notice where id = #{id}
  </delete>

  <delete id="deleteStoreNoticeByIds" parameterType="String">
    delete from store_notice where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>


</mapper>