<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.SoldCardMapper">

    <resultMap type="SoldCard" id="SoldCardResult">
        <result property="id" column="id"/>
        <result property="storeProductId" column="store_product_id"/>
        <result property="memberId" column="member_id"/>
        <result property="cardSn" column="card_sn"/>
        <result property="realBalance" column="real_balance"/>
        <result property="giftBalance" column="gift_balance"/>
        <result property="realGiftRatio" column="real_gift_ratio"/>
        <result property="expireDate" column="expire_date"/>
        <result property="disabled" column="disabled"/>
        <result property="createdByEmployeeId" column="created_by_employee_id"/>
        <result property="createdAt" column="created_at"/>
        <result property="discountRatio" column="discount_ratio"/>
    </resultMap>

    <sql id="selectSoldCardVo">
        select sc.id, sc.store_product_id, sc.member_id, sc.card_sn, sc.real_balance, sc.gift_balance, sc.real_gift_ratio, sc.expire_date, sc.disabled, sc.created_by_employee_id, sc.created_at, sc.discount_ratio from sold_card sc
    </sql>

    <select id="selectSoldCardById" parameterType="Long" resultMap="SoldCardResult">
        <include refid="selectSoldCardVo"/>
        where sc.id = #{id}
    </select>

    <select id="selectSoldCardList" parameterType="SoldCard" resultMap="SoldCardResult">
        <include refid="selectSoldCardVo"/>
        left join store_product sp on sc.store_product_id = sp.id
        left join product p on sp.product_id = p.id
        <where>
            <if test="disabled != null "> and sc.disabled = #{disabled}</if>
            <if test="beginCreatedAt != null">and sc.created_at &gt;= #{beginCreatedAt}</if>
            <if test="endCreatedAt != null">and sc.created_at &lt;= #{endCreatedAt}</if>
            <if test="cardType != null and cardType != ''">
                and sc.store_product_id in (
                    select product_id from consumption_card where type = #{cardType}
                )
            </if>
            <if test="productName != null and productName != ''">
                and p.name like concat('%', #{productName}, '%')
            </if>
            <if test="minRealBalance != null">
                and sc.real_balance &gt;= #{minRealBalance}
            </if>
            <if test="maxRealBalance != null">
                and sc.real_balance &lt;= #{maxRealBalance}
            </if>
        </where>
    </select>

    <insert id="insertSoldCard" parameterType="SoldCard" useGeneratedKeys="true" keyProperty="id">
        insert into sold_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeProductId != null">store_product_id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="cardSn != null and cardSn != ''">card_sn,</if>
            <if test="realBalance != null">real_balance,</if>
            <if test="giftBalance != null">gift_balance,</if>
            <if test="realGiftRatio != null">real_gift_ratio,</if>
            <if test="expireDate != null">expire_date,</if>
            <if test="disabled != null">disabled,</if>
            <if test="createdByEmployeeId != null">created_by_employee_id,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="discountRatio != null">discount_ratio,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeProductId != null">#{storeProductId},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="cardSn != null and cardSn != ''">#{cardSn},</if>
            <if test="realBalance != null">#{realBalance},</if>
            <if test="giftBalance != null">#{giftBalance},</if>
            <if test="realGiftRatio != null">#{realGiftRatio},</if>
            <if test="expireDate != null">#{expireDate},</if>
            <if test="disabled != null">#{disabled},</if>
            <if test="createdByEmployeeId != null">#{createdByEmployeeId},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="discountRatio != null">#{discountRatio},</if>
        </trim>
    </insert>

    <update id="updateSoldCard" parameterType="SoldCard">
        update sold_card
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeProductId != null">store_product_id = #{storeProductId},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="cardSn != null and cardSn != ''">card_sn = #{cardSn},</if>
            <if test="realBalance != null">real_balance = #{realBalance},</if>
            <if test="giftBalance != null">gift_balance = #{giftBalance},</if>
            <if test="realGiftRatio != null">real_gift_ratio = #{realGiftRatio},</if>
            <if test="expireDate != null">expire_date = #{expireDate},</if>
            <if test="disabled != null">disabled = #{disabled},</if>
            <if test="createdByEmployeeId != null">created_by_employee_id = #{createdByEmployeeId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="discountRatio != null">discount_ratio = #{discountRatio},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSoldCardById" parameterType="Long">
        delete from sold_card where id = #{id}
    </delete>

    <delete id="deleteSoldCardByIds" parameterType="String">
        delete from sold_card where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>