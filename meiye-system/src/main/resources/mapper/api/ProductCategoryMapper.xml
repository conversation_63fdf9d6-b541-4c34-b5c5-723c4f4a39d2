<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ProductCategoryMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.ProductCategory">
    <!-- 这里的映射是正确的：数据库字段（column）→ 实体类属性（property） -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="root_store_id" jdbcType="BIGINT" property="rootStoreId" />
    <result column="root_id" jdbcType="BIGINT" property="rootId" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="type" jdbcType="OTHER" property="type" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>

  <sql id="Base_Column_List">
    id, root_store_id, root_id, parent_id, `name`, `level`, `type`, `status`
  </sql>

  <select id="selectProductCategoryById" parameterType="Long" resultMap="BaseResultMap">
    select * from product_category where id = #{id}
  </select>

  <select id="selectProductCategoryList" parameterType="ProductCategory" resultMap="BaseResultMap">
    select * from product_category
    <where>
    <if test="id != null "> and id = #{id}</if>
    <if test="rootStoreId != null "> and root_store_id = #{rootStoreId}</if>
    <if test="rootId != null "> and root_id = #{rootId}</if>
    <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
    <if test="type != null  and type != ''"> and type = #{type}</if>
    <if test="parentId != null"> and parent_id = #{parentId}</if>
    <if test="level != null"> and level = #{level}</if>
    <if test="status != null  and description != ''"> and status = #{status}</if>
    </where>
  </select>


  <update id="updateProductCategoryList" parameterType="ProductCategory">
    update product_category
    <trim prefix="SET" suffixOverrides=",">
      <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
      <if test="rootId != null">root_id = #{rootId},</if>
      <if test="parentId != null">parent_id = #{parentId},</if>
      <if test="name != null and name != ''">name = #{name},</if>
      <if test="type != null and type != ''">type = #{type},</if>
      <if test="level != null">level = #{level},</if>
      <if test="status != null">status = #{status},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteProductCategoryById" parameterType="ProductCategory">
    delete from product_category where id = #{id}
  </delete>

  <delete id="deleteStoreByIds" parameterType="String">
    delete from product_category where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <insert id="insertProductCategory" parameterType="ProductCategory"
          useGeneratedKeys="true" keyProperty="id">        <!-- 回填到实体类的id字段 -->
  insert into product_category
  <trim prefix="(" suffix=")" suffixOverrides=",">
    <if test="rootStoreId != null">root_store_id,</if>
    <if test="rootId != null">root_id,</if>
    <if test="parentId != null">parent_id,</if>
    <if test="name != null and name != ''">name,</if>
    <if test="level != null">level,</if>
    <if test="type != null">type,</if>
    <if test="status != null">status,</if>
    <!-- 移除id字段，自增主键无需手动插入 -->
  </trim>
  <trim prefix="values (" suffix=")" suffixOverrides=",">
    <!-- 移除id的插入逻辑，避免手动传递id覆盖自增 -->
    <if test="rootStoreId != null">#{rootStoreId},</if>
    <if test="rootId != null">#{rootId},</if>
    <if test="parentId != null">#{parentId},</if>
    <if test="name != null and name != ''">#{name},</if>
    <if test="level != null">#{level},</if>
    <if test="type != null">#{type},</if>
    <if test="status != null">#{status},</if>
  </trim>
</insert>

</mapper>