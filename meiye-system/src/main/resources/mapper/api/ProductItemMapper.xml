<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ProductItemMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.ProductItem">
    <!--@mbg.generated-->
    <!--@Table product_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="main_product_id" jdbcType="BIGINT" property="mainProductId" />
    <result column="sub_product_id" jdbcType="BIGINT" property="subProductId" />
    <result column="equal_price" jdbcType="DECIMAL" property="equalPrice" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, main_product_id, sub_product_id, equal_price, quantity
  </sql>
</mapper>