<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ServiceScriptItemMapper">
    
    <resultMap type="ServiceScriptItem" id="ServiceScriptItemResult">
        <result property="id"    column="id"    />
        <result property="rootStoreId"    column="root_store_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="target"    column="target"    />
        <result property="requirement"    column="requirement"    />
        <result property="status"    column="status"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdAt"    column="created_at"    />
        <result property="rootStoreName"    column="root_store_name"    />
        <result property="storeName"    column="store_name"    />
    </resultMap>

    <sql id="selectServiceScriptItemVo">
        select ssi.id, ssi.root_store_id, ssi.store_id, ssi.title, ssi.content, ssi.target, ssi.requirement, ssi.status, ssi.created_by, ssi.created_at,
               rs.name as root_store_name,
               s.name as store_name
        from service_script_item ssi
        left join store rs on ssi.root_store_id = rs.id
        left join store s on ssi.store_id = s.id
    </sql>

    <select id="selectServiceScriptItemList" parameterType="ServiceScriptItem" resultMap="ServiceScriptItemResult">
        <include refid="selectServiceScriptItemVo"/>
        <where>  
            <if test="rootStoreId != null "> and ssi.root_store_id = #{rootStoreId}</if>
            <if test="storeId != null "> and ssi.store_id = #{storeId}</if>
            <if test="title != null  and title != ''"> and ssi.title = #{title}</if>
            <if test="content != null  and content != ''"> and ssi.content = #{content}</if>
            <if test="target != null  and target != ''"> and ssi.target = #{target}</if>
            <if test="requirement != null  and requirement != ''"> and ssi.requirement = #{requirement}</if>
            <if test="status != null  and status != ''"> and ssi.status = #{status}</if>
            <if test="createdBy != null "> and ssi.created_by = #{createdBy}</if>
            <if test="createdAt != null "> and ssi.created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectServiceScriptItemById" parameterType="Long" resultMap="ServiceScriptItemResult">
        <include refid="selectServiceScriptItemVo"/>
        where ssi.id = #{id}
    </select>

    <insert id="insertServiceScriptItem" parameterType="ServiceScriptItem" useGeneratedKeys="true" keyProperty="id">
        insert into service_script_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id,</if>
            <if test="storeId != null">store_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="target != null and target != ''">target,</if>
            <if test="requirement != null">requirement,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">#{rootStoreId},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="target != null and target != ''">#{target},</if>
            <if test="requirement != null">#{requirement},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updateServiceScriptItem" parameterType="ServiceScriptItem">
        update service_script_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="target != null and target != ''">target = #{target},</if>
            <if test="requirement != null">requirement = #{requirement},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceScriptItemById" parameterType="Long">
        delete from service_script_item where id = #{id}
    </delete>

    <delete id="deleteServiceScriptItemByIds" parameterType="String">
        delete from service_script_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="getStoreRootId" resultType="Long">
        select id from store where parent_id = 0 or parent_id is null limit 1
    </select>
    
    <select id="getStoreIdByName" parameterType="String" resultType="Long">
        select id from store where name = #{storeName} limit 1
    </select>
</mapper>