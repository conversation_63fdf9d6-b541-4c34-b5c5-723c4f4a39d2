<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ProductCommissionMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.ProductCommission">
    <!--@mbg.generated-->
    <!--@Table product_commission-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="payment_type" jdbcType="OTHER" property="paymentType" />
    <result column="card_product_id" jdbcType="BIGINT" property="cardProductId" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="position_id" jdbcType="BIGINT" property="positionId" />
    <result column="commission_type" jdbcType="OTHER" property="commissionType" />
    <result column="commission_fixed" jdbcType="DECIMAL" property="commissionFixed" />
    <result column="commission_rate" jdbcType="DECIMAL" property="commissionRate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_id, payment_type, card_product_id, department_id, position_id, commission_type, 
    commission_fixed, commission_rate
  </sql>
</mapper>