<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ServiceLogMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.ServiceLog">
    <!--@mbg.generated-->
    <!--@Table service_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sale_order_detail_id" jdbcType="BIGINT" property="saleOrderDetailId" />
    <result column="assign_employee_id" jdbcType="BIGINT" property="assignEmployeeId" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="cp_department_id" jdbcType="BIGINT" property="cpDepartmentId" />
    <result column="cp_position_id" jdbcType="BIGINT" property="cpPositionId" />
    <result column="audio_key" jdbcType="VARCHAR" property="audioKey" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="audio_text" jdbcType="LONGVARCHAR" property="audioText" />
    <result column="feedback" jdbcType="LONGVARCHAR" property="feedback" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="commission_value" jdbcType="DECIMAL" property="commissionValue" />
  </resultMap>
  
  <sql id="selectServiceLogVo">
    select id, sale_order_detail_id, assign_employee_id, employee_id, cp_department_id, cp_position_id, 
    audio_key, start_time, end_time, audio_text, feedback, description, commission_value from service_log
  </sql>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sale_order_detail_id, assign_employee_id, employee_id, cp_department_id, cp_position_id, 
    audio_key, start_time, end_time, audio_text, feedback, description, commission_value
  </sql>
  
  <select id="selectServiceLogList" parameterType="ServiceLog" resultMap="BaseResultMap">
    <include refid="selectServiceLogVo"/>
    <where>
      <if test="saleOrderDetailId != null "> and sale_order_detail_id = #{saleOrderDetailId}</if>
      <if test="assignEmployeeId != null "> and assign_employee_id = #{assignEmployeeId}</if>
      <if test="employeeId != null "> and employee_id = #{employeeId}</if>
      <if test="cpDepartmentId != null "> and cp_department_id = #{cpDepartmentId}</if>
      <if test="cpPositionId != null "> and cp_position_id = #{cpPositionId}</if>
      <if test="audioKey != null  and audioKey != ''"> and audio_key = #{audioKey}</if>
      <if test="startTime != null "> and start_time = #{startTime}</if>
      <if test="endTime != null "> and end_time = #{endTime}</if>
      <if test="audioText != null  and audioText != ''"> and audio_text = #{audioText}</if>
      <if test="feedback != null  and feedback != ''"> and feedback = #{feedback}</if>
      <if test="description != null  and description != ''"> and description = #{description}</if>
      <if test="commissionValue != null "> and commission_value = #{commissionValue}</if>
    </where>
  </select>

  <select id="selectServiceLogById" parameterType="Long" resultMap="BaseResultMap">
    <include refid="selectServiceLogVo"/>
    where id = #{id}
  </select>

  <insert id="insertServiceLog" parameterType="ServiceLog" useGeneratedKeys="true" keyProperty="id">
    insert into service_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleOrderDetailId != null">sale_order_detail_id,</if>
      <if test="assignEmployeeId != null">assign_employee_id,</if>
      <if test="employeeId != null">employee_id,</if>
      <if test="cpDepartmentId != null">cp_department_id,</if>
      <if test="cpPositionId != null">cp_position_id,</if>
      <if test="audioKey != null">audio_key,</if>
      <if test="startTime != null">start_time,</if>
      <if test="endTime != null">end_time,</if>
      <if test="audioText != null">audio_text,</if>
      <if test="feedback != null">feedback,</if>
      <if test="description != null">description,</if>
      <if test="commissionValue != null">commission_value,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleOrderDetailId != null">#{saleOrderDetailId},</if>
      <if test="assignEmployeeId != null">#{assignEmployeeId},</if>
      <if test="employeeId != null">#{employeeId},</if>
      <if test="cpDepartmentId != null">#{cpDepartmentId},</if>
      <if test="cpPositionId != null">#{cpPositionId},</if>
      <if test="audioKey != null">#{audioKey},</if>
      <if test="startTime != null">#{startTime},</if>
      <if test="endTime != null">#{endTime},</if>
      <if test="audioText != null">#{audioText},</if>
      <if test="feedback != null">#{feedback},</if>
      <if test="description != null">#{description},</if>
      <if test="commissionValue != null">#{commissionValue},</if>
    </trim>
  </insert>

  <update id="updateServiceLog" parameterType="ServiceLog">
    update service_log
    <trim prefix="SET" suffixOverrides=",">
      <if test="saleOrderDetailId != null">sale_order_detail_id = #{saleOrderDetailId},</if>
      <if test="assignEmployeeId != null">assign_employee_id = #{assignEmployeeId},</if>
      <if test="employeeId != null">employee_id = #{employeeId},</if>
      <if test="cpDepartmentId != null">cp_department_id = #{cpDepartmentId},</if>
      <if test="cpPositionId != null">cp_position_id = #{cpPositionId},</if>
      <if test="audioKey != null">audio_key = #{audioKey},</if>
      <if test="startTime != null">start_time = #{startTime},</if>
      <if test="endTime != null">end_time = #{endTime},</if>
      <if test="audioText != null">audio_text = #{audioText},</if>
      <if test="feedback != null">feedback = #{feedback},</if>
      <if test="description != null">description = #{description},</if>
      <if test="commissionValue != null">commission_value = #{commissionValue},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteServiceLogById" parameterType="Long">
    delete from service_log where id = #{id}
  </delete>

  <delete id="deleteServiceLogByIds" parameterType="String">
    delete from service_log where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

</mapper>