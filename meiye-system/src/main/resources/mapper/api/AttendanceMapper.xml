<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.AttendanceMapper">
    
    <resultMap type="Attendance" id="AttendanceResult">
        <result property="id"    column="id"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="checkIn"    column="check_in"    />
        <result property="beLate"    column="be_late"    />
        <result property="checkOut"    column="check_out"    />
        <result property="leaveEarly"    column="leave_early"    />
        <result property="date"    column="date"    />
        <result property="lat"    column="lat"    />
        <result property="lon"    column="lon"    />
        <result property="outArea"    column="out_area"    />
    </resultMap>

    <sql id="selectAttendanceVo">
        select id, employee_id, check_in, be_late, check_out, leave_early, date, lat, lon, out_area from attendance
    </sql>

    <select id="selectAttendanceList" parameterType="Attendance" resultMap="AttendanceResult">
        <include refid="selectAttendanceVo"/>
        <where>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <!-- 日期范围查询：使用STR_TO_DATE转换字符串为日期 -->
            <if test="checkInStart != null">
                AND check_in &gt;= STR_TO_DATE(#{checkInStart}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="checkInEnd != null">
                AND check_in &lt;= STR_TO_DATE(#{checkInEnd}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="beLate != null "> and be_late = #{beLate}</if>
            <if test="checkOut != null "> and check_out = #{checkOut}</if>
            <if test="leaveEarly != null "> and leave_early = #{leaveEarly}</if>
            <if test="date != null "> and date = #{date}</if>
            <if test="lat != null "> and lat = #{lat}</if>
            <if test="lon != null "> and lon = #{lon}</if>
            <if test="outArea != null "> and out_area = #{outArea}</if>
        </where>
        ORDER BY date DESC
    </select>
    
    <select id="selectAttendanceById" parameterType="Long" resultMap="AttendanceResult">
        <include refid="selectAttendanceVo"/>
        where id = #{id}
    </select>

    <select id="selectAttendanceByUserAndDate" resultType="Attendance">
        SELECT *
        FROM attendance
        WHERE employee_id = #{employeeId}
          AND DATE(date) = DATE(#{date})
            LIMIT 1
    </select>

    <insert id="insertAttendance" parameterType="Attendance" useGeneratedKeys="true" keyProperty="id">
        insert into attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">employee_id,</if>
            <if test="checkIn != null">check_in,</if>
            <if test="beLate != null">be_late,</if>
            <if test="checkOut != null">check_out,</if>
            <if test="leaveEarly != null">leave_early,</if>
            <if test="date != null">date,</if>
            <if test="lat != null">lat,</if>
            <if test="lon != null">lon,</if>
            <if test="outArea != null">out_area,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">#{employeeId},</if>
            <if test="checkIn != null">#{checkIn},</if>
            <if test="beLate != null">#{beLate},</if>
            <if test="checkOut != null">#{checkOut},</if>
            <if test="leaveEarly != null">#{leaveEarly},</if>
            <if test="date != null">#{date},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lon != null">#{lon},</if>
            <if test="outArea != null">#{outArea},</if>
         </trim>
    </insert>

    <update id="updateAttendance" parameterType="Attendance">
        update attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="checkIn != null">check_in = #{checkIn},</if>
            <if test="beLate != null">be_late = #{beLate},</if>
            <if test="checkOut != null">check_out = #{checkOut},</if>
            <if test="leaveEarly != null">leave_early = #{leaveEarly},</if>
            <if test="date != null">date = #{date},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="outArea != null">out_area = #{outArea},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAttendanceById" parameterType="Long">
        delete from attendance where id = #{id}
    </delete>

    <delete id="deleteAttendanceByIds" parameterType="String">
        delete from attendance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>