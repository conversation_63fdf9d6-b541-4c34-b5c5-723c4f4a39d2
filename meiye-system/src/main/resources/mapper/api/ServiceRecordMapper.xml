<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ServiceRecordMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.ServiceRecord">
    <!--@mbg.generated-->
    <!--@Table service_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_detail_id" jdbcType="BIGINT" property="orderDetailId" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="script_id" jdbcType="BIGINT" property="scriptId" />
    <result column="ai_level" jdbcType="OTHER" property="aiLevel" />
    <result column="ai_evaluation" jdbcType="LONGVARCHAR" property="aiEvaluation" />
    <result column="appointment_info" jdbcType="LONGVARCHAR" property="appointmentInfo" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  
  <sql id="selectServiceRecordVo">
    select id, order_id, order_detail_id, employee_id, script_id, ai_level, ai_evaluation, appointment_info, 
    created_at from service_record
  </sql>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, order_detail_id, employee_id, script_id, ai_level, ai_evaluation, appointment_info, 
    created_at
  </sql>

  <select id="selectServiceRecordList" parameterType="ServiceRecord" resultMap="BaseResultMap">
    <include refid="selectServiceRecordVo"/>
    <where>
      <if test="orderId != null "> and order_id = #{orderId}</if>
      <if test="orderDetailId != null "> and order_detail_id = #{orderDetailId}</if>
      <if test="employeeId != null "> and employee_id = #{employeeId}</if>
      <if test="scriptId != null "> and script_id = #{scriptId}</if>
      <if test="aiLevel != null  and aiLevel != ''"> and ai_level = #{aiLevel}</if>
      <if test="aiEvaluation != null  and aiEvaluation != ''"> and ai_evaluation = #{aiEvaluation}</if>
      <if test="appointmentInfo != null  and appointmentInfo != ''"> and appointment_info = #{appointmentInfo}</if>
      <if test="createdAt != null "> and created_at = #{createdAt}</if>
    </where>
  </select>

  <select id="selectServiceRecordById" parameterType="Long" resultMap="BaseResultMap">
    <include refid="selectServiceRecordVo"/>
    where id = #{id}
  </select>

  <insert id="insertServiceRecord" parameterType="ServiceRecord" useGeneratedKeys="true" keyProperty="id">
    insert into service_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">order_id,</if>
      <if test="orderDetailId != null">order_detail_id,</if>
      <if test="employeeId != null">employee_id,</if>
      <if test="scriptId != null">script_id,</if>
      <if test="aiLevel != null and aiLevel != ''">ai_level,</if>
      <if test="aiEvaluation != null">ai_evaluation,</if>
      <if test="appointmentInfo != null">appointment_info,</if>
      <if test="createdAt != null">created_at,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">#{orderId},</if>
      <if test="orderDetailId != null">#{orderDetailId},</if>
      <if test="employeeId != null">#{employeeId},</if>
      <if test="scriptId != null">#{scriptId},</if>
      <if test="aiLevel != null and aiLevel != ''">#{aiLevel},</if>
      <if test="aiEvaluation != null">#{aiEvaluation},</if>
      <if test="appointmentInfo != null">#{appointmentInfo},</if>
      <if test="createdAt != null">#{createdAt},</if>
    </trim>
  </insert>

  <update id="updateServiceRecord" parameterType="ServiceRecord">
    update service_record
    <trim prefix="SET" suffixOverrides=",">
      <if test="orderId != null">order_id = #{orderId},</if>
      <if test="orderDetailId != null">order_detail_id = #{orderDetailId},</if>
      <if test="employeeId != null">employee_id = #{employeeId},</if>
      <if test="scriptId != null">script_id = #{scriptId},</if>
      <if test="aiLevel != null and aiLevel != ''">ai_level = #{aiLevel},</if>
      <if test="aiEvaluation != null">ai_evaluation = #{aiEvaluation},</if>
      <if test="appointmentInfo != null">appointment_info = #{appointmentInfo},</if>
      <if test="createdAt != null">created_at = #{createdAt},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteServiceRecordById" parameterType="Long">
    delete from service_record where id = #{id}
  </delete>

  <delete id="deleteServiceRecordByIds" parameterType="String">
    delete from service_record where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

</mapper>