<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.StoreProductCommissionMapper">
    <resultMap type="StoreProductCommission" id="StoreProductCommissionResult">
        <result property="id"    column="id"    />
        <result property="storeProductId"    column="store_product_id"    />
        <result property="paymentType"    column="payment_type"    />
        <result property="storeCardProductId"    column="store_card_product_id"    />
        <result property="departmentId"    column="department_id"    />
        <result property="positionId"    column="position_id"    />
        <result property="commissionType"    column="commission_type"    />
        <result property="commissionFixed"    column="commission_fixed"    />
        <result property="commissionRate"    column="commission_rate"    />
        <result property="cashCommissionRate"    column="cash_commission_rate"    />
        <result property="balanceCommissionRate"    column="balance_commission_rate"    />
        <result property="name"    column="name"    />
    </resultMap>

    <sql id="selectStoreProductCommissionVo">
        select id, store_product_id, payment_type, store_card_product_id, department_id, position_id, commission_type, commission_fixed, commission_rate, cash_commission_rate, balance_commission_rate from store_product_commission
    </sql>

    <select id="selectStoreProductCommissionList" resultType="com.meiye.api.domain.StoreProductCommission">
        SELECT
        spc.*,
        p.name AS name,
        pc.type AS type
        FROM
        store_product_commission spc
        LEFT JOIN
        product p ON spc.store_product_id = p.id
        LEFT JOIN
        product_category pc ON p.category_id = pc.id
        <where>
            <if test="storeProductId != null "> and store_product_id = #{storeProductId}</if>
            <if test="paymentType != null  and paymentType != ''"> and payment_type = #{paymentType}</if>
            <if test="storeCardProductId != null "> and store_card_product_id = #{storeCardProductId}</if>
            <if test="departmentId != null "> and department_id = #{departmentId}</if>
            <if test="positionId != null "> and position_id = #{positionId}</if>
            <if test="commissionType != null  and commissionType != ''"> and commission_type = #{commissionType}</if>
            <if test="commissionFixed != null "> and commission_fixed = #{commissionFixed}</if>
            <if test="commissionRate != null "> and commission_rate = #{commissionRate}</if>
            <if test="cashCommissionRate != null "> and cash_commission_rate = #{cashCommissionRate}</if>
            <if test="balanceCommissionRate != null "> and balance_commission_rate = #{balanceCommissionRate}</if>
            <if test="name != null "> and name = #{name}</if>
            <if test="type != null and type != ''"> and pc.type = #{type}</if>
        </where>
    </select>
    
    <select id="selectStoreProductCommissionById" parameterType="Long" resultMap="StoreProductCommissionResult">
        <include refid="selectStoreProductCommissionVo"/>
        where id = #{id}
    </select>

    <select id="selectStoreProductCommissionBySId" parameterType="Long" resultMap="StoreProductCommissionResult">
        <include refid="selectStoreProductCommissionVo"/>
        where store_product_id = #{id}
    </select>

    <insert id="insertStoreProductCommission" parameterType="StoreProductCommission" useGeneratedKeys="true" keyProperty="id">
        insert into store_product_commission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeProductId != null">store_product_id,</if>
            <if test="paymentType != null and paymentType != ''">payment_type,</if>
            <if test="storeCardProductId != null">store_card_product_id,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="positionId != null">position_id,</if>
            <if test="commissionType != null and commissionType != ''">commission_type,</if>
            <if test="commissionFixed != null">commission_fixed,</if>
            <if test="commissionRate != null">commission_rate,</if>
            <if test="cashCommissionRate != null">cash_commission_rate,</if>
            <if test="balanceCommissionRate != null">balance_commission_rate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeProductId != null">#{storeProductId},</if>
            <if test="paymentType != null and paymentType != ''">#{paymentType},</if>
            <if test="storeCardProductId != null">#{storeCardProductId},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="positionId != null">#{positionId},</if>
            <if test="commissionType != null and commissionType != ''">#{commissionType},</if>
            <if test="commissionFixed != null">#{commissionFixed},</if>
            <if test="commissionRate != null">#{commissionRate},</if>
            <if test="cashCommissionRate != null">#{cashCommissionRate},</if>
            <if test="balanceCommissionRate != null">#{balanceCommissionRate},</if>
         </trim>
    </insert>

    <update id="updateStoreProductCommission" parameterType="StoreProductCommission">
        update store_product_commission
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeProductId != null">store_product_id = #{storeProductId},</if>
            <if test="paymentType != null and paymentType != ''">payment_type = #{paymentType},</if>
            <if test="storeCardProductId != null">store_card_product_id = #{storeCardProductId},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="positionId != null">position_id = #{positionId},</if>
            <if test="commissionType != null and commissionType != ''">commission_type = #{commissionType},</if>
            <if test="commissionFixed != null">commission_fixed = #{commissionFixed},</if>
            <if test="commissionRate != null">commission_rate = #{commissionRate},</if>
            <if test="cashCommissionRate != null">cash_commission_rate = #{cashCommissionRate},</if>
            <if test="balanceCommissionRate != null">balance_commission_rate = #{balanceCommissionRate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStoreProductCommissionById" parameterType="Long">
        delete from store_product_commission where id = #{id}
    </delete>

    <delete id="deleteStoreProductCommissionByIds" parameterType="String">
        delete from store_product_commission where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>