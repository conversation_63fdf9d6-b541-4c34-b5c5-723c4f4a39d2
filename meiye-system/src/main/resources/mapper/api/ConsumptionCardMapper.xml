<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ConsumptionCardMapper">
    
    <resultMap type="ConsumptionCard" id="ConsumptionCardResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="type"    column="type"    />
        <result property="validityDays"    column="validity_days"    />
        <result property="realBalance"    column="real_balance"    />
        <result property="giftBalance"    column="gift_balance"    />
        <result property="realGiftRatio"    column="real_gift_ratio"    />
        <result property="fully"    column="fully"    />
        <result property="discountRatio"    column="discount_ratio"    />
    </resultMap>

    <sql id="selectConsumptionCardVo">
        select id, product_id, type, validity_days, real_balance, gift_balance, real_gift_ratio, fully,discount_ratio from consumption_card
    </sql>

    <select id="selectConsumptionCardList" parameterType="ConsumptionCard" resultMap="ConsumptionCardResult">
        <include refid="selectConsumptionCardVo"/>
        <where>
            <!-- 必须保留if条件，避免productIds为空时生成无效SQL -->
            <if test="productIds != null and productIds.length > 0">
                product_id IN
                <foreach collection="productIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="productIds == null or productIds.length == 0">
                1 = 0
            </if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="validityDays != null "> and validity_days = #{validityDays}</if>
            <if test="realBalance != null "> and real_balance = #{realBalance}</if>
            <if test="giftBalance != null "> and gift_balance = #{giftBalance}</if>
            <if test="realGiftRatio != null "> and real_gift_ratio = #{realGiftRatio}</if>
            <if test="fully != null "> and fully = #{fully}</if>
            <if test="discountRatio != null "> and discount_ratio = #{discountRatio}</if>
        </where>
    </select>
    
    <select id="selectConsumptionCardByPId" parameterType="Long" resultMap="ConsumptionCardResult">
        <include refid="selectConsumptionCardVo"/>
        where product_id = #{id}
    </select>

    <insert id="insertConsumptionCard" parameterType="ConsumptionCard" useGeneratedKeys="true" keyProperty="id">
        insert into consumption_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="type != null">type,</if>
            <if test="validityDays != null">validity_days,</if>
            <if test="realBalance != null">real_balance,</if>
            <if test="giftBalance != null">gift_balance,</if>
            <if test="realGiftRatio != null">real_gift_ratio,</if>
            <if test="fully != null">fully,</if>
            <if test="discountRatio != null">discount_ratio,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="type != null">#{type},</if>
            <if test="validityDays != null">#{validityDays},</if>
            <if test="realBalance != null">#{realBalance},</if>
            <if test="giftBalance != null">#{giftBalance},</if>
            <if test="realGiftRatio != null">#{realGiftRatio},</if>
            <if test="fully != null">#{fully},</if>
            <if test="discountRatio != null">#{discountRatio},</if>
         </trim>
    </insert>

    <update id="updateConsumptionCard" parameterType="ConsumptionCard">
        update consumption_card
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="validityDays != null">validity_days = #{validityDays},</if>
            <if test="realBalance != null">real_balance = #{realBalance},</if>
            <if test="giftBalance != null">gift_balance = #{giftBalance},</if>
            <if test="realGiftRatio != null">real_gift_ratio = #{realGiftRatio},</if>
            <if test="fully != null">fully = #{fully},</if>
            <if test="discountRatio != null">discount_ratio = #{discountRatio},</if>
        </trim>
        where product_id = #{id}
    </update>

    <delete id="deleteConsumptionCardById" parameterType="Long">
        delete from consumption_card where id = #{id}
    </delete>

    <delete id="deleteConsumptionCardByIds" parameterType="String">
        delete from consumption_card where product_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>