<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.UploadedImageMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.UploadedImage">
    <!--@mbg.generated-->
    <!--@Table uploaded_image-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_size" jdbcType="INTEGER" property="fileSize" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="image_width" jdbcType="INTEGER" property="imageWidth" />
    <result column="image_height" jdbcType="INTEGER" property="imageHeight" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, employee_id, `name`, description, file_url, file_size, file_type, image_width, file_name,
    image_height, created_at, created_by
  </sql>

  <resultMap type="UploadedImage" id="UploadedImageResult">
    <result property="id" column="id"/>
    <result property="employeeId" column="employee_id"/>
    <result property="name" column="name"/>
    <result property="description" column="description"/>
    <result property="fileUrl" column="file_url"/>
    <result property="fileSize" column="file_size"/>
    <result property="fileType" column="file_type"/>
    <result property="imageWidth" column="image_width"/>
    <result property="imageHeight" column="image_height"/>
    <result property="createdAt" column="created_at"/>
    <result property="createdBy" column="created_by"/>
    <result property="fileName" column="file_name"/>
  </resultMap>

  <sql id="selectUploadedImageVo">
 select id, employee_id, name, description, file_url, file_size, file_type, image_width, image_height, created_at, created_by, file_name from uploaded_image
  </sql>
  <select id="selectUploadedImageList" parameterType="UploadedImage" resultMap="UploadedImageResult">
    <include refid="selectUploadedImageVo"/>
    <where>
      <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
      <if test="fileType != null and fileType != ''"> and file_type = #{fileType}</if>
      <if test="employeeId != null and employeeId != ''"> and employee_id like concat('%', #{employeeId}, '%')</if>
    </where>
  </select>
  <select id="selectUploadedImageById" parameterType="Long" resultMap="UploadedImageResult">
    <include refid="selectUploadedImageVo"/>
    where id = #{id}
  </select>
  <insert id="insertUploadedImage" parameterType="UploadedImage" useGeneratedKeys="true" keyProperty="id">
    insert into uploaded_image
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">employee_id,</if>
      <if test="name != null">name,</if>
      <if test="description != null">description,</if>
      <if test="fileUrl != null">file_url,</if>
      <if test="fileSize != null">file_size,</if>
      <if test="fileType != null">file_type,</if>
      <if test="imageWidth != null">image_width,</if>
      <if test="imageHeight != null">image_height,</if>
      <if test="createdAt != null">created_at,</if>
      <if test="createdBy != null">created_by,</if>
      <if test="fileName != null">file_name,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">#{employeeId},</if>
      <if test="name != null">#{name},</if>
      <if test="description != null">#{description},</if>
      <if test="fileUrl != null">#{fileUrl},</if>
      <if test="fileSize != null">#{fileSize},</if>
      <if test="fileType != null">#{fileType},</if>
      <if test="imageWidth != null">#{imageWidth},</if>
      <if test="imageHeight != null">#{imageHeight},</if>
      <if test="createdAt != null">#{createdAt},</if>
      <if test="createdBy != null">#{createdBy},</if>
      <if test="fileName != null">#{fileName},</if>
    </trim>
  </insert>
  <update id="updateUploadedImage" parameterType="UploadedImage">
    update uploaded_image
    <trim prefix="SET" suffixOverrides=",">
      <if test="employeeId != null">employee_id = #{employeeId},</if>
      <if test="name != null">name = #{name},</if>
      <if test="description != null">description = #{description},</if>
      <if test="fileUrl != null">file_url = #{fileUrl},</if>
      <if test="fileSize != null">file_size = #{fileSize},</if>
      <if test="fileType != null">file_type = #{fileType},</if>
      <if test="imageWidth != null">image_width = #{imageWidth},</if>
      <if test="imageHeight != null">image_height = #{imageHeight},</if>
      <if test="createdAt != null">created_at = #{createdAt},</if>
      <if test="createdBy != null">created_by = #{createdBy},</if>

      <if test="fileName != null">file_name = #{fileName},</if>
    </trim>
    where id = #{id}
  </update>
  <delete id="deleteUploadedImageById" parameterType="Long"> delete from uploaded_image where id = #{id} </delete>
  <delete id="deleteUploadedImageByIds" parameterType="String">
    delete from uploaded_image where id in
    <foreach item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
  </delete>
</mapper>