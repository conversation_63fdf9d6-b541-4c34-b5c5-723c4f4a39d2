<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.AppointmentMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.Appointment">
    <!--@mbg.generated-->
    <!--@Table appointment-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="member_id" jdbcType="BIGINT" property="memberId" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="scheduled_time" jdbcType="TIMESTAMP" property="scheduledTime" />
    <result column="status" jdbcType="OTHER" property="status" />
    <result column="service_record_id" jdbcType="BIGINT" property="serviceRecordId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, store_id, member_id, employee_id, product_id, scheduled_time, `status`, service_record_id, 
    created_at
  </sql>
</mapper>