<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.EmployeeInfoMapper">
    <resultMap id="BaseResultMap" type="com.meiye.api.domain.EmployeeInfo">
        <!--@mbg.generated-->
        <!--@Table employee_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="photo" jdbcType="VARCHAR" property="photo"/>
        <result column="identity_card" jdbcType="VARCHAR" property="identityCard"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="summary" jdbcType="LONGVARCHAR" property="summary"/>
        <result column="specialty" jdbcType="LONGVARCHAR" property="specialty"/>
        <result column="learning_ability" jdbcType="OTHER" property="learningAbility"/>
        <result column="education" jdbcType="VARCHAR" property="education"/>
        <result column="edu_time" jdbcType="DATE" property="eduTime"/>
        <result column="certificate" jdbcType="VARCHAR" property="certificate"/>
        <result column="employment_date" jdbcType="DATE" property="employmentDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        birthday,
        avatar,
        photo,
        identity_card,
        phone,
        summary,
        specialty,
        learning_ability,
        education,
        edu_time,
        certificate,
        employment_date
    </sql>

    <resultMap type="EmployeeInfo" id="EmployeeInfoResult">
        <result property="id" column="id"/>
        <result property="birthday" column="birthday"/>
        <result property="avatar" column="avatar"/>
        <result property="photo" column="photo"/>
        <result property="identityCard" column="identity_card"/>
        <result property="phone" column="phone"/>
        <result property="summary" column="summary"/>
        <result property="specialty" column="specialty"/>
        <result property="learningAbility" column="learning_ability"/>
        <result property="education" column="education"/>
        <result property="eduTime" column="edu_time"/>
        <result property="certificate" column="certificate"/>
        <result property="employmentDate" column="employment_date"/>
    </resultMap>

    <sql id="selectEmployeeInfoVo">
        select id,
               birthday,
               avatar,
               photo,
               identity_card,
               phone,
               summary,
               specialty,
               learning_ability,
               education,
               edu_time,
               certificate,
               employment_date
        from employee_info
    </sql>

    <select id="selectEmployeeInfoList" parameterType="EmployeeInfo" resultMap="EmployeeInfoResult">
        <include refid="selectEmployeeInfoVo"/>
        <where>
            <if test="birthday != null">
                and birthday = #{birthday}
            </if>
            <if test="phone != null  and phone != ''">
                and phone = #{phone}
            </if>
            <if test="learningAbility != null  and learningAbility != ''">
                and learning_ability = #{learningAbility}
            </if>
        </where>
    </select>

    <select id="selectEmployeeInfoById" parameterType="Long" resultMap="EmployeeInfoResult">
        <include refid="selectEmployeeInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmployeeInfo" parameterType="EmployeeInfo" useGeneratedKeys="true" keyProperty="id">
        insert into employee_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="avatar != null">
                avatar,
            </if>
            <if test="photo != null">
                photo,
            </if>
            <if test="identityCard != null and identityCard != ''">
                identity_card,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="summary != null">
                summary,
            </if>
            <if test="specialty != null">
                specialty,
            </if>
            <if test="learningAbility != null">
                learning_ability,
            </if>
            <if test="education != null">
                education,
            </if>
            <if test="eduTime != null">
                edu_time,
            </if>
            <if test="certificate != null">
                certificate,
            </if>
            <if test="employmentDate != null">
                employment_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="birthday != null">
                #{birthday},
            </if>
            <if test="avatar != null">
                #{avatar},
            </if>
            <if test="photo != null">
                #{photo},
            </if>
            <if test="identityCard != null and identityCard != ''">
                #{identityCard},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="summary != null">
                #{summary},
            </if>
            <if test="specialty != null">
                #{specialty},
            </if>
            <if test="learningAbility != null">
                #{learningAbility},
            </if>
            <if test="education != null">
                #{education},
            </if>
            <if test="eduTime != null">
                #{eduTime},
            </if>
            <if test="certificate != null">
                #{certificate},
            </if>
            <if test="employmentDate != null">
                #{employmentDate},
            </if>
        </trim>
    </insert>

    <update id="updateEmployeeInfo" parameterType="EmployeeInfo">
        update employee_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="birthday != null">
                birthday = #{birthday},
            </if>
            <if test="avatar != null">
                avatar = #{avatar},
            </if>
            <if test="photo != null">
                photo = #{photo},
            </if>
            <if test="identityCard != null and identityCard != ''">
                identity_card = #{identityCard},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="summary != null">
                summary = #{summary},
            </if>
            <if test="specialty != null">
                specialty = #{specialty},
            </if>
            <if test="learningAbility != null">
                learning_ability = #{learningAbility},
            </if>
            <if test="education != null">
                education = #{education},
            </if>
            <if test="eduTime != null">
                edu_time = #{eduTime},
            </if>
            <if test="certificate != null">
                certificate = #{certificate},
            </if>
            <if test="employmentDate != null">
                employment_date = #{employmentDate},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeInfoById" parameterType="Long">
        delete
        from employee_info
        where id = #{id}
    </delete>

    <delete id="deleteEmployeeInfoByIds" parameterType="String">
        delete
        from employee_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <resultMap type="com.meiye.api.vo.EmployeeInfoVo" id="EmployeeVoResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="pwdUpdateDate" column="pwd_update_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="rootStoreId" column="root_store_id"/>
        <result property="storeId" column="store_id"/>
        <result property="isManager" column="is_manager"/>
        <result property="postId" column="post_id"/>
        <association property="store" javaType="Store" resultMap="storeResult"/>
        <association property="rootStore" javaType="Store" resultMap="rootStoreResult"/>
        <association property="employeeInfo" javaType="EmployeeInfo" column="user_id">
            <id property="id" column="user_id"/>
            <result property="birthday" column="birthday"/>
            <result property="avatar" column="avatar"/>
            <result property="photo" column="photo"/>
            <result property="identityCard" column="identity_card"/>
            <result property="phone" column="phone"/>
            <result property="summary" column="summary"/>
            <result property="specialty" column="specialty"/>
            <result property="learningAbility" column="learning_ability"/>
            <result property="education" column="education"/>
            <result property="eduTime" column="edu_time"/>
            <result property="certificate" column="certificate"/>
            <result property="employmentDate" column="employment_date"/>
        </association>
    </resultMap>
    <resultMap type="EmployeeInfo" id="EmployeeResult">
        <!-- 指向e.id的别名 -->
        <id property="id" column="user_id"/>
        <result property="birthday" column="e_birthday"/>
        <result property="avatar" column="e_avatar"/>
        <result property="photo" column="e_photo"/>
        <result property="identityCard" column="e_identity_card"/>
        <result property="phone" column="e_phone"/>
        <result property="summary" column="e_summary"/>
        <result property="specialty" column="e_specialty"/>
        <result property="learningAbility" column="e_learning_ability"/>
        <result property="education" column="e_education"/>
        <result property="eduTime" column="e_edu_time"/>
        <result property="certificate" column="e_certificate"/>
        <result property="employmentDate" column="e_employment_date"/>
    </resultMap>
    <resultMap id="storeResult" type="Store">
        <result property="id" column="store_id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="storeCode" column="store_code"/>
    </resultMap>
    <resultMap id="rootStoreResult" type="Store">
        <result property="id" column="root_store_id"/>
        <result property="name" column="root_name"/>
        <result property="type" column="root_type"/>
        <result property="storeCode" column="root_store_code"/>
    </resultMap>


    <select id="selectEmployeeVoList" parameterType="com.meiye.api.vo.EmployeeInfoVo" resultMap="EmployeeVoResult">
        select u.user_id,
               u.dept_id,
               u.nick_name,
               u.user_name,
               u.email,
               u.avatar,
               u.phonenumber,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.root_store_id,
               u.store_id,
               u.is_manager,
               s.id          as store_id,
               s.name,
               s.store_code  as store_code,
               s.type,
               ss.id         as root_store_id,
               ss.name       as root_name,
               ss.store_code as root_store_code,
               ss.type       as root_type,
        e.id,
        e.birthday       as birthday,
        e.avatar         as avatar,
        e.photo          as photo,
        e.identity_card  as identity_card,
        e.phone          as phone,
        e.summary        as summary,
        e.specialty      as specialty,
        e.learning_ability as learning_ability,
        e.education      as education,
        e.edu_time       as edu_time,
        e.certificate    as certificate,
        e.employment_date as employment_date,
               up.post_id
        from sys_user u
                 left join store s on u.store_id = s.id
                 left join store ss on u.root_store_id = ss.id
                 left join employee_info e on u.user_id = e.id
                 left join sys_user_post up on u.user_id = up.user_id
        where u.del_flag = '0'
        <if test="userId != null and userId != 0">
            AND u.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="rootStoreId != null and rootStoreId != ''">
            AND u.root_store_id = #{rootStoreId}
        </if>
        <if test="storeId != null and storeId != ''">
            AND u.store_id = #{storeId}
        </if>
        <if test="isManager != null">
            AND u.is_manager = #{isManager}
        </if>
        <if test="postId != null">
            AND up.post_id = #{postId}
        </if>

        <if test="employeeInfo != null and employeeInfo.learningAbility != null and employeeInfo.learningAbility != ''">
            AND e.learning_ability = #{employeeInfo.learningAbility}
        </if>


        <if test="params.beginTime != null and params.beginTime != ''">
            <!-- 开始时间检索 -->
            AND date_format(u.create_time, '%Y%m%d') &gt;= date_format(#{params.beginTime}, '%Y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            <!-- 结束时间检索 -->
            AND date_format(u.create_time, '%Y%m%d') &lt;= date_format(#{params.endTime}, '%Y%m%d')
        </if>
        order by u.create_time desc
    </select>

    <select id="selectEmployeeVoById" resultMap="EmployeeVoResult">
        select u.user_id,
               u.dept_id,
               u.nick_name,
               u.user_name,
               u.email,
               u.avatar,
               u.phonenumber,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.root_store_id,
               u.store_id,
               u.is_manager,
               s.id          as store_id,
               s.name,
               s.store_code  as store_code,
               s.type,
               ss.id         as root_store_id,
               ss.name       as root_name,
               ss.store_code as root_store_code,
               ss.type       as root_type,
               e.id,
               e.birthday       as birthday,
               e.avatar         as avatar,
               e.photo          as photo,
               e.identity_card  as identity_card,
               e.phone          as phone,
               e.summary        as summary,
               e.specialty      as specialty,
               e.learning_ability as learning_ability,
               e.education      as education,
               e.edu_time       as edu_time,
               e.certificate    as certificate,
               e.employment_date as employment_date,
               up.post_id
        from sys_user u
                 left join store s on u.store_id = s.id
                 left join store ss on u.root_store_id = ss.id
                 left join employee_info e on u.user_id = e.id
                 left join sys_user_post up on u.user_id = up.user_id
        where e.id = #{id}
    </select>

    <!-- 添加 resultMap 定义 -->
    <resultMap id="EmployeeCommissionOptionResult" type="com.meiye.api.vo.EmployeeCommissionOptionVO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <collection property="EmployeeCommissionItemList" ofType="com.meiye.api.vo.EmployeeCommissionItem">
            <result property="storeProductId" column="storeProductId"/>
            <result property="cashCommissionRate" column="cashCommissionRate"/>
            <result property="balanceCommissionRate" column="balanceCommissionRate"/>
        </collection>
    </resultMap>

    <select id="selectECOptions" resultMap="EmployeeCommissionOptionResult">
        SELECT
            ei.id AS id,
            su.nick_name AS name,
            spc.store_product_id as storeProductId ,
            spc.cash_commission_rate as cashCommissionRate,
            spc.balance_commission_rate as   balanceCommissionRate
        FROM
            employee_info ei
                LEFT JOIN sys_user su ON ei.id = su.user_id
                LEFT JOIN sys_user_post sup ON sup.user_id = ei.id
                LEFT JOIN store_product_commission spc ON spc.position_id = sup.post_id
        where su.`status`='0'
        <if test="storeId != null">
            AND su.store_id = #{storeId}
        </if>
    </select>
</mapper>