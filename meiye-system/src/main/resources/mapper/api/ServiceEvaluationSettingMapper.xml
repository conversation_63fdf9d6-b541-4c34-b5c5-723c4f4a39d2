<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ServiceEvaluationSettingMapper">
    
    <resultMap type="ServiceEvaluationSetting" id="BaseResultMap">
        <!--@mbg.generated-->
        <!--@Table service_evaluation_setting-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="root_store_id" jdbcType="BIGINT" property="rootStoreId" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="department_id" jdbcType="BIGINT" property="departmentId" />
        <result column="position_id" jdbcType="BIGINT" property="positionId" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="root_store_name" jdbcType="VARCHAR" property="rootStoreName" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    </resultMap>
    
    <sql id="selectServiceEvaluationSettingVo">
        select 
            ses.id, 
            ses.`name`, 
            ses.root_store_id, 
            ses.store_id, 
            ses.department_id, 
            ses.position_id, 
            ses.`status`, 
            ses.total_weight, 
            ses.created_by, 
            ses.created_at,
            rs.name as root_store_name,
            s.name as store_name
        from service_evaluation_setting ses
        left join store rs on ses.root_store_id = rs.id
        left join store s on ses.store_id = s.id
    </sql>
    
    <select id="selectServiceEvaluationSettingList" parameterType="ServiceEvaluationSetting" resultMap="BaseResultMap">
        <include refid="selectServiceEvaluationSettingVo"/>
        <where>
            <if test="name != null  and name != ''"> and ses.`name` = #{name}</if>
            <if test="rootStoreId != null "> and ses.root_store_id = #{rootStoreId}</if>
            <if test="storeId != null "> and ses.store_id = #{storeId}</if>
            <if test="departmentId != null "> and ses.department_id = #{departmentId}</if>
            <if test="positionId != null "> and ses.position_id = #{positionId}</if>
            <if test="status != null  and status != ''"> and ses.`status` = #{status}</if>
            <if test="totalWeight != null "> and ses.total_weight = #{totalWeight}</if>
            <if test="createdBy != null "> and ses.created_by = #{createdBy}</if>
            <if test="createdAt != null "> and ses.created_at = #{createdAt}</if>
        </where>
    </select>
    
    <select id="selectServiceEvaluationSettingById" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectServiceEvaluationSettingVo"/>
        where ses.id = #{id}
    </select>
    
    <insert id="insertServiceEvaluationSetting" parameterType="ServiceEvaluationSetting" useGeneratedKeys="true" keyProperty="id">
        insert into service_evaluation_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">`name`,</if>
            <if test="rootStoreId != null">root_store_id,</if>
            <if test="storeId != null">store_id,</if>
            <if test="departmentId != null">department_id,</if>
            <if test="positionId != null">position_id,</if>
            <if test="status != null">`status`,</if>
            <if test="totalWeight != null">total_weight,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createdAt != null">created_at,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="rootStoreId != null">#{rootStoreId},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="departmentId != null">#{departmentId},</if>
            <if test="positionId != null">#{positionId},</if>
            <if test="status != null">#{status},</if>
            <if test="totalWeight != null">#{totalWeight},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createdAt != null">#{createdAt},</if>
        </trim>
    </insert>
    
    <update id="updateServiceEvaluationSetting" parameterType="ServiceEvaluationSetting">
        update service_evaluation_setting
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">`name` = #{name},</if>
            <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="positionId != null">position_id = #{positionId},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="totalWeight != null">total_weight = #{totalWeight},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>
    
    <delete id="deleteServiceEvaluationSettingById" parameterType="Long">
        delete from service_evaluation_setting where id = #{id}
    </delete>
    
    <delete id="deleteServiceEvaluationSettingByIds" parameterType="String">
        delete from service_evaluation_setting where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>