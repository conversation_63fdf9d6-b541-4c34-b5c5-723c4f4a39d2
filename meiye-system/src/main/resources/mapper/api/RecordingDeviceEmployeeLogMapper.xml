<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.RecordingDeviceEmployeeLogMapper">
    
    <resultMap type="RecordingDeviceEmployeeLog" id="RecordingDeviceEmployeeLogResult">
        <result property="id"    column="id"    />
        <result property="storeId"    column="store_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdAt"    column="created_at"    />
        <result property="action"    column="action"    />
    </resultMap>

    <sql id="selectRecordingDeviceEmployeeLogVo">
        select id, store_id, device_id, employee_id, created_by, created_at, action from recording_device_employee_log
    </sql>

    <select id="selectRecordingDeviceEmployeeLogList" parameterType="RecordingDeviceEmployeeLog" resultMap="RecordingDeviceEmployeeLogResult">
        <include refid="selectRecordingDeviceEmployeeLogVo"/>
        <where>  
            <if test="storeId != null "> and store_id = #{storeId}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="action != null  and action != ''"> and action = #{action}</if>
        </where>
    </select>
    
    <select id="selectRecordingDeviceEmployeeLogById" parameterType="Long" resultMap="RecordingDeviceEmployeeLogResult">
        <include refid="selectRecordingDeviceEmployeeLogVo"/>
        where id = #{id}
    </select>

    <select id="selectLastBindLog" resultType="com.meiye.api.domain.RecordingDeviceEmployeeLog">
        SELECT *
        FROM recording_device_employee_log
        WHERE device_id = #{deviceId}
          AND action = 'bind'
        ORDER BY created_at DESC
            LIMIT 1
    </select>

    <insert id="insertRecordingDeviceEmployeeLog" parameterType="RecordingDeviceEmployeeLog" useGeneratedKeys="true" keyProperty="id">
        insert into recording_device_employee_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeId != null">store_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="action != null">action,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeId != null">#{storeId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="action != null">#{action},</if>
         </trim>
    </insert>

    <update id="updateRecordingDeviceEmployeeLog" parameterType="RecordingDeviceEmployeeLog">
        update recording_device_employee_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="action != null">action = #{action},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRecordingDeviceEmployeeLogById" parameterType="Long">
        delete from recording_device_employee_log where id = #{id}
    </delete>

    <delete id="deleteRecordingDeviceEmployeeLogByIds" parameterType="String">
        delete from recording_device_employee_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>