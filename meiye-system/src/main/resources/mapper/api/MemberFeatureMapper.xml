<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.MemberFeatureMapper">
    
    <resultMap type="MemberFeature" id="MemberFeatureResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="memberFeatureId"    column="member_feature_id"    />
        <result property="value"    column="value"    />
        <result property="inputBy"    column="input_by"    />
        <result property="inputTime"    column="input_time"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="inputByName"    column="input_by_name"    />
        <result property="memberName"    column="member_name"    />
        <result property="featureTypeName"    column="feature_type_name"    />
    </resultMap>

    <sql id="selectMemberFeatureVo">
        select mf.id, mf.member_id, mf.member_feature_id, mf.value, mf.input_by, mf.input_time, mf.is_deleted,
               u.nick_name as input_by_name,
               m.name as member_name,
               mft.name as feature_type_name
        from member_feature mf
        left join sys_user u on mf.input_by = u.user_id
        left join member m on mf.member_id = m.id
        left join member_feature_type mft on mf.member_feature_id = mft.id
    </sql>

    <select id="selectMemberFeatureList" parameterType="MemberFeature" resultMap="MemberFeatureResult">
        <include refid="selectMemberFeatureVo"/>
        <where>  
            <if test="memberId != null "> and mf.member_id = #{memberId}</if>
            <if test="memberFeatureId != null "> and mf.member_feature_id = #{memberFeatureId}</if>
            <if test="value != null  and value != ''"> and mf.value like concat('%', #{value}, '%')</if>
            <if test="inputBy != null "> and mf.input_by = #{inputBy}</if>
            <if test="memberName != null and memberName != ''"> and m.name like concat('%', #{memberName}, '%')</if>
            <if test="featureTypeName != null and featureTypeName != ''"> and mft.name like concat('%', #{featureTypeName}, '%')</if>
            and (mf.is_deleted is null or mf.is_deleted = 0)
        </where>
    </select>
    
    <select id="selectMemberFeatureById" parameterType="Long" resultMap="MemberFeatureResult">
        <include refid="selectMemberFeatureVo"/>
        where mf.id = #{id}
    </select>

    <insert id="insertMemberFeature" parameterType="MemberFeature" useGeneratedKeys="true" keyProperty="id">
        insert into member_feature
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberId != null">member_id,</if>
            <if test="memberFeatureId != null">member_feature_id,</if>
            <if test="value != null and value != ''">value,</if>
            <if test="inputBy != null">input_by,</if>
            <if test="inputTime != null">input_time,</if>
            <if test="isDeleted != null">is_deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberId != null">#{memberId},</if>
            <if test="memberFeatureId != null">#{memberFeatureId},</if>
            <if test="value != null and value != ''">#{value},</if>
            <if test="inputBy != null">#{inputBy},</if>
            <if test="inputTime != null">#{inputTime},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
         </trim>
    </insert>

    <update id="updateMemberFeature" parameterType="MemberFeature">
        update member_feature
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="memberFeatureId != null">member_feature_id = #{memberFeatureId},</if>
            <if test="value != null and value != ''">value = #{value},</if>
            <if test="inputBy != null">input_by = #{inputBy},</if>
            <if test="inputTime != null">input_time = #{inputTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberFeatureById" parameterType="Long">
        delete from member_feature where id = #{id}
    </delete>

    <delete id="deleteMemberFeatureByIds" parameterType="String">
        delete from member_feature where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
