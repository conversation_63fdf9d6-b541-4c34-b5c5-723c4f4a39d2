<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.KpiMetricMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.KpiMetric">
    <!--@mbg.generated-->
    <!--@Table kpi_metric-->
    <result property="id" column="id"/>
    <result property="code" column="code"/>
    <result property="name" column="name"/>
    <result property="unit" column="unit"/>
  </resultMap>
  
  <sql id="selectKpiMetricVo">
    select id, code, name, unit from kpi_metric
  </sql>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    select id, code, name, unit from kpi_metric
  </sql>
  <select id="selectKpiMetricList" parameterType="KpiMetric" resultMap="BaseResultMap">
    <include refid="selectKpiMetricVo"/>
    <where>
      <if test="id != null"> and id = #{id}</if>
      <if test="code != null  and code != ''"> and code = #{code}</if>
      <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
      <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
    </where>
  </select>

  <select id="selectKpiMetricById" parameterType="Long" resultMap="BaseResultMap">
    <include refid="selectKpiMetricVo"/>
    where id = #{id}
  </select>

  <insert id="insertKpiMetric" parameterType="KpiMetric" useGeneratedKeys="true" keyProperty="id">
    insert into kpi_metric
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null and code != ''">code,</if>
      <if test="name != null and name != ''">name,</if>
      <if test="unit != null and unit != ''">unit,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null and code != ''">#{code},</if>
      <if test="name != null and name != ''">#{name},</if>
      <if test="unit != null and unit != ''">#{unit},</if>
    </trim>
  </insert>

  <update id="updateKpiMetric" parameterType="KpiMetric">
    update kpi_metric
    <trim prefix="SET" suffixOverrides=",">
      <if test="code != null and code != ''">code = #{code},</if>
      <if test="name != null and name != ''">name = #{name},</if>
      <if test="unit != null and unit != ''">unit = #{unit},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteKpiMetricById" parameterType="Long">
    delete from kpi_metric where id = #{id}
  </delete>

  <delete id="deleteKpiMetricByIds" parameterType="String">
    delete from kpi_metric where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

</mapper>