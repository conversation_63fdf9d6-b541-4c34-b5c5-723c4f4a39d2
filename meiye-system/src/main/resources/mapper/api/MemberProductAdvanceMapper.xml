<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.MemberProductAdvanceMapper">

    <resultMap type="MemberProductAdvance" id="MemberProductAdvanceResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="storeProductId"    column="store_product_id"    />
        <result property="cycleLength"    column="cycle_length"    />
        <result property="accept"    column="accept"    />
        <result property="refuse"    column="refuse"    />
        <result property="description"    column="description"    />
        <result property="createdByEmployeeId"    column="created_by_employee_id"    />
        <result property="createdAt"    column="created_at"    />
        <result property="createdByEmployeeName"    column="created_by_employee_name"    />
        <result property="memberName"    column="member_name"    />
    </resultMap>

    <sql id="selectMemberProductAdvanceVo">
        select mpa.id, mpa.member_id, mpa.store_product_id, mpa.cycle_length, mpa.accept, mpa.refuse, 
               mpa.description, mpa.created_by_employee_id, mpa.created_at,
               u.nick_name as created_by_employee_name,
               m.name as member_name
        from member_product_advance mpa
        left join sys_user u on mpa.created_by_employee_id = u.user_id
        left join member m on mpa.member_id = m.id
    </sql>

    <select id="selectMemberProductAdvanceList" parameterType="MemberProductAdvance" resultMap="MemberProductAdvanceResult">
        <include refid="selectMemberProductAdvanceVo"/>
        <where>
            <if test="memberId != null "> and mpa.member_id = #{memberId}</if>
            <if test="storeProductId != null "> and mpa.store_product_id = #{storeProductId}</if>
            <if test="cycleLength != null  and cycleLength != ''"> and mpa.cycle_length = #{cycleLength}</if>
            <if test="accept != null "> and mpa.accept = #{accept}</if>
            <if test="refuse != null "> and mpa.refuse = #{refuse}</if>
            <if test="description != null  and description != ''"> and mpa.description = #{description}</if>
            <if test="createdByEmployeeId != null "> and mpa.created_by_employee_id = #{createdByEmployeeId}</if>
            <if test="createdAt != null "> and mpa.created_at = #{createdAt}</if>
            <if test="memberName != null  and memberName != ''"> and m.name like concat('%', #{memberName}, '%')</if>
        </where>
    </select>

    <select id="selectMemberProductAdvanceById" parameterType="Long" resultMap="MemberProductAdvanceResult">
        <include refid="selectMemberProductAdvanceVo"/>
        where mpa.id = #{id}
    </select>

    <insert id="insertMemberProductAdvance" parameterType="MemberProductAdvance" useGeneratedKeys="true" keyProperty="id">
        insert into member_product_advance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberId != null">member_id,</if>
            <if test="storeProductId != null">store_product_id,</if>
            <if test="cycleLength != null">cycle_length,</if>
            <if test="accept != null">accept,</if>
            <if test="refuse != null">refuse,</if>
            <if test="description != null">description,</if>
            <if test="createdByEmployeeId != null">created_by_employee_id,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberId != null">#{memberId},</if>
            <if test="storeProductId != null">#{storeProductId},</if>
            <if test="cycleLength != null">#{cycleLength},</if>
            <if test="accept != null">#{accept},</if>
            <if test="refuse != null">#{refuse},</if>
            <if test="description != null">#{description},</if>
            <if test="createdByEmployeeId != null">#{createdByEmployeeId},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updateMemberProductAdvance" parameterType="MemberProductAdvance">
        update member_product_advance
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="storeProductId != null">store_product_id = #{storeProductId},</if>
            <if test="cycleLength != null">cycle_length = #{cycleLength},</if>
            <if test="accept != null">accept = #{accept},</if>
            <if test="refuse != null">refuse = #{refuse},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createdByEmployeeId != null">created_by_employee_id = #{createdByEmployeeId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberProductAdvanceById" parameterType="Long">
        delete from member_product_advance where id = #{id}
    </delete>

    <delete id="deleteMemberProductAdvanceByIds" parameterType="String">
        delete from member_product_advance where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>