<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.RecordingDeviceMapper">

    <resultMap type="RecordingDevice" id="RecordingDeviceResult">
        <result property="id"    column="id"    />
        <result property="storeId"    column="store_id"    />
        <result property="deviceSn"    column="device_sn"    />
        <result property="buyDate"    column="buy_date"    />
        <result property="status"    column="status"    />
        <result property="bound_status"    column="bound_status"    />
    </resultMap>

    <sql id="selectRecordingDeviceVo">
        select id, store_id, device_sn, buy_date, status,bound_status from recording_device
    </sql>

    <select id="selectRecordingDeviceList" parameterType="RecordingDevice" resultMap="RecordingDeviceResult">
        <include refid="selectRecordingDeviceVo"/>
        <where>
            <if test="storeId != null "> and store_id = #{storeId}</if>
            <if test="deviceSn != null  and deviceSn != ''"> and device_sn = #{deviceSn}</if>
            <if test="buyDate != null "> and buy_date = #{buyDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="bound_status != null  and bound_status != ''"> and bound_status = #{bound_status}</if>
        </where>
        ORDER BY buy_date DESC
    </select>

    <select id="selectRecordingDeviceById" parameterType="Long" resultMap="RecordingDeviceResult">
        <include refid="selectRecordingDeviceVo"/>
        where id = #{id}
    </select>

    <insert id="insertRecordingDevice" parameterType="RecordingDevice" useGeneratedKeys="true" keyProperty="id">
        insert into recording_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeId != null">store_id,</if>
            <if test="deviceSn != null">device_sn,</if>
            <if test="buyDate != null">buy_date,</if>
            <if test="status != null">status,</if>
            <if test="bound_status != null">bound_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeId != null">#{storeId},</if>
            <if test="deviceSn != null">#{deviceSn},</if>
            <if test="buyDate != null">#{buyDate},</if>
            <if test="status != null">#{status},</if>
            <if test="bound_status != null">#{bound_status},</if>
         </trim>
    </insert>

    <update id="updateRecordingDevice" parameterType="RecordingDevice">
        update recording_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="deviceSn != null">device_sn = #{deviceSn},</if>
            <if test="buyDate != null">buy_date = #{buyDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="bound_status != null">bound_status = #{bound_status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRecordingDeviceById" parameterType="Long">
        delete from recording_device where id = #{id}
    </delete>

    <delete id="deleteRecordingDeviceByIds" parameterType="String">
        delete from recording_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="selectDeviceWithAssignInfo" resultType="com.meiye.api.vo.RecordingDeviceVo">
        SELECT
            rd.id as id,
            rd.store_id as store_id,
            rd.device_sn as device_sn,
            rd.buy_date as buy_date,
            rde.id as "assignTo.id",
            rde.store_id as "assignTo.store_id",
            rde.device_id as "assignTo.device_id",
            rde.employee_id as "assignTo.employee_id"
        FROM recording_device rd
                 LEFT JOIN recording_device_employee rde ON rd.id = rde.device_id
    </select>

    <resultMap id="BaseResultMap" type="com.meiye.api.domain.RecordingDevice">
        <!--@mbg.generated-->
        <!--@Table recording_device-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="device_sn" jdbcType="VARCHAR" property="deviceSn" />
        <result column="buy_date" jdbcType="TIMESTAMP" property="buyDate" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, store_id, device_sn, buy_date
    </sql>
</mapper>