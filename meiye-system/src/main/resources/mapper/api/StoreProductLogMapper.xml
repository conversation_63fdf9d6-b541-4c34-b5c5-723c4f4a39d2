<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.StoreProductLogMapper">
    
    <resultMap type="StoreProductLog" id="StoreProductLogResult">
        <result property="id"    column="id"    />
        <result property="storeId"    column="store_id"    />
        <result property="storeProductId"    column="store_product_id"    />
        <result property="type"    column="type"    />
        <result property="originJson"    column="origin_json"    />
        <result property="currentJson"    column="current_json"    />
        <result property="createdAt"    column="created_at"    />
        <result property="createdByEmployeeId"    column="created_by_employee_id"    />
    </resultMap>

    <sql id="selectStoreProductLogVo">
        select id, store_id, store_product_id, type, origin_json, current_json, created_at, created_by_employee_id from store_product_log
    </sql>

    <select id="selectStoreProductLogList" parameterType="StoreProductLog" resultMap="StoreProductLogResult">
        <include refid="selectStoreProductLogVo"/>
        <where>  
            <if test="storeId != null "> and store_id = #{storeId}</if>
            <if test="storeProductId != null "> and store_product_id = #{storeProductId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="originJson != null  and originJson != ''"> and origin_json = #{originJson}</if>
            <if test="currentJson != null  and currentJson != ''"> and current_json = #{currentJson}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="createdByEmployeeId != null "> and created_by_employee_id = #{createdByEmployeeId}</if>
        </where>
    </select>
    
    <select id="selectStoreProductLogById" parameterType="Long" resultMap="StoreProductLogResult">
        <include refid="selectStoreProductLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertStoreProductLog" parameterType="StoreProductLog" useGeneratedKeys="true" keyProperty="id">
        insert into store_product_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeId != null">store_id,</if>
            <if test="storeProductId != null">store_product_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="originJson != null">origin_json,</if>
            <if test="currentJson != null">current_json,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="createdByEmployeeId != null">created_by_employee_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeId != null">#{storeId},</if>
            <if test="storeProductId != null">#{storeProductId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="originJson != null">#{originJson},</if>
            <if test="currentJson != null">#{currentJson},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="createdByEmployeeId != null">#{createdByEmployeeId},</if>
         </trim>
    </insert>

    <update id="updateStoreProductLog" parameterType="StoreProductLog">
        update store_product_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="storeProductId != null">store_product_id = #{storeProductId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="originJson != null">origin_json = #{originJson},</if>
            <if test="currentJson != null">current_json = #{currentJson},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="createdByEmployeeId != null">created_by_employee_id = #{createdByEmployeeId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStoreProductLogById" parameterType="Long">
        delete from store_product_log where id = #{id}
    </delete>

    <delete id="deleteStoreProductLogBySPId" parameterType="Long">
        delete from store_product_log where store_product_id = #{categoryId}
    </delete>

    <delete id="deleteStoreProductLogByIds" parameterType="String">
        delete from store_product_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>