<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.MemberLevelMapper">

    <resultMap type="MemberLevel" id="MemberLevelResult">
        <result property="id"    column="id"    />
        <result property="rootStoreId"    column="root_store_id"    />
        <result property="level"    column="level"    />
        <result property="name"    column="name"    />
        <result property="autoLevelModel"    column="auto_level_model"    />
        <result property="totalCash"    column="total_cash"    />
        <result property="rootStoreName"    column="root_store_name"    />
    </resultMap>

    <sql id="selectMemberLevelVo">
        select ml.id, ml.root_store_id, ml.level, ml.name, ml.auto_level_model, ml.total_cash,
               s.name as root_store_name
        from member_level ml
                 left join store s on ml.root_store_id = s.id
    </sql>

    <select id="selectMemberLevelList" parameterType="MemberLevel" resultMap="MemberLevelResult">
        <include refid="selectMemberLevelVo"/>
        <where>  
            <if test="rootStoreId != null "> and ml.root_store_id = #{rootStoreId}</if>
            <if test="level != null "> and ml.level = #{level}</if>
            <if test="name != null  and name != ''"> and ml.name like concat('%', #{name}, '%')</if>
            <if test="autoLevelModel != null  and autoLevelModel != ''"> and ml.auto_level_model = #{autoLevelModel}</if>
            <if test="totalCash != null "> and ml.total_cash = #{totalCash}</if>
        </where>
    </select>

    <select id="selectMemberLevelById" parameterType="Long" resultMap="MemberLevelResult">
        <include refid="selectMemberLevelVo"/>
        where ml.id = #{id}
    </select>

    <insert id="insertMemberLevel" parameterType="MemberLevel" useGeneratedKeys="true" keyProperty="id">
        insert into member_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id,</if>
            <if test="level != null">level,</if>
            <if test="name != null">name,</if>
            <if test="autoLevelModel != null">auto_level_model,</if>
            <if test="totalCash != null">total_cash,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">#{rootStoreId},</if>
            <if test="level != null">#{level},</if>
            <if test="name != null">#{name},</if>
            <if test="autoLevelModel != null">#{autoLevelModel},</if>
            <if test="totalCash != null">#{totalCash},</if>
         </trim>
    </insert>

    <update id="updateMemberLevel" parameterType="MemberLevel">
        update member_level
        <trim prefix="SET" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
            <if test="level != null">level = #{level},</if>
            <if test="name != null">name = #{name},</if>
            <if test="autoLevelModel != null">auto_level_model = #{autoLevelModel},</if>
            <if test="totalCash != null">total_cash = #{totalCash},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberLevelById" parameterType="Long">
        delete from member_level where id = #{id}
    </delete>

    <delete id="deleteMemberLevelByIds" parameterType="String">
        delete from member_level where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>