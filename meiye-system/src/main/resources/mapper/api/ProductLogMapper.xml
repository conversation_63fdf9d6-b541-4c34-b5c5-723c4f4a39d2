<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ProductLogMapper">
    
    <resultMap type="ProductLog" id="ProductLogResult">
        <result property="id"    column="id"    />
        <result property="rootStoreId"    column="root_store_id"    />
        <result property="productId"    column="product_id"    />
        <result property="type"    column="type"    />
        <result property="originJson"    column="origin_json"    />
        <result property="currentJson"    column="current_json"    />
        <result property="createdAt"    column="created_at"    />
        <result property="createdByEmployeeId"    column="created_by_employee_id"    />
    </resultMap>

    <sql id="selectProductLogVo">
        select id, root_store_id, product_id, type, origin_json, current_json, created_at, created_by_employee_id from product_log
    </sql>

    <select id="selectProductLogList" parameterType="ProductLog" resultMap="ProductLogResult">
        <include refid="selectProductLogVo"/>
        <where>  
            <if test="rootStoreId != null "> and root_store_id = #{rootStoreId}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="originJson != null  and originJson != ''"> and origin_json = #{originJson}</if>
            <if test="currentJson != null  and currentJson != ''"> and current_json = #{currentJson}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="createdByEmployeeId != null "> and created_by_employee_id = #{createdByEmployeeId}</if>
        </where>
    </select>
    
    <select id="selectProductLogById" parameterType="Long" resultMap="ProductLogResult">
        <include refid="selectProductLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertProductLog" parameterType="ProductLog" useGeneratedKeys="true" keyProperty="id">
        insert into product_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="originJson != null">origin_json,</if>
            <if test="currentJson != null">current_json,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="createdByEmployeeId != null">created_by_employee_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">#{rootStoreId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="originJson != null">#{originJson},</if>
            <if test="currentJson != null">#{currentJson},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="createdByEmployeeId != null">#{createdByEmployeeId},</if>
         </trim>
    </insert>

    <update id="updateProductLog" parameterType="ProductLog">
        update product_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="originJson != null">origin_json = #{originJson},</if>
            <if test="currentJson != null">current_json = #{currentJson},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="createdByEmployeeId != null">created_by_employee_id = #{createdByEmployeeId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProductLogById" parameterType="Long">
        delete from product_log where id = #{id}
    </delete>

    <delete id="deleteProductLogByIds" parameterType="String">
        delete from product_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>