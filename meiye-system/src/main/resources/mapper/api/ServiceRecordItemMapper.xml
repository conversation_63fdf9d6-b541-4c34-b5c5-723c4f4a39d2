<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ServiceRecordItemMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.ServiceRecordItem">
    <!--@mbg.generated-->
    <!--@Table service_record_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="service_record_id" jdbcType="BIGINT" property="serviceRecordId" />
    <result column="service_evaluation_setting_id" jdbcType="BIGINT" property="serviceEvaluationSettingId" />
    <result column="audio_text" jdbcType="LONGVARCHAR" property="audioText" />
    <result column="ai_level" jdbcType="OTHER" property="aiLevel" />
    <result column="ai_evaluation" jdbcType="LONGVARCHAR" property="aiEvaluation" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  
  <sql id="selectServiceRecordItemVo">
    select id, service_record_id, service_evaluation_setting_id, audio_text, ai_level, ai_evaluation, 
    created_at from service_record_item
  </sql>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, service_record_id, service_evaluation_setting_id, audio_text, ai_level, ai_evaluation, 
    created_at
  </sql>
  
  <select id="selectServiceRecordItemList" parameterType="ServiceRecordItem" resultMap="BaseResultMap">
    <include refid="selectServiceRecordItemVo"/>
    <where>
      <if test="serviceRecordId != null "> and service_record_id = #{serviceRecordId}</if>
      <if test="serviceEvaluationSettingId != null "> and service_evaluation_setting_id = #{serviceEvaluationSettingId}</if>
      <if test="audioText != null  and audioText != ''"> and audio_text = #{audioText}</if>
      <if test="aiLevel != null  and aiLevel != ''"> and ai_level = #{aiLevel}</if>
      <if test="aiEvaluation != null  and aiEvaluation != ''"> and ai_evaluation = #{aiEvaluation}</if>
      <if test="createdAt != null "> and created_at = #{createdAt}</if>
    </where>
  </select>

  <select id="selectServiceRecordItemById" parameterType="Long" resultMap="BaseResultMap">
    <include refid="selectServiceRecordItemVo"/>
    where id = #{id}
  </select>

  <insert id="insertServiceRecordItem" parameterType="ServiceRecordItem" useGeneratedKeys="true" keyProperty="id">
    insert into service_record_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serviceRecordId != null">service_record_id,</if>
      <if test="serviceEvaluationSettingId != null">service_evaluation_setting_id,</if>
      <if test="audioText != null">audio_text,</if>
      <if test="aiLevel != null and aiLevel != ''">ai_level,</if>
      <if test="aiEvaluation != null">ai_evaluation,</if>
      <if test="createdAt != null">created_at,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serviceRecordId != null">#{serviceRecordId},</if>
      <if test="serviceEvaluationSettingId != null">#{serviceEvaluationSettingId},</if>
      <if test="audioText != null">#{audioText},</if>
      <if test="aiLevel != null and aiLevel != ''">#{aiLevel},</if>
      <if test="aiEvaluation != null">#{aiEvaluation},</if>
      <if test="createdAt != null">#{createdAt},</if>
    </trim>
  </insert>

  <update id="updateServiceRecordItem" parameterType="ServiceRecordItem">
    update service_record_item
    <trim prefix="SET" suffixOverrides=",">
      <if test="serviceRecordId != null">service_record_id = #{serviceRecordId},</if>
      <if test="serviceEvaluationSettingId != null">service_evaluation_setting_id = #{serviceEvaluationSettingId},</if>
      <if test="audioText != null">audio_text = #{audioText},</if>
      <if test="aiLevel != null and aiLevel != ''">ai_level = #{aiLevel},</if>
      <if test="aiEvaluation != null">ai_evaluation = #{aiEvaluation},</if>
      <if test="createdAt != null">created_at = #{createdAt},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteServiceRecordItemById" parameterType="Long">
    delete from service_record_item where id = #{id}
  </delete>

  <delete id="deleteServiceRecordItemByIds" parameterType="String">
    delete from service_record_item where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

</mapper>