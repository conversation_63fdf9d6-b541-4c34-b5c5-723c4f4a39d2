<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.StoreProductMapper">
    <!-- 主结果映射 -->
    <resultMap type="StoreProduct" id="StoreProductResult">
        <result property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="productId" column="product_id"/>
        <result property="salePrice" column="sale_price"/>
        <result property="isAvailable" column="is_available"/>
    </resultMap>

    <resultMap id="storeProductVOMap" type="com.meiye.api.vo.StoreProductVO">
        <id property="storeProductId" column="sp_id"/>
        <result property="storeId" column="store_id"/>
        <result property="storeName" column="store_name"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="price" column="price"/>
        <result property="salePrice" column="sale_price"/>
        <result property="isAvailable" column="is_available"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryType" column="category_type"/>
        <result property="categoryName" column="category_name"/>

        <result property="consumptionCardId" column="cc_id"/>
        <result property="consumptionCardType" column="cc_type"/>
        <result property="consumptionCardTypeName" column="consumption_card_type_name"/>
        <result property="consumptionCardValidityDays" column="validity_days"/>
        <result property="realBalance" column="real_balance"/>
        <result property="giftBalance" column="gift_balance"/>
        <result property="realGiftRatio" column="real_gift_ratio"/>
        <result property="discountRatio" column="discount_ratio"/>

        <!-- 关联集合 -->
        <collection property="storeProductCommissionList" resultMap="storeProductCommissionMap"/>
        <collection property="productItemList" resultMap="productItemMap"/>
        <collection property="productCommissionList" resultMap="productCommissionMap"/>
    </resultMap>

    <!-- 店面提成映射 -->
    <resultMap id="storeProductCommissionMap" type="com.meiye.api.vo.StoreProductVO$StoreProductCommissionVo">
        <id property="id" column="spc_id"/>
        <result property="storeProductId" column="spc_store_product_id"/>
        <result property="departmentId" column="department_id"/>
        <result property="positionId" column="position_id"/>
        <result property="cashCommissionRate" column="spc_cash_commission_rate"/>
        <result property="balanceCommissionRate" column="spc_balance_commission_rate"/>
    </resultMap>

    <!-- 产品子项映射 -->
    <resultMap id="productItemMap" type="com.meiye.api.vo.StoreProductVO$ProductItemVO">
        <id property="id" column="pi_id"/>
        <result property="mainProductId" column="pi_main_product_id"/>
        <result property="subProductId" column="sub_product_id"/>
        <result property="equalPrice" column="equal_price"/>
        <result property="quantity" column="quantity"/>
    </resultMap>

    <!-- 默认提成映射 -->
    <resultMap id="productCommissionMap" type="com.meiye.api.vo.StoreProductVO$ProductCommissionVO">
        <id property="id" column="pc_id"/>
        <result property="productId" column="pc_product_id"/>
        <result property="departmentId" column="pc_department_id"/>
        <result property="positionId" column="pc_position_id"/>
        <result property="cashCommissionRate" column="pc_cash_commission_rate"/>
        <result property="balanceCommissionRate" column="pc_balance_commission_rate"/>
    </resultMap>


    <select id="queryStoreProductList" resultMap="storeProductVOMap">
        SELECT
            /* 店面销售物基础信息 */
            sp.id                        AS sp_id,
            sp.store_id,
            s.name                       AS store_name,
            sp.product_id,
            p.price,
            p.name                       AS product_name,
            sp.sale_price,
            sp.is_available,
            p.category_id,
            pc.type                      AS category_type,
            pc.name                      AS category_name,

            /* 消费卡信息 */
            cc.id                        AS cc_id,
            cc.validity_days,
            cc.type                      AS cc_type,
            CASE cc.type
                WHEN 'balance' THEN '储值卡'
                WHEN 'discount' THEN '优惠卡'
                WHEN 'times' THEN '次卡'
                WHEN 'experience' THEN '体验卡'
                ELSE '未知'
                END AS consumption_card_type_name,
            cc.real_balance,
            cc.gift_balance,
            cc.real_gift_ratio,
            cc.discount_ratio,

            /* 店面提成 */
            spc.id                       AS spc_id,
            spc.store_product_id         AS spc_store_product_id,
            spc.department_id,
            spc.position_id,
            spc.cash_commission_rate     AS spc_cash_commission_rate,
            spc.balance_commission_rate  AS spc_balance_commission_rate,

            /* 产品子项 */
            pi.id                        AS pi_id,
            pi.main_product_id           AS pi_main_product_id,
            pi.sub_product_id,
            pi.equal_price,
            pi.quantity,

            /* 默认提成 */
            pcom.id                      AS pc_id,
            pcom.product_id              AS pc_product_id,
            pcom.department_id           AS pc_department_id,
            pcom.position_id             AS pc_position_id,
            pcom.cash_commission_rate    AS pc_cash_commission_rate,
            pcom.balance_commission_rate AS pc_balance_commission_rate
        FROM store_product sp
                 LEFT JOIN store s ON sp.store_id = s.id
                 LEFT JOIN product p ON sp.product_id = p.id
                 LEFT JOIN product_category pc ON p.category_id = pc.id
                 LEFT JOIN consumption_card cc ON p.id = cc.product_id
                 LEFT JOIN store_product_commission spc ON sp.id = spc.store_product_id
                 LEFT JOIN product_item pi ON p.id = pi.main_product_id
                 LEFT JOIN product_commission pcom ON p.id = pcom.product_id
        <where>
            sp.is_available = 1
            <if test="query.storeId != null "> and sp.store_id = #{query.storeId}</if>
        </where>
    </select>

    <sql id="selectStoreProductVo"> select id, store_id, product_id, sale_price, is_available from store_product </sql>
    <select id="selectStoreProductList" parameterType="StoreProduct" resultMap="StoreProductResult">
        <include refid="selectStoreProductVo"/>
        <where>
            <if test="storeId != null "> and store_id = #{storeId}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="salePrice != null "> and sale_price = #{salePrice}</if>
            <if test="isAvailable != null "> and is_available = #{isAvailable}</if>
        </where>
    </select>
    <select id="selectStoreProductById" parameterType="Long" resultMap="StoreProductResult">
        <include refid="selectStoreProductVo"/>
        where id = #{id}
    </select>
    <insert id="insertStoreProduct" parameterType="StoreProduct" useGeneratedKeys="true" keyProperty="id">
        insert into store_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeId != null">store_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="salePrice != null">sale_price,</if>
            <if test="isAvailable != null">is_available,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeId != null">#{storeId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="salePrice != null">#{salePrice},</if>
            <if test="isAvailable != null">#{isAvailable},</if>
        </trim>
    </insert>
    <update id="updateStoreProduct" parameterType="StoreProduct">
        update store_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="salePrice != null">sale_price = #{salePrice},</if>
            <if test="isAvailable != null">is_available = #{isAvailable},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteStoreProductById" parameterType="Long"> delete from store_product where id = #{id} </delete>
    <delete id="deleteStoreProductByIds" parameterType="String">
        delete from store_product where id in
        <foreach item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
    </delete>
    <delete id="deleteStoreProductByPId" parameterType="Long">
        delete from store_product where product_id = #{id}
    </delete>
</mapper>