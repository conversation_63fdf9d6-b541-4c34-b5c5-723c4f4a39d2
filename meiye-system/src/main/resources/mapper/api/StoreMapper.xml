<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.StoreMapper">
    <resultMap id="BaseResultMap" type="com.meiye.api.domain.Store">
        <!--@mbg.generated-->
        <!--@Table store-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="logo_id" jdbcType="BIGINT" property="logoId" />
        <result column="photo_ids" jdbcType="VARCHAR" property="photoIds" />
        <result column="parent_id" jdbcType="BIGINT" property="parentId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="type" jdbcType="OTHER" property="type" />
        <result column="city_code" jdbcType="CHAR" property="cityCode" />
        <result column="address" jdbcType="LONGVARCHAR" property="address" />
        <result column="description" jdbcType="LONGVARCHAR" property="description" />
        <result column="is_active" jdbcType="BOOLEAN" property="isActive" />
        <result column="non_active_days" jdbcType="INTEGER" property="nonActiveDays" />
        <result column="use_same_script" jdbcType="BOOLEAN" property="useSameScript" />
        <result column="lat" jdbcType="DECIMAL" property="lat" />
        <result column="lon" jdbcType="DECIMAL" property="lon" />
        <result column="check_in_around" jdbcType="DECIMAL" property="checkInAround" />
        <result column="start_time" jdbcType="TIME" property="startTime" />
        <result column="end_time" jdbcType="TIME" property="endTime" />
        <result column="evaluation_by_ai" jdbcType="BOOLEAN" property="evaluationByAi" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="store_code" jdbcType="CHAR" property="storeCode" />

    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, logo_id, photo_ids, parent_id, `name`, `type`, city_code, address, description,
        is_active, non_active_days, use_same_script, lat, lon, check_in_around, start_time,
        end_time, evaluation_by_ai, created_at,store_code
    </sql>

    <resultMap type="SysStore" id="StoreResult">
        <result property="id"    column="id"    />
        <result property="logoId"    column="logo_id"    />
        <result property="photoIds"    column="photo_ids"    />
        <result property="parentId"    column="parent_id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="cityCode"    column="city_code"    />
        <result property="address"    column="address"    />
        <result property="description"    column="description"    />
        <result property="isActive"    column="is_active"    />
        <result property="nonActiveDays"    column="non_active_days"    />
        <result property="useSameScript"    column="use_same_script"    />
        <result property="lat"    column="lat"    />
        <result property="lon"    column="lon"    />
        <result property="checkInAround"    column="check_in_around"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="evaluationByAi"    column="evaluation_by_ai"    />
        <result property="createdAt"    column="created_at"    />
        <result property="logoUrl"    column="logo_url"    />
        <result property="photoUrl"    column="photo_url"    />
        <result property="status"    column="status"    />
        <result property="lockedReason"    column="locked_reason"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="createdBy"    column="created_by"    />
        <result column="store_code" property="storeCode" />
    </resultMap>

    <resultMap type="Store" id="StoreResult2">
        <result property="id"    column="id"    />
        <result property="logoId"    column="logo_id"    />
        <result property="photoIds"    column="photo_ids"    />
        <result property="parentId"    column="parent_id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="cityCode"    column="city_code"    />
        <result property="address"    column="address"    />
        <result property="description"    column="description"    />
        <result property="isActive"    column="is_active"    />
        <result property="nonActiveDays"    column="non_active_days"    />
        <result property="useSameScript"    column="use_same_script"    />
        <result property="lat"    column="lat"    />
        <result property="lon"    column="lon"    />
        <result property="checkInAround"    column="check_in_around"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="evaluationByAi"    column="evaluation_by_ai"    />
        <result property="createdAt"    column="created_at"    />
        <result property="logoUrl"    column="logo_url"    />
        <result property="photoUrl"    column="photo_url"    />
        <result property="status"    column="status"    />
        <result property="lockedReason"    column="locked_reason"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="createdBy"    column="created_by"    />
        <result column="store_code" property="storeCode" />
    </resultMap>

    <sql id="selectStoreVo">
        select id,store_code, logo_id, photo_ids, parent_id, name, type, city_code, address, description, is_active, non_active_days, use_same_script, lat, lon, check_in_around, start_time, end_time, evaluation_by_ai, created_at, logo_url, photo_url, status, locked_reason, contact_person, contact_phone, created_by from store
    </sql>

    <select id="selectStoreList" parameterType="SysStore" resultMap="StoreResult">
        <include refid="selectStoreVo"/>
        <where>
            <if test="logoId != null "> and logo_id = #{logoId}</if>
            <if test="photoIds != null "> and photo_ids = #{photoIds}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="cityCode != null  and cityCode != ''"> and city_code = #{cityCode}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
            <if test="nonActiveDays != null "> and non_active_days = #{nonActiveDays}</if>
            <if test="useSameScript != null "> and use_same_script = #{useSameScript}</if>
            <if test="lat != null "> and lat = #{lat}</if>
            <if test="lon != null "> and lon = #{lon}</if>
            <if test="checkInAround != null "> and check_in_around = #{checkInAround}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="evaluationByAi != null "> and evaluation_by_ai = #{evaluationByAi}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="logoUrl != null  and logoUrl != ''"> and logo_url = #{logoUrl}</if>
            <if test="photoUrl != null  and photoUrl != ''"> and photo_url = #{photoUrl}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="lockedReason != null  and lockedReason != ''"> and locked_reason = #{lockedReason}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="createdBy != null "> and created_by = #{createdBy}</if>
            <if test="storeCode != null  and storeCode != ''"> and store_code = #{storeCode}</if>
            <if test="params.beginCreatedAt != null and params.beginCreatedAt != ''">
                and date(created_at)  &gt;= date(#{params.beginCreatedAt})
            </if>
            <if test="params.endCreatedAt != null and params.endCreatedAt != ''">
                and date(created_at)  &lt;= date(#{params.endCreatedAt})
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectStoreById" parameterType="Long" resultMap="StoreResult">
        <include refid="selectStoreVo"/>
        where id = #{id}
    </select>

    <select id="selectNoSysStoreById" parameterType="Long" resultMap="StoreResult2">
        <include refid="selectStoreVo"/>
        where id = #{id}
    </select>

    <select id="selectSonById" parameterType="Long" resultMap="StoreResult2">
        <include refid="selectStoreVo"/>
        where parent_id = #{id}
    </select>

    <select id="selectSonsById" parameterType="Long" resultMap="StoreResult2">
        <include refid="selectStoreVo"/>
        where parent_id = #{id}
    </select>

    <select id="selectTypeById" parameterType="Long" resultMap="StoreResult2">
        <include refid="selectStoreVo"/>
        where id = #{id}
    </select>

    <insert id="insertStore" parameterType="SysStore" useGeneratedKeys="true" keyProperty="id">
        insert into store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="logoId != null">logo_id,</if>
            <if test="photoIds != null">photo_ids,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="address != null">address,</if>
            <if test="description != null">description,</if>
            <if test="isActive != null">is_active,</if>
            <if test="nonActiveDays != null">non_active_days,</if>
            <if test="useSameScript != null">use_same_script,</if>
            <if test="lat != null">lat,</if>
            <if test="lon != null">lon,</if>
            <if test="checkInAround != null">check_in_around,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="evaluationByAi != null">evaluation_by_ai,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="logoUrl != null">logo_url,</if>
            <if test="photoUrl != null">photo_url,</if>
            <if test="status != null">status,</if>
            <if test="lockedReason != null">locked_reason,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="storeCode != null">store_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="logoId != null">#{logoId},</if>
            <if test="photoIds != null">#{photoIds},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="address != null">#{address},</if>
            <if test="description != null">#{description},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="nonActiveDays != null">#{nonActiveDays},</if>
            <if test="useSameScript != null">#{useSameScript},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lon != null">#{lon},</if>
            <if test="checkInAround != null">#{checkInAround},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="evaluationByAi != null">#{evaluationByAi},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="logoUrl != null">#{logoUrl},</if>
            <if test="photoUrl != null">#{photoUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="lockedReason != null">#{lockedReason},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="storeCode != null">#{storeCode},</if>
        </trim>
    </insert>

    <update id="updateStore" parameterType="SysStore">
        update store
        <trim prefix="SET" suffixOverrides=",">
            <if test="logoId != null">logo_id = #{logoId},</if>
            <if test="photoIds != null">photo_ids = #{photoIds},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="address != null">address = #{address},</if>
            <if test="description != null">description = #{description},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="nonActiveDays != null">non_active_days = #{nonActiveDays},</if>
            <if test="useSameScript != null">use_same_script = #{useSameScript},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="checkInAround != null">check_in_around = #{checkInAround},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="evaluationByAi != null">evaluation_by_ai = #{evaluationByAi},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="logoUrl != null">logo_url = #{logoUrl},</if>
            <if test="photoUrl != null">photo_url = #{photoUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="lockedReason != null">locked_reason = #{lockedReason},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="storeCode != null">store_code = #{storeCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStoreById" parameterType="Long">
        delete from store where id = #{id}
    </delete>

    <delete id="deleteStoreByIds" parameterType="String">
        delete from store where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>