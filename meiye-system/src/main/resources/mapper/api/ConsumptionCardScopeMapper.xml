<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ConsumptionCardScopeMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.ConsumptionCardScope">
    <!--@mbg.generated-->
    <!--@Table consumption_card_scope-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="support_product_id" jdbcType="BIGINT" property="supportProductId" />
    <result column="discount_type" jdbcType="OTHER" property="discountType" />
    <result column="discount_fixed" jdbcType="DECIMAL" property="discountFixed" />
    <result column="discount_ratio" jdbcType="DECIMAL" property="discountRatio" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_id, support_product_id, discount_type, discount_fixed, discount_ratio
  </sql>
</mapper>