<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.domain.DailyStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.DailyStatistics">
    <!--@mbg.generated-->
    <!--@Table daily_statistics-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="root_store_id" jdbcType="BIGINT" property="rootStoreId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="mployee_id" jdbcType="BIGINT" property="mployeeId" />
    <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="post_id" jdbcType="BIGINT" property="postId" />
    <result column="indicator_id" jdbcType="BIGINT" property="indicatorId" />
    <result column="stat_date" jdbcType="VARCHAR" property="statDate" />
    <result column="stat_value" jdbcType="DECIMAL" property="statValue" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, root_store_id, store_id, mployee_id, dept_id, post_id, indicator_id, stat_date,
    stat_value, created_at, updated_at
  </sql>
</mapper>
