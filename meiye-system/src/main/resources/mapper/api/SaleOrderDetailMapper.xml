<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.SaleOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.SaleOrderDetail">
    <!--@mbg.generated-->
    <!--@Table sale_order_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sale_order_id" jdbcType="BIGINT" property="saleOrderId" />
    <result column="store_product_id" jdbcType="BIGINT" property="storeProductId" />
    <result column="sold_card_id" jdbcType="BIGINT" property="soldCardId" />
    <result column="product_snapshot" jdbcType="LONGVARCHAR" property="productSnapshot" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="consumption_card_discount" jdbcType="DECIMAL" property="consumptionCardDiscount" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="payment_type" jdbcType="OTHER" property="paymentType" />
    <result column="payment_card_id" jdbcType="BIGINT" property="paymentCardId" />
    <result column="payment_method_id" jdbcType="BIGINT" property="paymentMethodId" />
    <result column="from_employee_id" jdbcType="BIGINT" property="fromEmployeeId" />
    <result column="removed" jdbcType="BOOLEAN" property="removed" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sale_order_id, store_product_id, sold_card_id, product_snapshot, quantity, price, 
    consumption_card_discount, amount, payment_type, payment_card_id, payment_method_id, 
    from_employee_id, removed
  </sql>
</mapper>