<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.KpiResultMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.KpiResult">
    <!--@mbg.generated-->
    <!--@Table kpi_result-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="setting_id" jdbcType="BIGINT" property="settingId" />
    <result column="actual_value" jdbcType="DECIMAL" property="actualValue" />
    <result column="calculated_at" jdbcType="TIMESTAMP" property="calculatedAt" />
  </resultMap>
  
  <sql id="selectKpiResultVo">
    select id, setting_id, actual_value, calculated_at from kpi_result
  </sql>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, setting_id, actual_value, calculated_at
  </sql>
  <select id="selectKpiResultList" parameterType="KpiResult" resultMap="BaseResultMap">
    <include refid="selectKpiResultVo"/>
    <where>
      <if test="settingId != null "> and setting_id = #{settingId}</if>
      <if test="actualValue != null "> and actual_value = #{actualValue}</if>
      <if test="calculatedAt != null "> and calculated_at = #{calculatedAt}</if>
    </where>
  </select>
  <select id="selectKpiResultById" parameterType="Long" resultMap="BaseResultMap">
    <include refid="selectKpiResultVo"/>
    where id = #{id}
  </select>
  <insert id="insertKpiResult" parameterType="KpiResult" useGeneratedKeys="true" keyProperty="id">
    insert into kpi_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="settingId != null">setting_id,</if>
      <if test="actualValue != null">actual_value,</if>
      <if test="calculatedAt != null">calculated_at,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="settingId != null">#{settingId},</if>
      <if test="actualValue != null">#{actualValue},</if>
      <if test="calculatedAt != null">#{calculatedAt},</if>
    </trim>
  </insert>
  <update id="updateKpiResult" parameterType="KpiResult">
    update kpi_result
    <trim prefix="SET" suffixOverrides=",">
      <if test="settingId != null">setting_id = #{settingId},</if>
      <if test="actualValue != null">actual_value = #{actualValue},</if>
      <if test="calculatedAt != null">calculated_at = #{calculatedAt},</if>
    </trim>
    where id = #{id}
  </update>
  <delete id="deleteKpiResultById" parameterType="Long"> delete from kpi_result where id = #{id} </delete>
  <delete id="deleteKpiResultByIds" parameterType="String">
    delete from kpi_result where id in
    <foreach item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
  </delete>
</mapper>