<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.SoldCardBalanceLogMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.SoldCardBalanceLog">
    <!--@mbg.generated-->
    <!--@Table sold_card_balance_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sold_card_id" jdbcType="BIGINT" property="soldCardId" />
    <result column="sale_store_product_id" jdbcType="BIGINT" property="saleStoreProductId" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="ref_order_id" jdbcType="BIGINT" property="refOrderId" />
    <result column="ref_order_detail_id" jdbcType="BIGINT" property="refOrderDetailId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sold_card_id, sale_store_product_id, quantity, ref_order_id, ref_order_detail_id, 
    created_at
  </sql>
</mapper>