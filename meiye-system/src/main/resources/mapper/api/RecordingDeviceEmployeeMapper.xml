<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.RecordingDeviceEmployeeMapper">

    <resultMap type="RecordingDeviceEmployee" id="RecordingDeviceEmployeeResult">
        <result property="id"    column="id"    />
        <result property="storeId"    column="store_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="employeeId"    column="employee_id"    />
    </resultMap>

    <sql id="selectRecordingDeviceEmployeeVo">
        select id, store_id, device_id, employee_id from recording_device_employee
    </sql>

    <select id="selectRecordingDeviceEmployeeList" parameterType="RecordingDeviceEmployee" resultMap="RecordingDeviceEmployeeResult">
        <include refid="selectRecordingDeviceEmployeeVo"/>
        <where>
            <if test="storeId != null "> and store_id = #{storeId}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
        </where>
    </select>

    <select id="selectRecordingDeviceEmployeeById" parameterType="Long" resultMap="RecordingDeviceEmployeeResult">
        <include refid="selectRecordingDeviceEmployeeVo"/>
        where id = #{id}
    </select>

    <select id="selectByEmployeeId" resultType="RecordingDeviceEmployee">
        SELECT * FROM recording_device_employee
        WHERE employee_id = #{employeeId}
    </select>

    <select id="selectByDeviceId" parameterType="java.lang.Long" resultType="RecordingDeviceEmployee">
        select id, store_id, device_id, employee_id
        from recording_device_employee
        where device_id = #{deviceId}
    </select>

    <insert id="insertRecordingDeviceEmployee" parameterType="RecordingDeviceEmployee" useGeneratedKeys="true" keyProperty="id">
        insert into recording_device_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeId != null">store_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="employeeId != null">employee_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeId != null">#{storeId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="employeeId != null">#{employeeId},</if>
         </trim>
    </insert>

    <update id="updateRecordingDeviceEmployee" parameterType="RecordingDeviceEmployee">
        update recording_device_employee
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            employee_id = #{employeeId},
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRecordingDeviceEmployeeById" parameterType="Long">
        delete from recording_device_employee where id = #{id}
    </delete>

    <delete id="deleteRecordingDeviceEmployeeByDeviceId" parameterType="Long">
        delete from recording_device_employee where device_id = #{id}
    </delete>

    <delete id="deleteRecordingDeviceEmployeeByIds" parameterType="String">
        delete from recording_device_employee where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteRecordingDeviceEmployeeByDeviceIds" parameterType="String">
        delete from recording_device_employee where device_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>