<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ServiceEvaluationGroupMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.ServiceEvaluationGroup">
    <!--@mbg.generated-->
    <!--@Table service_evaluation_group-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="service_evaluation_setting_id" jdbcType="BIGINT" property="serviceEvaluationSettingId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
    <result column="dimension_list" jdbcType="LONGVARCHAR" property="dimensionList" />
  </resultMap>
  <resultMap id="ServiceEvaluationGroupResult" type="com.meiye.api.domain.ServiceEvaluationGroup">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="service_evaluation_setting_id" jdbcType="BIGINT" property="serviceEvaluationSettingId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
    <result column="dimension_list" jdbcType="LONGVARCHAR" property="dimensionList" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, service_evaluation_setting_id, `name`, total_weight, dimension_list
  </sql>
  <sql id="selectServiceEvaluationGroupVo">
    select id, service_evaluation_setting_id, name, total_weight, dimension_list from service_evaluation_group
  </sql>

  <select id="selectServiceEvaluationGroupList" parameterType="ServiceEvaluationGroup" resultMap="ServiceEvaluationGroupResult">
    <include refid="selectServiceEvaluationGroupVo"/>
    <where>
      <if test="serviceEvaluationSettingId != null "> and service_evaluation_setting_id = #{serviceEvaluationSettingId}</if>
      <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
      <if test="totalWeight != null "> and total_weight = #{totalWeight}</if>
      <if test="dimensionList != null  and dimensionList != ''"> and dimension_list = #{dimensionList}</if>
    </where>
  </select>

  <select id="selectServiceEvaluationGroupById" parameterType="Long" resultMap="ServiceEvaluationGroupResult">
    <include refid="selectServiceEvaluationGroupVo"/>
    where id = #{id}
  </select>

  <insert id="insertServiceEvaluationGroup" parameterType="ServiceEvaluationGroup" useGeneratedKeys="true" keyProperty="id">
    insert into service_evaluation_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serviceEvaluationSettingId != null">service_evaluation_setting_id,</if>
      <if test="name != null and name != ''">name,</if>
      <if test="totalWeight != null">total_weight,</if>
      <if test="dimensionList != null">dimension_list,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serviceEvaluationSettingId != null">#{serviceEvaluationSettingId},</if>
      <if test="name != null and name != ''">#{name},</if>
      <if test="totalWeight != null">#{totalWeight},</if>
      <if test="dimensionList != null">#{dimensionList},</if>
    </trim>
  </insert>

  <update id="updateServiceEvaluationGroup" parameterType="ServiceEvaluationGroup">
    update service_evaluation_group
    <trim prefix="SET" suffixOverrides=",">
      <if test="serviceEvaluationSettingId != null">service_evaluation_setting_id = #{serviceEvaluationSettingId},</if>
      <if test="name != null and name != ''">name = #{name},</if>
      <if test="totalWeight != null">total_weight = #{totalWeight},</if>
      <if test="dimensionList != null">dimension_list = #{dimensionList},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteServiceEvaluationGroupById" parameterType="Long">
    delete from service_evaluation_group where id = #{id}
  </delete>

  <delete id="deleteServiceEvaluationGroupByIds" parameterType="String">
    delete from service_evaluation_group where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

</mapper>