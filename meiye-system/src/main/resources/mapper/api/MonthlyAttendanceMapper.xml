<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.MonthlyAttendanceMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.MonthlyAttendance">
    <!--@mbg.generated-->
    <!--@Table monthly_attendance-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="be_late_times" jdbcType="INTEGER" property="beLateTimes" />
    <result column="leave_early_times" jdbcType="INTEGER" property="leaveEarlyTimes" />
    <result column="out_area_times" jdbcType="INTEGER" property="outAreaTimes" />
    <result column="job_days" jdbcType="INTEGER" property="jobDays" />
    <result column="good_days" jdbcType="INTEGER" property="goodDays" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, employee_id, `year`, `month`, be_late_times, leave_early_times, out_area_times, 
    job_days, good_days
  </sql>
</mapper>