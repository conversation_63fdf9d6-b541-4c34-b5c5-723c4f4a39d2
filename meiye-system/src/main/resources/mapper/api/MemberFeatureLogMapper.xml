<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.MemberFeatureLogMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.MemberFeatureLog">
    <!--@mbg.generated-->
    <!--@Table member_feature_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="feature_type_id" jdbcType="BIGINT" property="featureTypeId" />
    <result column="origin_feature_name" jdbcType="VARCHAR" property="originFeatureName" />
    <result column="current_feature_name" jdbcType="VARCHAR" property="currentFeatureName" />
    <result column="origin_value" jdbcType="VARCHAR" property="originValue" />
    <result column="current_value" jdbcType="VARCHAR" property="currentValue" />
    <result column="created_by_employee_id" jdbcType="BIGINT" property="createdByEmployeeId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, feature_type_id, origin_feature_name, current_feature_name, origin_value, current_value, 
    created_by_employee_id, created_at
  </sql>
  <select id="selectMemberFeatureLogList" parameterType="MemberFeatureLog" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM member_feature_log
    <where>
      <if test="featureTypeId != null">
        and feature_type_id = #{featureTypeId}
      </if>
      <if test="originFeatureName != null and originFeatureName != ''">
        and origin_feature_name like concat('%', #{originFeatureName}, '%')
      </if>
      <if test="currentFeatureName != null and currentFeatureName != ''">
        and current_feature_name like concat('%', #{currentFeatureName}, '%')
      </if>
      <if test="originValue != null and originValue != ''">
        and origin_value like concat('%', #{originValue}, '%')
      </if>
      <if test="currentValue != null and currentValue != ''">
        and current_value like concat('%', #{currentValue}, '%')
      </if>
      <if test="createdByEmployeeId != null">
        and created_by_employee_id = #{createdByEmployeeId}
      </if>
      <if test="params != null and params.beginTime != null and params.beginTime != ''">
        and created_at &gt;= #{params.beginTime}
      </if>
      <if test="params != null and params.endTime != null and params.endTime != ''">
        and created_at &lt;= #{params.endTime}
      </if>
    </where>
    ORDER BY created_at DESC
  </select>
  <select id="selectMemberFeatureLogById" parameterType="Long" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM member_feature_log
    WHERE id = #{id}
  </select>
  <insert id="insertMemberFeatureLog" parameterType="MemberFeatureLog" useGeneratedKeys="true" keyProperty="id">
    insert into member_feature_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="featureTypeId != null">feature_type_id,</if>
      <if test="originFeatureName != null">origin_feature_name,</if>
      <if test="currentFeatureName != null">current_feature_name,</if>
      <if test="originValue != null">origin_value,</if>
      <if test="currentValue != null">current_value,</if>
      <if test="createdByEmployeeId != null">created_by_employee_id,</if>
      <if test="createdAt != null">created_at,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="featureTypeId != null">#{featureTypeId},</if>
      <if test="originFeatureName != null">#{originFeatureName},</if>
      <if test="currentFeatureName != null">#{currentFeatureName},</if>
      <if test="originValue != null">#{originValue},</if>
      <if test="currentValue != null">#{currentValue},</if>
      <if test="createdByEmployeeId != null">#{createdByEmployeeId},</if>
      <if test="createdAt != null">#{createdAt},</if>
    </trim>
  </insert>
  <update id="updateMemberFeatureLog" parameterType="MemberFeatureLog">
    update member_feature_log
    <trim prefix="SET" suffixOverrides=",">
      <if test="featureTypeId != null">feature_type_id = #{featureTypeId},</if>
      <if test="originFeatureName != null">origin_feature_name = #{originFeatureName},</if>
      <if test="currentFeatureName != null">current_feature_name = #{currentFeatureName},</if>
      <if test="originValue != null">origin_value = #{originValue},</if>
      <if test="currentValue != null">current_value = #{currentValue},</if>
      <if test="createdByEmployeeId != null">created_by_employee_id = #{createdByEmployeeId},</if>
      <if test="createdAt != null">created_at = #{createdAt},</if>
    </trim>
    where id = #{id}
  </update>
  <delete id="deleteMemberFeatureLogById" parameterType="Long">
    delete from member_feature_log where id = #{id}
  </delete>
  <delete id="deleteMemberFeatureLogByIds" parameterType="String">
    delete from member_feature_log where id in 
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>