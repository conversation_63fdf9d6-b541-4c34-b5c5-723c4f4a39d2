<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.MemberFeatureTypeMapper">

    <resultMap type="MemberFeatureType" id="MemberFeatureTypeResult">
        <result property="id"    column="id"    />
        <result property="rootStoreId"    column="root_store_id"    />
        <result property="name"    column="name"    />
        <result property="isDefault"    column="is_default"    />
        <result property="rootStoreName"    column="root_store_name"    />
    </resultMap>

    <sql id="selectMemberFeatureTypeVo">
        select mft.id, mft.root_store_id, mft.name, mft.is_default,
               s.name as root_store_name
        from member_feature_type mft
                 left join store s on mft.root_store_id = s.id
    </sql>


    <select id="selectMemberFeatureTypeList" parameterType="MemberFeatureType" resultMap="MemberFeatureTypeResult">
        <include refid="selectMemberFeatureTypeVo"/>
        <where>  
            <if test="rootStoreId != null "> and root_store_id = #{rootStoreId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="isDefault != null "> and is_default = #{isDefault}</if>
        </where>
    </select>

    <select id="selectMemberFeatureTypeById" parameterType="Long" resultMap="MemberFeatureTypeResult">
        <include refid="selectMemberFeatureTypeVo"/>
        where mft.id = #{id}
    </select>

    <select id="selectAllMemberFeatureTypes" resultMap="MemberFeatureTypeResult">
        <include refid="selectMemberFeatureTypeVo"/>
        order by mft.id
    </select>

    <insert id="insertMemberFeatureType" parameterType="MemberFeatureType" useGeneratedKeys="true" keyProperty="id">
        insert into member_feature_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id,</if>
            <if test="name != null">name,</if>
            <if test="isDefault != null">is_default,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">#{rootStoreId},</if>
            <if test="name != null">#{name},</if>
            <if test="isDefault != null">#{isDefault},</if>
         </trim>
    </insert>

    <update id="updateMemberFeatureType" parameterType="MemberFeatureType">
        update member_feature_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberFeatureTypeById" parameterType="Long">
        delete from member_feature_type where id = #{id}
    </delete>

    <delete id="deleteMemberFeatureTypeByIds" parameterType="String">
        delete from member_feature_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>