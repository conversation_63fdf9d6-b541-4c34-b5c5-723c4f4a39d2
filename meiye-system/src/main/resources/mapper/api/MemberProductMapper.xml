<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.MemberProductMapper">
    
    <resultMap type="MemberProduct" id="MemberProductResult">
        <result property="id"    column="id"    />
        <result property="memberId"    column="member_id"    />
        <result property="storeProductId"    column="store_product_id"    />
        <result property="cycleLength"    column="cycle_length"    />
        <result property="lastDo"    column="last_do"    />
        <result property="createdByEmployeeId"    column="created_by_employee_id"    />
        <result property="createdAt"    column="created_at"    />
        <result property="memberName"    column="member_name"    />
        <result property="createdByEmployeeName"    column="created_by_employee_name"    />
    </resultMap>

    <sql id="selectMemberProductVo">
        select mp.id, mp.member_id, mp.store_product_id, mp.cycle_length, mp.last_do, 
               mp.created_by_employee_id, mp.created_at,
               m.name as member_name,
               u.nick_name as created_by_employee_name
        from member_product mp
        left join member m on mp.member_id = m.id
        left join sys_user u on mp.created_by_employee_id = u.user_id
    </sql>

    <select id="selectMemberProductList" parameterType="MemberProduct" resultMap="MemberProductResult">
        <include refid="selectMemberProductVo"/>
        <where>  
            <if test="memberId != null "> and mp.member_id = #{memberId}</if>
            <if test="storeProductId != null "> and mp.store_product_id = #{storeProductId}</if>
            <if test="cycleLength != null  and cycleLength != ''"> and mp.cycle_length = #{cycleLength}</if>
            <if test="lastDo != null "> and mp.last_do = #{lastDo}</if>
            <if test="createdByEmployeeId != null "> and mp.created_by_employee_id = #{createdByEmployeeId}</if>
            <if test="createdAt != null "> and mp.created_at = #{createdAt}</if>
            <if test="memberName != null and memberName != ''"> and m.name like concat('%', #{memberName}, '%')</if>
        </where>
    </select>
    
    <select id="selectMemberProductById" parameterType="Long" resultMap="MemberProductResult">
        <include refid="selectMemberProductVo"/>
        where mp.id = #{id}
    </select>

    <insert id="insertMemberProduct" parameterType="MemberProduct" useGeneratedKeys="true" keyProperty="id">
        insert into member_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberId != null">member_id,</if>
            <if test="storeProductId != null">store_product_id,</if>
            <if test="cycleLength != null">cycle_length,</if>
            <if test="lastDo != null">last_do,</if>
            <if test="createdByEmployeeId != null">created_by_employee_id,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberId != null">#{memberId},</if>
            <if test="storeProductId != null">#{storeProductId},</if>
            <if test="cycleLength != null">#{cycleLength},</if>
            <if test="lastDo != null">#{lastDo},</if>
            <if test="createdByEmployeeId != null">#{createdByEmployeeId},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updateMemberProduct" parameterType="MemberProduct">
        update member_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="storeProductId != null">store_product_id = #{storeProductId},</if>
            <if test="cycleLength != null">cycle_length = #{cycleLength},</if>
            <if test="lastDo != null">last_do = #{lastDo},</if>
            <if test="createdByEmployeeId != null">created_by_employee_id = #{createdByEmployeeId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberProductById" parameterType="Long">
        delete from member_product where id = #{id}
    </delete>

    <delete id="deleteMemberProductByIds" parameterType="String">
        delete from member_product where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>