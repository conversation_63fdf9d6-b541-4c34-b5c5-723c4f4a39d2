<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ServiceScriptMapper">
    
    <resultMap type="ServiceScript" id="ServiceScriptResult">
        <result property="id"    column="id"    />
        <result property="rootStoreId"    column="root_store_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="scriptCategory"    column="script_category"    />
        <result property="status"    column="status"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdAt"    column="created_at"    />
        <result property="rootStoreName"    column="root_store_name"    />
        <result property="storeName"    column="store_name"    />
    </resultMap>

    <resultMap id="ServiceScriptServiceScriptItemResult" type="ServiceScript" extends="ServiceScriptResult">
        <collection property="serviceScriptItemList" ofType="ServiceScriptItem" column="id" select="selectServiceScriptItemList" />
    </resultMap>

    <resultMap type="ServiceScriptItem" id="ServiceScriptItemResult">
        <result property="id"    column="id"    />
        <result property="rootStoreId"    column="root_store_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="target"    column="target"    />
        <result property="requirement"    column="requirement"    />
        <result property="status"    column="status"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectServiceScriptVo">
        select ss.id, ss.root_store_id, ss.store_id, ss.title, ss.description, ss.script_category, ss.status, ss.created_by, ss.created_at,
               rs.name as root_store_name,
               s.name as store_name
        from service_script ss
        left join store rs on ss.root_store_id = rs.id
        left join store s on ss.store_id = s.id
    </sql>

    <select id="selectServiceScriptList" parameterType="ServiceScript" resultMap="ServiceScriptResult">
        <include refid="selectServiceScriptVo"/>
        <where>  
            <if test="rootStoreId != null "> and ss.root_store_id = #{rootStoreId}</if>
            <if test="storeId != null "> and ss.store_id = #{storeId}</if>
            <if test="title != null  and title != ''"> and ss.title = #{title}</if>
            <if test="description != null  and description != ''"> and ss.description = #{description}</if>
            <if test="scriptCategory != null  and scriptCategory != ''"> and ss.script_category = #{scriptCategory}</if>
            <if test="status != null  and status != ''"> and ss.status = #{status}</if>
            <if test="createdBy != null "> and ss.created_by = #{createdBy}</if>
            <if test="createdAt != null "> and ss.created_at = #{createdAt}</if>
            <!-- 添加根据当前用户店面ID过滤的逻辑：显示当前店面数据和空值数据 -->
            <if test="currentUserStoreId != null">
                and (ss.store_id = #{currentUserStoreId} or ss.store_id is null)
            </if>
        </where>
    </select>
    
    <select id="selectServiceScriptById" parameterType="Long" resultMap="ServiceScriptServiceScriptItemResult">
        <include refid="selectServiceScriptVo"/>
        where ss.id = #{id}
    </select>

    <select id="selectServiceScriptItemList" resultMap="ServiceScriptItemResult">
        select id, root_store_id, store_id, title, content, target, requirement, status, created_by, created_at
        from service_script_item
        where id = #{id}
    </select>

    <insert id="insertServiceScript" parameterType="ServiceScript" useGeneratedKeys="true" keyProperty="id">
        insert into service_script
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id,</if>
            <if test="storeId != null">store_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="description != null">description,</if>
            <if test="scriptCategory != null and scriptCategory != ''">script_category,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rootStoreId != null">#{rootStoreId},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="scriptCategory != null and scriptCategory != ''">#{scriptCategory},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <update id="updateServiceScript" parameterType="ServiceScript">
        update service_script
        <trim prefix="SET" suffixOverrides=",">
            <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="scriptCategory != null and scriptCategory != ''">script_category = #{scriptCategory},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteServiceScriptById" parameterType="Long">
        delete from service_script where id = #{id}
    </delete>

    <delete id="deleteServiceScriptByIds" parameterType="String">
        delete from service_script where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteServiceScriptItemByIds" parameterType="String">
        delete from service_script_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteServiceScriptItemById" parameterType="Long">
        delete from service_script_item where id = #{id}
    </delete>

    <insert id="batchServiceScriptItem">
        insert into service_script_item( id, root_store_id, store_id, title, content, target, requirement, status, created_by, created_at) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.rootStoreId}, #{item.storeId}, #{item.title}, #{item.content}, #{item.target}, #{item.requirement}, #{item.status}, #{item.createdBy}, #{item.createdAt})
        </foreach>
    </insert>
</mapper>