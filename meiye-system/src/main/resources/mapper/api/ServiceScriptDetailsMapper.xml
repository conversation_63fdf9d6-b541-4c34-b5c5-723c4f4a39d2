<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ServiceScriptDetailsMapper">
    
    <resultMap type="ServiceScriptDetails" id="ServiceScriptDetailsResult">
        <result property="serviceScriptId"    column="service_script_id"    />
        <result property="serviceScriptItemId"    column="service_script_item_id"    />
        <result property="sortOrder"    column="sort_order"    />
    </resultMap>

    <!-- ... existing code ... -->
    <sql id="selectServiceScriptDetailsVo">
        select service_script_id, service_script_item_id, sort_order from service_script_details
    </sql>

    <select id="selectServiceScriptDetailsList" parameterType="ServiceScriptDetails" resultMap="ServiceScriptDetailsResult">
        <include refid="selectServiceScriptDetailsVo"/>
        <where>  
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
        </where>
    </select>
    
    <select id="selectServiceScriptDetailsByServiceScriptId" parameterType="Long" resultMap="ServiceScriptDetailsResult">
        <include refid="selectServiceScriptDetailsVo"/>
        where service_script_id = #{serviceScriptId}
    </select>
    <!-- ... existing code ... -->
    <insert id="insertServiceScriptDetails" parameterType="ServiceScriptDetails">
        insert into service_script_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceScriptId != null">service_script_id,</if>
            <if test="serviceScriptItemId != null">service_script_item_id,</if>
            <if test="sortOrder != null">sort_order,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceScriptId != null">#{serviceScriptId},</if>
            <if test="serviceScriptItemId != null">#{serviceScriptItemId},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
         </trim>
    </insert>

    <update id="updateServiceScriptDetails" parameterType="ServiceScriptDetails">
        update service_script_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceScriptItemId != null">service_script_item_id = #{serviceScriptItemId},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
        </trim>
        where service_script_id = #{serviceScriptId}
    </update>

    <delete id="deleteServiceScriptDetailsByServiceScriptId" parameterType="Long">
        delete from service_script_details where service_script_id = #{serviceScriptId}
    </delete>

    <delete id="deleteServiceScriptDetailsByServiceScriptIds" parameterType="String">
        delete from service_script_details where service_script_id in 
        <foreach item="serviceScriptId" collection="array" open="(" separator="," close=")">
            #{serviceScriptId}
        </foreach>
    </delete>
</mapper>