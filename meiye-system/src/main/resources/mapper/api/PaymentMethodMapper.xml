<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.PaymentMethodMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.PaymentMethod">
    <!--@mbg.generated-->
    <!--@Table payment_method-->
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
     `name`, `code`
  </sql>
</mapper>