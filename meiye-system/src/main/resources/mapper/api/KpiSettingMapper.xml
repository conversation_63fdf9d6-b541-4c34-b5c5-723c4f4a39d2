<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.KpiSettingMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.KpiSetting">
    <!--@mbg.generated-->
    <!--@Table kpi_setting-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="root_store_id" jdbcType="BIGINT" property="rootStoreId" />
    <result column="target_store_id" jdbcType="BIGINT" property="targetStoreId" />
    <result column="target_position_id" jdbcType="BIGINT" property="targetPositionId" />
    <result column="target_employee_id" jdbcType="BIGINT" property="targetEmployeeId" />
    <result column="metric_code" jdbcType="VARCHAR" property="metricCode" />
    <result column="target_value" jdbcType="DECIMAL" property="targetValue" />
    <result column="cycle" jdbcType="CHAR" property="cycle" />
    <result column="ref_product_id" jdbcType="BIGINT" property="refProductId" />
    <result column="status" jdbcType="OTHER" property="status" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  
  <sql id="selectKpiSettingVo">
    select id, root_store_id, target_store_id, target_position_id, target_employee_id, metric_code, 
    target_value, `cycle`, ref_product_id, `status`, created_at from kpi_setting
  </sql>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, root_store_id, target_store_id, target_position_id, target_employee_id, metric_code, 
    target_value, `cycle`, ref_product_id, `status`, created_at
  </sql>

  <select id="selectKpiSettingList" parameterType="KpiSetting" resultMap="BaseResultMap">
    <include refid="selectKpiSettingVo"/>
    <where>
      <if test="rootStoreId != null "> and root_store_id = #{rootStoreId}</if>
      <if test="targetStoreId != null "> and target_store_id = #{targetStoreId}</if>
      <if test="targetPositionId != null "> and target_position_id = #{targetPositionId}</if>
      <if test="targetEmployeeId != null "> and target_employee_id = #{targetEmployeeId}</if>
      <if test="metricCode != null and metricCode != ''"> and metric_code = #{metricCode}</if>
      <if test="targetValue != null "> and target_value = #{targetValue}</if>
      <if test="cycle != null and cycle != ''"> and cycle = #{cycle}</if>
      <if test="refProductId != null "> and ref_product_id = #{refProductId}</if>
      <if test="status != null and status != ''"> and status = #{status}</if>
      <if test="createdAt != null "> and created_at = #{createdAt}</if>
    </where>
  </select>
  <select id="selectKpiSettingById" parameterType="Long" resultMap="BaseResultMap">
    <include refid="selectKpiSettingVo"/>
    where id = #{id}
  </select>
  <insert id="insertKpiSetting" parameterType="KpiSetting" useGeneratedKeys="true" keyProperty="id">
    insert into kpi_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rootStoreId != null">root_store_id,</if>
      <if test="targetStoreId != null">target_store_id,</if>
      <if test="targetPositionId != null">target_position_id,</if>
      <if test="targetEmployeeId != null">target_employee_id,</if>
      <if test="metricCode != null and metricCode != ''">metric_code,</if>
      <if test="targetValue != null">target_value,</if>
      <if test="cycle != null and cycle != ''">cycle,</if>
      <if test="refProductId != null">ref_product_id,</if>
      <if test="status != null and status != ''">status,</if>
      <if test="createdAt != null">created_at,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="rootStoreId != null">#{rootStoreId},</if>
      <if test="targetStoreId != null">#{targetStoreId},</if>
      <if test="targetPositionId != null">#{targetPositionId},</if>
      <if test="targetEmployeeId != null">#{targetEmployeeId},</if>
      <if test="metricCode != null and metricCode != ''">#{metricCode},</if>
      <if test="targetValue != null">#{targetValue},</if>
      <if test="cycle != null and cycle != ''">#{cycle},</if>
      <if test="refProductId != null">#{refProductId},</if>
      <if test="status != null and status != ''">#{status},</if>
      <if test="createdAt != null">#{createdAt},</if>
    </trim>
  </insert>
  <update id="updateKpiSetting" parameterType="KpiSetting">
    update kpi_setting
    <trim prefix="SET" suffixOverrides=",">
      <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
      <if test="targetStoreId != null">target_store_id = #{targetStoreId},</if>
      <if test="targetPositionId != null">target_position_id = #{targetPositionId},</if>
      <if test="targetEmployeeId != null">target_employee_id = #{targetEmployeeId},</if>
      <if test="metricCode != null and metricCode != ''">metric_code = #{metricCode},</if>
      <if test="targetValue != null">target_value = #{targetValue},</if>
      <if test="cycle != null and cycle != ''">cycle = #{cycle},</if>
      <if test="refProductId != null">ref_product_id = #{refProductId},</if>
      <if test="status != null and status != ''">status = #{status},</if>
      <if test="createdAt != null">created_at = #{createdAt},</if>
    </trim>
    where id = #{id}
  </update>
  <delete id="deleteKpiSettingById" parameterType="Long"> delete from kpi_setting where id = #{id} </delete>
  <delete id="deleteKpiSettingByIds" parameterType="String">
    delete from kpi_setting where id in
    <foreach item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
  </delete>
</mapper>