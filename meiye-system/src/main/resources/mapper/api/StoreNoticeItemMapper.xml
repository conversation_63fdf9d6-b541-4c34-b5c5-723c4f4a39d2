<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.StoreNoticeItemMapper">
    
    <resultMap type="StoreNoticeItem" id="StoreNoticeItemResult">
        <result property="id"    column="id"    />
        <result property="storeNoticeId"    column="store_notice_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="isRead"    column="is_read"    />
        <result property="readTime"    column="read_time"    />
    </resultMap>

    <sql id="selectStoreNoticeItemVo">
        select id, store_notice_id, store_id, employee_id, is_read, read_time from store_notice_item
    </sql>

    <select id="selectStoreNoticeItemList" parameterType="StoreNoticeItem" resultMap="StoreNoticeItemResult">
        <include refid="selectStoreNoticeItemVo"/>
        <where>  
            <if test="storeNoticeId != null "> and store_notice_id = #{storeNoticeId}</if>
            <if test="storeId != null "> and store_id = #{storeId}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="isRead != null "> and is_read = #{isRead}</if>
            <if test="readTime != null "> and read_time = #{readTime}</if>
        </where>
    </select>
    
    <select id="selectStoreNoticeItemById" parameterType="Long" resultMap="StoreNoticeItemResult">
        <include refid="selectStoreNoticeItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertStoreNoticeItem" parameterType="StoreNoticeItem">
        insert into store_notice_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="storeNoticeId != null">store_notice_id,</if>
            <if test="storeId != null">store_id,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="isRead != null">is_read,</if>
            <if test="readTime != null">read_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="storeNoticeId != null">#{storeNoticeId},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="isRead != null">#{isRead},</if>
            <if test="readTime != null">#{readTime},</if>
         </trim>
    </insert>

    <update id="updateStoreNoticeItem" parameterType="StoreNoticeItem">
        update store_notice_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeNoticeId != null">store_notice_id = #{storeNoticeId},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
            <if test="readTime != null">read_time = #{readTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStoreNoticeItemById" parameterType="Long">
        delete from store_notice_item where id = #{id}
    </delete>

    <delete id="deleteStoreNoticeItemByNId" parameterType="Long">
        delete from store_notice_item where store_notice_id = #{id}
    </delete>


    <delete id="deleteStoreNoticeItemByIds" parameterType="String">
        delete from store_notice_item where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>