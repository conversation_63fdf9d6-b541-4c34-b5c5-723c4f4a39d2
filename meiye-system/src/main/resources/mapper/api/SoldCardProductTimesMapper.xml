<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.SoldCardProductTimesMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.SoldCardProductTimes">
    <!--@mbg.generated-->
    <!--@Table sold_card_product_times-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sold_card_id" jdbcType="BIGINT" property="soldCardId" />
    <result column="sale_store_product_id" jdbcType="BIGINT" property="saleStoreProductId" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sold_card_id, sale_store_product_id, quantity
  </sql>
</mapper>