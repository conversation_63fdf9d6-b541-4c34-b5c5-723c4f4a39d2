<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="90px" size="small">

      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          placeholder="请输入名称"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item label="卡类型" prop="type">
        <el-select
          v-model="queryParams.type"
          clearable
          placeholder="请选择卡类型"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.card_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="创建时间" prop="createdAtRange">
        <el-date-picker
          v-model="queryParams.createdAtRange"
          clearable
          end-placeholder="结束日期"
          range-separator="至"
          start-placeholder="开始日期"
          type="daterange"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:product:add']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
          :disabled="!isAdmin"
        >新增
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="productList">
      <!-- 通用字段：所有卡都显示 -->
      <el-table-column align="center" label="名称" prop="name"/>
      <el-table-column align="center" label="卡类型" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.card_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="单价" prop="price"/>
      <el-table-column align="center" label="创建时间" prop="createdAt">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="销售状态" prop="status">
        <template slot-scope="scope">
          {{ scope.row.status === 'enabled' ? '启用' : (scope.row.status === 'disabled' ? '禁用' : '未知') }}
        </template>
      </el-table-column>

      <!-- 储值卡（balance）专属字段：在单元格内部控制显示 -->
      <el-table-column align="center" label="直充金额(元)" prop="realBalance">
        <template slot-scope="scope">
          <!-- 仅当卡类型为balance时显示 -->
          <span v-if="scope.row.type === 'balance'">
            {{ scope.row.realBalance.toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="赠送金额(元)" prop="giftBalance">
        <template slot-scope="scope">
          <span v-if="scope.row.type === 'balance'">
            {{ scope.row.giftBalance !== null ? scope.row.giftBalance.toFixed(2) : '0.00' }}
          </span>
        </template>
      </el-table-column>
      <!-- 优惠卡（discount）专属字段 -->
      <el-table-column align="center" label="折扣比例(%)" prop="discountRatio">
        <template slot-scope="scope">
          <span v-if="scope.row.type === 'discount'">
            <!-- 若接口返回字段为discount_ratio，需在映射时转换 -->
            {{ scope.row.discountRatio !== null ? scope.row.discountRatio : '0' }}%
          </span>
        </template>
      </el-table-column>

      <!-- 次卡/体验卡（times/experience）专属字段 -->
      <el-table-column align="center" label="有效期(天)" prop="validityDays">
        <template slot-scope="scope">
          <span v-if="scope.row.validityDays > 0">
            {{ scope.row.validityDays }}
          </span>
          <span v-else>永久</span>
        </template>
      </el-table-column>

<el-table-column align="center" label="销售状态" width="140">
  <template slot-scope="scope">
    <el-switch
      v-model="scope.row.status"
      :active-value="'enabled'" 
      :inactive-value="'disabled'"  
      active-text="启用"
      inactive-text="禁用"
      @change="handleStatusChange(scope.row)" 
      :disabled="false" 
    />
  </template>
</el-table-column>

      <!-- 操作列 -->
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:product:edit']"
            icon="el-icon-money"
            size="mini"
            type="text"
            @click="handleCommissionConfig(scope.row)"
          >提成配置
          </el-button>
          <el-button
            v-hasPermi="['system:product:edit']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            :disabled=" scope.row.status === 'enabled'"
          >修改
          </el-button>
          <el-button
            v-hasPermi="['system:product:remove']"
            :disabled="!isAdmin|| scope.row.status === 'enabled'"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNum"
      :total="total"
      @pagination="getList"
    />

    <!-- 主对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="80%">
      <el-form label-position="left" ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="会员卡类型" prop="type">
          <el-radio-group v-model="form.type" @change="handleRadioChange" :disabled="!isAdmin" placeholder="请选择会员卡类型">
            <el-radio
              v-for="dict in dict.type.card_type"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
<el-form-item label="分类" prop="categoryId">
<el-cascader
  v-model="categoryPath"
  :options="categoryOptions"
  :props="{
    expandTrigger: 'hover',
    value: 'id',
    label: 'name',
    checkStrictly: true,
    emitPath: true
  }"
  :disabled="!isAdmin"
  @change="handleCategoryChange"
/>
</el-form-item>
        <el-divider></el-divider>
        <h3>会员卡信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="名称" prop="name">
              <el-input
                class="center-input"
                v-model="form.name"
                placeholder="请输入名称"
                style="width: 200px;"
                :disabled="!isAdmin"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="form-row-item" label="单价" prop="price">
              <el-input-number
                v-model="form.price"
                placeholder="请输入单价"
                :min="0"
                style="text-align: left;"
                :precision="0"
                :controls="false">
              </el-input-number>
              (元)
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="form.type==='balance'">
          <el-col :span="12">
            <el-form-item label="直充金额 " prop="realBalance">
              <el-input-number
                v-model="form.realBalance"
                placeholder="请输入直充金额"
                :min="0"
                :precision="0"
                :disabled="!isAdmin"
                :controls="false">
              </el-input-number>
              (元)
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="赠送金额" prop="giftBalance">
              <el-input-number
                v-model="form.giftBalance"
                placeholder="请输入赠送金额"
                :min="0"
                :precision="0"
                :disabled="!isAdmin"
                :controls="false">
              </el-input-number>
              (元)
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12" v-if="form.type!='balance'">
            <el-form-item label="有效期" prop="validityDays">
              <el-input-number
                v-model="form.validityDays"
                placeholder="请输入有效期"
                :min="0"
                :precision="0"
                :disabled="!isAdmin"
                :controls="false">
              </el-input-number>
              (天)
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.type==='discount'">
            <el-form-item label="折扣率" prop="discountRatio">
              <el-input-number
                v-model="form.discountRatio"
                placeholder="请输入折扣率"
                :min="0"
                :max="100"
                :precision="0"
                :disabled="!isAdmin"
                :controls="false">
              </el-input-number>
              (%)
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div v-if="form.type==='times'||form.type==='experience'" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h3>优惠信息</h3>
          <!-- 添加总价显示 -->
          <div style="font-size: 14px; font-weight: bold;">
            总价:
            <span style="color: #f56c6c; font-size: 16px;">
              {{ totalAmount }}
            </span> 元
          </div>
        </div>
        <el-row :gutter="20" v-if="form.type==='times'||form.type==='experience' ">
          <el-col :span="12" >
          <!--      项目      -->
            <el-table :data="form.serviceList">
                <el-table-column align="center" label="项目" prop="name" min-width="120">
    <template slot-scope="scope">
      <el-select
        v-model="scope.row.selectedService"
        @change="handleServiceChange(scope.row, $event)"
        :filterable="true"
        placeholder="请选择项目"
        :disabled="!isAdmin"
      >
        <el-option
          v-for="item in SOptions"
          :key="item.id"
          :label="item.name"
          :disabled="isServiceDisabled(item.id, scope.$index)"
          :value="item.id">
        </el-option>
      </el-select>
    </template>
  </el-table-column>
              <el-table-column align="center" label="单价(元)" prop="equalPrice" width="100">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.equalPrice"
                    placeholder="请输入单价"
                    :min="0"
                    :precision="0"
                    style="width: 100%"
                    :controls="false">
                  </el-input-number>

                </template>
              </el-table-column>
              <el-table-column align="center" label="次数" prop="quantity" width="90">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.quantity"
                    placeholder="请输入次数"
                    :min="0"
                    :max="100"
                    style="width: 100%"
                    :precision="0"
                    :controls="false">
                  </el-input-number>

                   </template>
              </el-table-column>
              <el-table-column align="center" label="小计(元)" width="90">
                <template slot-scope="scope">
                  {{scope.row.equalPrice * scope.row.quantity}}
                </template>


              </el-table-column>
              <el-table-column label="操作" align="center" width="80">
                <template slot-scope="scope">
                  <div v-if="!scope.row.isSubtotal">
                    <el-popconfirm
                      title="确定要删除这条记录吗？"
                      @confirm="removeServiceItem(scope.$index)">
                      <el-button slot="reference" type="text" icon="el-icon-delete" :disabled="!isAdmin"></el-button>
                    </el-popconfirm>
                  </div>
                  <div v-else></div>
                </template>
              </el-table-column>
            </el-table>

            <div style="margin-top: 10px; text-align: center;">
              <el-button type="primary" icon="el-icon-plus" @click="addServiceItem" :disabled="!isAdmin">添加项目</el-button>

            </div>
          </el-col>
          <el-col :span="12">
            <!--      产品      -->
            <el-table :data="form.projectList">
              <el-table-column align="center" label="产品" prop="name" min-width="120">
                <template slot-scope="scope">
<el-select
  v-model="scope.row.selectedProduct"
  @change="handleProductChange(scope.row, $event)"
  :filterable="true"
  placeholder="请选择产品"
  :disabled="!isAdmin"
>
  <el-option
    v-for="item in productOptions"
    :key="item.id"
    :label="item.name"
          :disabled="isProductDisabled(item.id, scope.$index)"
    :value="item.id">
  </el-option>
</el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" label="单价(元)" prop="equalPrice" width="100">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.equalPrice"
                    placeholder="请输入单价"
                    :min="0"
                    :precision="0"
                    style="width: 100%"
                    :controls="false">
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column align="center" label="数量" prop="quantity" width="90">
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.quantity"
                    placeholder="请输入数量"
                    :min="0"
                    :precision="0"
                    style="width: 100%"
                    :controls="false">
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column align="center" label="小计(元)" prop="quantity" width="90">
                <template slot-scope="scope">
                    {{scope.row.equalPrice * scope.row.quantity }}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center"  width="80">
                <template slot-scope="scope">
                    <el-popconfirm
                      title="确定要删除这条记录吗？"
                      @confirm="removeProjectItem(scope.$index)"
                    >
                      <el-button slot="reference" type="text" icon="el-icon-delete" :disabled="!isAdmin"></el-button>
                    </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px; text-align: center;">
              <el-button type="primary" icon="el-icon-plus" @click="addProjectItem" :disabled="!isAdmin">添加产品</el-button>
            </div>
          </el-col>
        </el-row>

        <el-divider v-if="form.type==='times'||form.type==='experience'"></el-divider>
 </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 提成配置弹窗 -->
    <el-dialog :title="commissionTitle" :visible.sync="commissionOpen" width="680px" append-to-body>
      <el-form ref="commissionForm" :model="commissionForm" :rules="commissionRules">
        <div style="max-height: 500px; overflow-y: auto;">
          <el-table :data="commissionPositions" style="width: 100%" border>
            <el-table-column label="职位" prop="positionName" align="center" width="120">
              <template slot-scope="scope">
                {{ scope.row.positionName }}
              </template>
            </el-table-column>
            <el-table-column label="现金提成比例（%）" align="center">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'commissionPositions.' + scope.$index + '.cashCommissionRate'"
                  style="margin-bottom: 0;">
                  <el-input-number
                    v-model="scope.row.cashCommissionRate"
                    :min="0"
                    :max="100"
                    :precision="0"
                    controls-position="right"
                    size="small"
                    style="width: 120px; text-align: center;">
                  </el-input-number>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="余额提成比例（%）" align="center">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'commissionPositions.' + scope.$index + '.balanceCommissionRate'"
                  style="margin-bottom: 0;">
                  <!-- 对于储值卡类型，余额提成比例设为0且不可修改 -->
                  <div v-if="currentProduct && currentProduct.type === 'balance'">
                    <el-input-number
                      v-model="scope.row.balanceCommissionRate"
                      :min="0"
                      :max="100"
                      :precision="0"
                      controls-position="right"
                      size="small"
                      style="width: 120px; text-align: center;"
                      :disabled="true">
                    </el-input-number>
                    <div style="color: #999; font-size: 12px; margin-top: 3px;">不支持余额支付</div>
                  </div>
                  <!-- 对于其他类型，保持原有逻辑 -->
                  <el-input-number
                    v-else
                    v-model="scope.row.balanceCommissionRate"
                    :min="0"
                    :max="100"
                    :precision="0"
                    controls-position="right"
                    size="small"
                    style="width: 120px; text-align: center;">
                  </el-input-number>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitCommissionForm">确 定</el-button>
          <el-button @click="cancelCommissionForm">取 消</el-button>
        </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProductC,
  getProduct,
  delProduct,
  addCard,
  updateProduct,
  productOption
} from "@/api/productManage/product"
import { listPost } from "@/api/system/post";
import { getCommission, updateCommission, addCommission, listByProductId } from "@/api/productManage/commission";
import {categoryChildOption,categoryChildOptionAll} from "@/api/productManage/productCategory";
import {listCard,delCard} from "@/api/productManage/card";
import { getInfo } from "@/api/login";

export default {
  name: "Product",
  dicts: ['card_type'],
computed: {
  totalAmount() {
    const serviceTotal = parseFloat(this.serviceSubtotal) || 0;
    const projectTotal = parseFloat(this.projectSubtotal) || 0;
    return (serviceTotal + projectTotal).toFixed(2);
  },
  serviceSubtotal() {
    return this.form.serviceList.reduce((total, item) => {
      const price = parseFloat(item.equalPrice) || 0;
      const quantity = parseInt(item.quantity) || 0;
      return total + (price * quantity);
    }, 0).toFixed(2);
  },
  projectSubtotal() {
    return this.form.projectList.reduce((total, item) => {
      const price = parseFloat(item.equalPrice) || 0;
      const quantity = parseInt(item.quantity) || 0;
      return total + (price * quantity);
    }, 0).toFixed(2);
  }
},
  data() {
    return {
      originalCategoryId: null, // 新增：存储原始的 categoryId
      leafPathMap: {},
      rootStoreId: null, // 用于存储店铺ID
      selectedProductIds: [], // 用于跟踪已选择的产品ID
      selectedServiceIds: [], // 用于跟踪已选择的服务项目ID

      // 用户权限相关
      isAdmin: false,
      currentUserStoreId: null,
      categoryOptions:[],
      loading: true,
      showSearch: true,
      total: 0,

      productList: [],
      serviceList:[],
      projectList:[],
      title: "",
      open: false,
      // 提成配置弹窗相关
      commissionOpen: false,
      commissionTitle: "",
      isEdit: false,
      commissionForm: {
        productId: undefined,
        positions: []
      },
      commissionPositions: [],
      commissionRules: {
        positions: []
      },
      // 当前产品信息
      currentProduct: {},
      // 岗位列表
      postOptions: [],
      postLoading: false,
      queryParams: {

        pageNum: 1,
        pageSize: 10,
        categoryId:null,
        rootStoreId: null,
        categoryCode: null,
        categoryCodeArr: [],
        name: null,
        price: null,
        canUseBalance: null,
        status: null,
        createdAtRange: [], // 新增：创建时间范围搜索
        unit: undefined,  // 修复：初始化为 undefined
        isMain: null,       // 修复：初始化为数字 0
      },
      form: {
        id: null,
        type: "balance",
        categoryId: null,
        rootStoreId: null,
        categoryCode: null,
        categoryCodeArr: [],
        name: null,
        price: null,
        unit: '', // 新增：计价单位
        canUseBalance: null,
        status:"enabled" ,
        isMain: null, // 新增：是否主推
        createdAt: null,
        serviceList: [], // 包含id, name, equalPrice, quantity, selectedService
        projectList: []  // 包含id, name, equalPrice, quantity, selectedProduct
      },
      loadingOptions: false,
      /*服务*/
      SOptions:[],
      /*产品*/
      productOptions:[],
      categoryPath: [],   // 新增绑定
      rules: {
        categoryId: [
          {required: true, message: "分类不能为空", trigger: "change"}
        ],
        rootStoreId: [
          {required: true, message: "适用的主店面不能为空", trigger: "blur"}
        ],
        name: [
          {required: true, message: "名称不能为空", trigger: "blur"}
        ],
        price: [
          {required: true, message: "单价不能为空", trigger: "blur"}
        ],
        canUseBalance: [
          {required: true, message: "储值卡支付不能为空", trigger: "change"}
        ],
        categoryCode: [
          {required: true, message: "分类码不能为空", trigger: "change"}
        ],
        unit: [
          {required: true, message: "计价单位不能为空", trigger: "change"}
        ],
        isMain: [
          {required: true, message: "是否主推不能为空", trigger: "change"}
        ],
        realBalance:[
          {required: true, message: "直充金额不能为空", trigger: "blur"},

        ],
        giftBalance:[
          {required: true, message: "赠送金额不能为空", trigger: "blur"},

        ],
        discountRatio:[
          {required: true, message: "折扣率不能为空", trigger: "blur"},

        ],
        validityDays: [
          { required: true, message: "有效期不能为空", trigger: "blur" },
        ]
      }
    }
  },
    watch: {
    open(newVal) {
      if (!newVal) {
        // 对话框关闭时重置分类路径和编辑状态
        this.categoryPath = [];
        this.isEdit = false;
      }
    }
  },
created() {
  categoryChildOption().then(res => {
    let allCategories = res.data || [];
    // 过滤只保留card类型
    const filteredCategories = allCategories.filter(cat => cat.type === 'card');
    this.categoryOptions = filteredCategories;
    this.buildLeafPathMap(this.categoryOptions);
  });
    this.getCurrentUserInfo().then(() => {
    this.getList();
    this.getCategoryChildOptionAll();
    // 新增：非管理员自动设置店铺ID到表单
    if (!this.isAdmin && this.currentUserStoreId) {
      this.form.rootStoreId = this.currentUserStoreId;
    }
  }).catch(error => {
    console.error("用户信息获取错误:", error);
  });
},
  methods: {
handleStatusChange(row) {
  // 1. 仿照 submitForm 构造完整参数
  const updateData = {
    id: row.id, // 产品ID
    name: row.name || '', // 名称
    price: Number(row.price) || 0, // 单价（转数字避免类型错误）
    categoryId: row.categoryId || row.category_id || null, // 分类ID
    categoryCode: row.categoryCode || row.category_code || (row.categoryId ? String(row.categoryId) : ''), // 分类码
    rootStoreId: row.rootStoreId || row.root_store_id || this.currentUserStoreId, // 店铺ID
    unit: row.unit || 'zhang', // 计价单位
    status: row.status, // 切换后的状态（'enabled'/'disabled'）
    isMain: row.isMain === 1 || row.isMain === '1' || row.isMain === true ? 1 : 0, // 是否主推
        // 消费卡专属字段（与正常修改对齐，从行数据提取）
    type: row.type || 'balance', // 卡类型
    validityDays: row.type !== 'balance' ? Number(row.validityDays || row.validity_days) || 0 : 0, // 有效期（非储值卡才传）
    realBalance: row.type === 'balance' ? Number(row.realBalance || row.real_balance) || 0 : 0, // 直充金额（储值卡才传）
    giftBalance: row.type === 'balance' ? Number(row.giftBalance || row.gift_balance) || 0 : 0, // 赠送金额（储值卡才传）
    discountRatio: row.type === 'discount' ? Number(row.discountRatio || row.discount_ratio) || 0 : 0, // 折扣率（优惠卡才传）

    // 优惠列表（与正常修改对齐，从行数据提取，无则传空数组）
    serviceList: this.formatStatusChangeList(row.serviceList || row.service_list || [], 'service'), // 项目列表
    projectList: this.formatStatusChangeList(row.projectList || row.project_list || [], 'product') // 产品列表
  };

  console.log("状态修改传参（与正常修改一致）:", updateData);

  // 2. 调用与正常修改相同的 updateProduct 接口
  updateProduct(updateData)
    .then(() => {
      this.$modal.msgSuccess(`已${row.status === 'enabled' ? '启用' : '禁用'}`);
    })
    .catch(error => {
      // 接口失败时回滚状态
      row.status = row.status === 'enabled' ? 'disabled' : 'enabled';
      this.$modal.msgError(`状态更新失败：${error.message || '网络异常'}`);
      console.error('销售状态更新失败（传完整值）:', error);
    });
},

// 新增：辅助方法 - 格式化状态修改时的列表数据（与正常修改的 formatListData 逻辑对齐）
formatStatusChangeList(rawList, type) {
  if (!Array.isArray(rawList) || rawList.length === 0) {
    return []; // 无数据时传空数组，避免后端报错
  }

  // 仿照 formatListData 处理列表结构，确保与正常修改的列表格式一致
  return rawList.map(item => ({
    id: item.id || item.subProductId || '', // 子项ID（兼容不同字段名）
    equalPrice: Number(item.equalPrice || item.equal_price) || 0, // 单价
    quantity: Number(item.quantity) || 1, // 数量
    selectedService: type === 'service' ? (item.id || item.subProductId) : undefined, // 项目标识（仅项目列表传）
    selectedProduct: type === 'product' ? (item.id || item.subProductId) : undefined // 产品标识（仅产品列表传）
  }));
},

loadServiceOptions() {
  return new Promise((resolve, reject) => {
    const query = { type: 'serve' };
    productOption(query).then(response => {
      this.SOptions = response.data || [];
      resolve();
    }).catch(error => {
      console.error("加载服务选项失败:", error);
      reject(error);
    });
  });
},

    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.cancel();
          done();
        })
        .catch(_ => {});
    },

buildLeafPathMap(tree) {
  const map = {};
  const dfs = (node, path = []) => {
    const currentPath = [...path, node.id];
    map[node.id] = currentPath;
    if (node.children) {
      node.children.forEach(child => dfs(child, currentPath));
    }
  };
  tree.forEach(root => dfs(root));
  this.leafPathMap = map;
},

// 回显时：根据叶子 id 拿到完整路径
    getPathByLeafId(leafId) {
      // 直接从映射表中获取路径
      const path = this.leafPathMap[leafId] || [];
      console.log(`根据叶子ID ${leafId} 获取到的路径:`, path);
      return path;
    },

  // 处理查询表单的分类选择（主要用于查询条件的categoryCode）
handleCategoryChange(path) {
  console.log("分类路径变化:", path);

  if (path && path.length) {
    const leafId = path[path.length - 1]; // 获取叶子节点ID
    const selectedCategory = this.findCategoryById(leafId);

    console.log("选中的分类:", selectedCategory);

    if (selectedCategory) {
      // 确保使用分类的 code，而不是 id
      this.form.categoryCode = selectedCategory.code || selectedCategory.id;
      console.log("设置的 categoryCode:", this.form.categoryCode);
    } else {
      // 如果找不到分类，使用 id 作为 fallback
      this.form.categoryCode = leafId;
      console.warn("找不到分类对象，使用 ID 作为 code:", leafId);
    }

    this.form.categoryId = leafId;
  } else {
    this.form.categoryId = null;
    this.form.categoryCode = null;
  }
},

    findCategoryById(id) {
      const findInTree = (categories) => {
        for (const category of categories) {
          if (category.id == id) return category;
          if (category.children && category.children.length > 0) {
            const found = findInTree(category.children);
            if (found) return found;
          }
        }
        return null;
      };

      return findInTree(this.categoryOptions);
    },

async getCurrentUserInfo() {
  try {
    const response = await getInfo(); // 调用登录接口获取用户信息
    let userData = response.data || response;

    // 重点：根据接口实际返回的字段名提取店铺ID（可能是storeId而非rootStoreId）
    // 例如：如果接口返回的是user.storeId，则修改为：
        if (userData.user && userData.user.storeId) {
          this.currentUserStoreId = userData.user.storeId; // 修正字段名
        } else if (userData.storeId) {
          this.currentUserStoreId = userData.storeId; // 修正字段名
        } else {
          this.$modal.msgError("未找到店铺信息，请联系管理员");
          return; // 获取不到店铺ID时终止执行
        }

    // 判断是否为管理员（保持不变）
    this.isAdmin = (userData.roles && userData.roles.includes('admin')) ||
                  (userData.user && userData.user.roles?.some(r => r.roleKey === 'admin'));

    // 非管理员自动设置表单的rootStoreId
    if (!this.isAdmin && this.currentUserStoreId) {
      this.form.rootStoreId = this.currentUserStoreId;
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    this.$modal.msgError("获取用户信息失败，无法继续操作");
  }
},

getCategoryChildOptionAll() {
  return new Promise((resolve, reject) => {
    categoryChildOptionAll().then(res => {
      let allCategories = res.data || [];
      
      // 只保留类型为"card"的分类
      const filteredCategories = allCategories.filter(category => {
        return category.type === 'card';
      });

      // 确保每个分类对象都有必要的字段
      filteredCategories.forEach(category => {
        if (!category.code) {
          category.code = category.id;
        }
        if (!category.children) {
          category.children = [];
        }
        
        // 递归过滤子分类，只保留类型为"card"的
        if (category.children && category.children.length > 0) {
          category.children = this.filterChildCategories(category.children);
        }
      });

      this.categoryOptions = filteredCategories;
      this.buildLeafPathMap(this.categoryOptions);
      resolve(this.categoryOptions);
    }).catch(error => {
      console.error("获取分类选项失败:", error);
      reject(error);
    });
  });
},

// 添加递归过滤子分类的方法
filterChildCategories(children) {
  return children
    .filter(child => child.type === 'card')
    .map(child => {
      if (child.children && child.children.length > 0) {
        child.children = this.filterChildCategories(child.children);
      }
      return child;
    });
},

    /*切换会员卡类型*/
    handleRadioChange(val){
        this.form.validityDays = null;
        this.form.realBalance = null;
        this.form.giftBalance = null;
        this.form.discountRatio = null;
        this.serviceList = [];
        this.projectList = [];
        this.selectedServiceIds = [];
        this.selectedProductIds = [];
    },
    // 加载岗位列表
    loadPostList(storeId) {
      return new Promise((resolve, reject) => {
        this.postLoading = true;
        listPost({ storeId: storeId }).then(response => {
          this.postOptions = response.rows || response.data || [];
          this.postLoading = false;
          resolve();
        }).catch(error => {
          console.error("获取岗位列表失败：", error);
          this.$message.error("岗位列表加载失败，请稍后重试");
          this.postLoading = false;
          reject(error);
        });
      });
    },

    // 处理提成配置
    handleCommissionConfig(row) {
      this.commissionForm.productId = row.id;
      this.commissionTitle = `提成配置 - ${row.name}`;
      this.commissionPositions = [];

      // 保存产品信息，用于获取departmentId
      this.currentProduct = row;

      // 根据当前登录账号的店铺ID加载岗位列表（参考service.vue的实现方式）
      this.loadPostList(this.currentUserStoreId).then(() => {
        // 使用实际的岗位列表初始化职位数据
        this.commissionPositions = this.postOptions.map(post => ({
          positionId: post.postId,
          positionName: post.postName,
          cashCommissionRate: 0,
          balanceCommissionRate: 0
        }));

        // 如果是储值卡类型，将余额提成比例设为0
        if (this.currentProduct && this.currentProduct.type === 'balance') {
          this.commissionPositions.forEach(position => {
            position.balanceCommissionRate = 0;
          });
        }

        // 获取提成配置数据
        this.getCommissionConfig(row.id);

        // 数据加载完成后再显示弹窗（与product.vue保持一致）
        this.commissionOpen = true;
      }).catch((error) => {
        console.error('加载岗位列表失败:', error);
        this.$message.error("岗位信息加载失败，请重试");
      });
    },

    // 获取提成配置数据
    getCommissionConfig(productId) {
      // 使用listByProductId方法通过storeProductId查询提成配置
      listByProductId({storeProductId: productId}).then(response => {
        let existingData = response.data;
        existingData = Array.isArray(existingData) ? existingData :
          (existingData && typeof existingData === 'object' ? [existingData] : []);

        // 将已有配置数据更新到commissionPositions中
        this.commissionPositions.forEach(position => {
          const config = existingData.find(item =>
            item.positionId === position.positionId ||
            item.position_id === position.positionId
          );

          if (config) {
            position.cashCommissionRate = config.cashCommissionRate || config.cash_commission_rate || 0;
            // 如果是储值卡类型，强制将余额提成比例设为0
            if (this.currentProduct && this.currentProduct.type === 'balance') {
              position.balanceCommissionRate = 0;
            } else {
              position.balanceCommissionRate = config.balanceCommissionRate || config.balance_commission_rate || 0;
            }
            // 保存配置的ID，用于更新操作
            position.id = config.id;
          } else {
            // 如果是储值卡类型，确保所有岗位的余额提成比例都为0
            if (this.currentProduct && this.currentProduct.type === 'balance') {
              position.balanceCommissionRate = 0;
            }
          }
        });
      }).catch(error => {
        console.error("获取提成配置失败:", error);
        this.$message.warning("获取提成配置失败，使用默认值");
        
        // 如果是储值卡类型，确保所有岗位的余额提成比例都为0
        if (this.currentProduct && this.currentProduct.type === 'balance') {
          this.commissionPositions.forEach(position => {
            position.balanceCommissionRate = 0;
          });
        }
      });
    },

    // 提交提成配置表单
    submitCommissionForm() {
      this.$refs.commissionForm.validate(valid => {
        if (valid) {
          // 先通过storeProductId获取现有的提成配置数据
          listByProductId({storeProductId: this.commissionForm.productId}).then(response => {
            const existingCommissionData = response.data || [];

            // 对每个岗位单独发送请求
            const requests = this.commissionPositions.map(pos => {
              // 查找是否已存在店面销售物ID和岗位ID都一样的数据
              const existingConfig = existingCommissionData.find(item =>
                item.storeProductId == this.commissionForm.productId &&
                item.positionId == pos.positionId
              );

              const submitData = {
                storeProductId: this.commissionForm.productId,
                positionId: pos.positionId,
                cashCommissionRate: pos.cashCommissionRate,
                balanceCommissionRate: pos.balanceCommissionRate,
                // 使用当前登录账号的店铺ID作为departmentId（参考service.vue的实现方式）
                departmentId: this.currentUserStoreId
              };

              // 如果是储值卡类型，强制将余额提成比例设为0
              if (this.currentProduct && this.currentProduct.type === 'balance') {
                submitData.balanceCommissionRate = 0;
              }

              // 如果存在配置且有有效ID，则更新，否则新增
              if (existingConfig && existingConfig.id) {
                submitData.id = existingConfig.id;
                return updateCommission(submitData);
              } else {
                return addCommission(submitData);
              }
            });

            // 批量执行所有请求
            Promise.all(requests)
              .then(() => {
                this.$modal.msgSuccess("提成配置保存成功");
                this.commissionOpen = false;
              })
              .catch(error => {
                console.error("保存失败:", error);
                this.$modal.msgError("提成配置保存失败");
              });
          }).catch(error => {
            console.error("通过storeProductId获取现有提成配置失败：", error);
            this.$modal.msgError("获取现有提成配置失败");
          });
        }
      });
    },

    // 取消提成配置表单
    cancelCommissionForm() {
      this.commissionOpen = false;
      this.resetCommissionForm();
    },

    // 重置提成配置表单
    resetCommissionForm() {
      this.commissionForm = {
        productId: undefined,
        // 7. 重置时保持数组类型
        commissionPositions: []
      };
    },
 isServiceDisabled(serviceId, currentIndex) {
    return this.form.serviceList.some((item, index) =>
      index !== currentIndex && item.selectedService === serviceId
    );
  },

  // 检查产品是否已被选择（排除当前行）
  isProductDisabled(productId, currentIndex) {
    return this.form.projectList.some((item, index) =>
      index !== currentIndex && item.selectedProduct === productId
    );
  },
 handleServiceChange(row, value) {
    // 更新已选择的服务ID列表
    this.selectedServiceIds = this.form.serviceList
      .filter(item => item.selectedService)
      .map(item => item.selectedService);

    // 根据选中的项目ID找到对应的项目信息
    const selectedService = this.SOptions.find(item => item.id === value);
    if (selectedService) {
      row.name = selectedService.name;
      row.equalPrice = selectedService.price || 0;
    }
  },

  handleProductChange(row, value) {
    // 更新已选择的产品ID列表
    this.selectedProductIds = this.form.projectList
      .filter(item => item.selectedProduct)
      .map(item => item.selectedProduct);

    // 根据选中的产品ID找到对应的产品信息
    const selectedProduct = this.productOptions.find(item => item.id === value);
    if (selectedProduct) {
      row.name = selectedProduct.name;
      row.equalPrice = selectedProduct.price || 0;
    }
  },
addServiceItem() {
  // 添加空的数据项到 serviceList
  const newItem = {
    id: null,
    equalPrice: 0,
    quantity: 1
  };
  this.form.serviceList.push(newItem);

  console.log("添加服务项目后的列表:", this.serviceList);

  // 更新已选择的服务ID列表
  this.selectedServiceIds = this.serviceList
    .filter(item => item.id)
    .map(item => item.id);
},
removeServiceItem(index) {
  const removedItem = this.form.serviceList.splice(index, 1)[0];
  if (removedItem && removedItem.id) {
    this.selectedServiceIds = this.selectedServiceIds.filter(id => id !== removedItem.selectedService);
  }
},
addProjectItem() {
  // 添加空的数据项到 projectList
  const newItem = {
    id: null,
    equalPrice: 0,
    quantity: 1
  };
  this.form.projectList.push(newItem);

  console.log("添加产品项目后的列表:", this.projectList);

  // 更新已选择的产品ID列表
  this.selectedProductIds = this.projectList
    .filter(item => item.id)
    .map(item => item.id);
},
removeProjectItem(index) {
  const removedItem = this.form.projectList.splice(index, 1)[0];
  if (removedItem && removedItem.id) {
    this.selectedProductIds = this.selectedProductIds.filter(id => id !== removedItem.selectedProduct);
  }
},


    // 获取会员卡列表
async getList() {
  this.loading = true;
  let startDate = null;
  let endDate = null;

  // 1. 处理日期参数（和之前一致）
  if (this.queryParams.createdAtRange && this.queryParams.createdAtRange.length === 2) {
    startDate = this.queryParams.createdAtRange[0] + ' 00:00:00';
    endDate = this.queryParams.createdAtRange[1] + ' 23:59:59';
  }
  const productParams = {
    ...this.queryParams,
    startDate: startDate,
    endDate: endDate,
  };
  delete productParams.createdAtRange;

  try {
    // 2. 第一步：先查“日期范围内的产品”（拿到符合条件的productId列表）
    const productRes = await listProductC(productParams);
    const validProducts = productRes.rows || [];
    // 提取符合条件的productId（核心：用这些ID过滤消费卡）
    const validProductIds = validProducts.map(p => p.id).filter(id => id); // 去重+过滤空值

    // 3. 第二步：用“validProductIds”查对应的消费卡（关键：后端需支持按productId列表过滤）
    const cardParams = {
      ...this.queryParams,
      productIds: validProductIds, // 新增参数：传给后端，筛选消费卡
    };
    delete cardParams.createdAtRange; // 消费卡无日期，删除无用参数
    const cardRes = await listCard(cardParams);
    const cards = cardRes.rows || [];

    // 4. 第三步：构建产品映射（用于补全消费卡的产品信息，和之前一致）
    const productMap = {};
    validProducts.forEach(p => {
      productMap[p.id] = {
        name: p.name || '',
        price: p.price || 0,
        status: p.status || '',
        createdAt: p.createdAt || p.created_at || '',
        categoryId: p.categoryId || p.category_id || null,
        categoryCode: p.categoryCode || p.category_code || null,
      };
    });

    // 5. 第四步：合并消费卡和产品信息（此时只有符合日期的消费卡）
    const cardsWithDetail = cards.map(card => {
      const detail = productMap[card.productId] || {};
      return {
        ...card,
        ...detail,
        id: card.id || '',
        productId: card.product_id || card.productId || '',
        type: card.type || '',
        validityDays: card.validity_days || card.validityDays || 0,
        discountRatio: card.discount_ratio || card.discountRatio || 0,
        realBalance: card.real_balance || card.realBalance || 0,
        giftBalance: card.gift_balance || card.giftBalance || 0,
      };
    });

    // 6. 最终赋值（此时productList只包含日期范围内产品对应的消费卡）
    this.productList = cardsWithDetail;
    this.total = cardRes.total || 0;

  } catch (error) {
    console.error('获取数据失败：', error);
    this.productList = [];
    this.total = 0;
  } finally {
    this.loading = false;
  }
},

    // 取消
    cancel() {
      this.open = false;
      this.reset();
      this.categoryPath = [];
      this.isEdit = false;
    },

    // 重置表单
reset() {
  this.form = {
    categoryId: null,
    categoryCode: null,
    id: null,
    type: 'balance',
    name: null,
    price: 0,
    rootStoreId: this.currentUserStoreId || null,
    categoryCodeArr: [],
    canUseBalance: null,
    unit: "zhang",
    isMain: null,
    realBalance: 0,
    giftBalance: 0,
    discountRatio: 0,
    validityDays: 0,
    serviceList: [],
    projectList: []
  };
  this.originalCategoryId = null; // 重置原始 categoryId
  this.resetForm("form");
},

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 重置查询
resetQuery() {
  this.resetForm("queryForm");
  this.queryParams.categoryCodeArr = [];
  this.queryParams.createdAtRange = []; // 确保清空时间范围
  this.queryParams.isMain = null;
  // 重置时保持非管理员的店铺ID
  if (!this.isAdmin && this.currentUserStoreId) {
    this.queryParams.rootStoreId = this.currentUserStoreId;
  }
  this.handleQuery();
},

    // 新增
handleAdd() {
  this.reset();
  this.categoryPath = [];
  this.originalCategoryId = null;
  this.form.categoryId = null;

  // 使用Promise.all同时加载产品和服务选项
  Promise.all([
    this.loadProductOptions(),
    this.loadServiceOptions()
  ]).then(() => {
    this.open = true;
    this.title = "添加会员卡";
  }).catch(error => {
    console.error("加载选项失败:", error);
    this.$modal.msgError("加载选项失败");
  });
},

    async handleUpdate(row) {
  this.reset();
  const id = row?.productId;
  if (!id) return this.$modal.msgError('未找到有效的数据ID，无法修改');

  try {
    // 显示加载状态
    this.loadingOptions = true;

    // 确保分类选项已加载
    await this.getCategoryChildOptionAll();

    // 同时加载产品选项和服务选项
    await Promise.all([
      this.loadProductOptions(),
      this.loadServiceOptions()
    ]);

    const res = await getProduct(id);
    const cardData = res.data || {};
    const cc = cardData.consumptionCard || {};

    // 保存原始的 categoryId
    this.originalCategoryId = cardData.categoryId;

    // 获取分类完整路径
    this.categoryPath = this.getPathByLeafId(cardData.categoryId);

    // 查找分类对象获取编码
    const categoryObj = this.findCategoryById(cardData.categoryId);

    this.form = {
      id: cardData.id,
      name: cardData.name || '',
      price: Number(cardData.price) || 0,
      categoryId: cardData.categoryId,
      categoryCode: cardData.categoryCode || (categoryObj ? categoryObj.code : cardData.categoryId),
      rootStoreId: cardData.rootStoreId || this.currentUserStoreId,
      status: cardData.status || 'enabled',
      type: cc.type || 'balance',
      realBalance: Number(cc.realBalance) || 0,
      giftBalance: Number(cc.giftBalance) || 0,
      discountRatio: Number(cc.discountRatio) || 0,
      validityDays: Number(cc.validityDays) || 0,
      serviceList: this.formatListData(cardData.serviceList || [], 'service'),
      projectList: this.formatListData(cardData.projectList || [], 'product')
    };

    this.open = true;
    this.title = '修改会员卡';
    this.isEdit = true;
  } catch (error) {
    console.error("加载数据失败:", error);
    this.$modal.msgError("加载数据失败");
  } finally {
    // 隐藏加载状态
    this.loadingOptions = false;
  }
},

// 添加加载产品选项的方法
loadProductOptions() {
  return new Promise((resolve, reject) => {
    const query = { type: 'type' };
    productOption(query).then(response => {
      this.productOptions = response.data || [];
      resolve();
    }).catch(error => {
      console.error("加载产品选项失败:", error);
      reject(error);
    });
  });
},

formatListData(rawList, type) {
  if (!Array.isArray(rawList)) return [];

  const options = type === 'service' ? this.SOptions : this.productOptions;

  return rawList.map(item => {
    const selectedItem = options.find(opt => opt.id === item.subProductId || opt.id === item.id);
    return {
      id: item.id, // ✅ 保留原始 id
      name: selectedItem ? selectedItem.name : item.name || '', // 如果找不到，使用原始名称
      equalPrice: Number(item.equalPrice || item.equal_price) || 0,
      quantity: Number(item.quantity) || 1,
      selectedService: type === 'service' ? (item.subProductId || item.id) : undefined,
      selectedProduct: type === 'product' ? (item.subProductId || item.id) : undefined
    };
  });
},

submitForm() {
  this.$refs.form.validate(valid => {
    if (!valid) {
      console.error("表单验证未通过");
      return;
    }

    const payload = {
      id: this.form.id,
      name: this.form.name,
      price: Number(this.form.price),
      categoryId: this.originalCategoryId || this.form.categoryId, // 优先使用原始ID
      categoryCode: this.form.categoryCode, // 使用表单中的 categoryCode
      rootStoreId: this.form.rootStoreId,
      unit: this.form.unit,
      status: this.form.status,
      isMain: this.form.isMain,

      // 消费卡专属字段
      type: this.form.type,
      validityDays: this.form.type !== 'balance' ? Number(this.form.validityDays) : 0,
      realBalance: this.form.type === 'balance' ? Number(this.form.realBalance) : 0,
      giftBalance: this.form.type === 'balance' ? Number(this.form.giftBalance) : 0,
      discountRatio: this.form.type === 'discount' ? Number(this.form.discountRatio) : 0,

      // 优惠列表
      serviceList: this.form.serviceList.map(i => ({
        id: i.selectedService || i.id,
        equalPrice: Number(i.equalPrice),
        quantity: Number(i.quantity)
      })),
      projectList: this.form.projectList.map(i => ({
        id: i.selectedProduct || i.id,
        equalPrice: Number(i.equalPrice),
        quantity: Number(i.quantity)
      }))
    };

    console.log("提交数据:", payload);

    const api = this.form.id ? updateProduct : addCard;
    api(payload)
      .then(() => {
        this.$modal.msgSuccess("操作成功");
        this.open = false;
        this.getList();
      })
      .catch(err => {
        console.error("操作失败:", err);
        this.$modal.msgError("操作失败: " + err.message);
      });
  });
},

handleDelete(row) {
  const productId = row.productId; // 获取 productId
  this.$modal.confirm('是否确认删除编号为"' + productId + '"的数据项？').then(function () {
    return delCard(productId); // 传递 productId
  }).then(() => {
    this.getList();
    this.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
},

  }
};
</script>

<style scoped>
</style>
