<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="单元标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入单元标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单元目标" prop="target">
        <el-input
          v-model="queryParams.target"
          placeholder="请输入单元目标"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="enabled"></el-option>
          <el-option label="禁用" value="disabled"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['service:item:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['service:item:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['service:item:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['service:item:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="itemList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主店面" align="center" prop="rootStoreName">
        <template slot-scope="scope">
          <span>{{ scope.row.rootStoreName === null || scope.row.rootStoreName === '' ? '暂无该店铺' : scope.row.rootStoreName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应用店面" align="center" prop="storeName">
        <template slot-scope="scope">
          <span>{{ scope.row.storeName === null || scope.row.storeName === '' ? '暂无该店铺' : scope.row.storeName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="单元标题" align="center" prop="title" />
      <el-table-column label="单元内容" align="center" prop="content" />
      <el-table-column label="单元目标" align="center" prop="target" />
      <el-table-column label="达标要求" align="center" prop="requirement" />
      <!-- 修改状态显示为开关 -->
      <el-table-column label="状态" align="center" prop="status"width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="enabled"
            inactive-value="disabled"
            active-text=""
            inactive-text=""
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createdBy">
        <template slot-scope="scope">
          <span>{{ getUserNickName(scope.row.createdBy) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['service:item:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['service:item:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改标准话术单元对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="应用店面" prop="storeName">
          <!-- 启用状态下禁用编辑 -->
          <el-select v-model="form.storeName" placeholder="请选择应用店面" :disabled="form.status === 'enabled'" style="width: 100%">
            <el-option
              v-for="store in filteredStoreOptions"
              :key="store.id"
              :label="store.name"
              :value="store.name">
            </el-option>
            <el-option v-if="!form.storeName" label="暂无该店铺" value=""></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单元标题" prop="title">
          <!-- 启用状态下禁用编辑 -->
          <el-input v-model="form.title" placeholder="请输入单元标题" :disabled="form.status === 'enabled'" style="width: 100%" />
        </el-form-item>



        <el-form-item label="单元内容">
          <!-- 启用状态下禁用编辑 -->
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="4"
            placeholder="请输入单元内容"
            maxlength="500"
            show-word-limit
            :disabled="form.status === 'enabled'"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="单元目标" prop="target">
          <!-- 启用状态下禁用编辑 -->
          <el-input v-model="form.target" placeholder="请输入单元目标" :disabled="form.status === 'enabled'" style="width: 100%" />
        </el-form-item>
        <el-form-item label="达标要求" prop="requirement">
          <!-- 启用状态下禁用编辑 -->
          <el-input v-model="form.requirement" type="textarea" placeholder="请输入内容" :disabled="form.status === 'enabled'" style="width: 100%" />
        </el-form-item>
        <!-- 修改状态为开关 -->
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            active-value="enabled"
            inactive-value="disabled"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listItem, getItem, delItem, addItem, updateItem, getStoreRootId, getStoreIdByName } from "@/api/service/item"
import { listStore } from "@/api/store/store"
import { getInfo } from "@/api/login"

export default {
  name: "Item",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 标准话术单元表格数据
      itemList: [],
      // 店面选项
      storeOptions: [],
      // 过滤后的店面选项（主店面及其分店）
      filteredStoreOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否为管理员
      isAdmin: false,
      // 当前用户店铺ID
      currentUserStoreId: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        storeId: null,
        title: null,
        content: null,
        target: null,
        requirement: null,
        status: null,
        createdBy: null,
        createdAt: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "单元标题不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "单元内容不能为空", trigger: "blur" }
        ],
        target: [
          { required: true, message: "单元目标不能为空", trigger: "blur" }
        ],
        storeName: [
          { required: true, message: "应用店面不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    }
  },
  created() {
    this.getCurrentUser()
    this.getStoreList()
    this.getList()
  },
  methods: {
    /** 获取当前用户信息 */
    async getCurrentUser() {
      try {
        const response = await getInfo()
        let userData = response.data || response

        // 提取用户信息中的店铺ID
        if (userData.user && userData.user.rootStoreId !== undefined && userData.user.rootStoreId !== null) {
          this.currentUserStoreId = userData.user.rootStoreId
        } else if (userData.user && userData.user.storeId !== undefined && userData.user.storeId !== null) {
          this.currentUserStoreId = userData.user.storeId
        } else if (userData.rootStoreId !== undefined && userData.rootStoreId !== null) {
          this.currentUserStoreId = userData.rootStoreId
        } else if (userData.storeId !== undefined && userData.storeId !== null) {
          this.currentUserStoreId = userData.storeId
        }

        // 判断是否为管理员
        this.isAdmin = (userData.roles && userData.roles.some(role => role.roleKey === 'admin')) ||
                      (userData.user && userData.user.roles &&
                       userData.user.roles.some(role => role.roleKey === 'admin'))

        // 获取列表数据
        this.getList()
      } catch (error) {
        console.error("获取用户信息失败:", error)
        this.$modal.msgError("获取用户信息失败")
        // 即使获取用户信息失败，也要加载列表
        this.getList()
      }
    },
    /** 查询标准话术单元列表 */
    getList() {
      this.loading = true
      // 移除主店面筛选参数
      const queryWithoutRootStore = { ...this.queryParams }
      delete queryWithoutRootStore.rootStoreId
      listItem(queryWithoutRootStore).then(response => {
        this.itemList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 获取店面列表
    getStoreList() {
      listStore({}).then(response => {
        this.storeOptions = response.rows
        // 根据主店面筛选店面选项
        if (this.currentUserStoreId) {
          this.filteredStoreOptions = this.storeOptions.filter(store =>
            store.id === this.currentUserStoreId || store.parentId === this.currentUserStoreId)
        } else {
          this.filteredStoreOptions = this.storeOptions
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        // 不再自动填充主店面ID
        // rootStoreId: this.currentUserStoreId,
        rootStoreName: null,
        storeId: null,
        storeName: null,
        title: null,
        content: null,
        target: null,
        requirement: null,
        status: null,
        createdBy: null,
        createdAt: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    async handleAdd() {
      this.reset()
      this.form.createdAt = this.parseTime(new Date(), '{y}-{m}-{d}') // 自动设置系统时间
      // 设置默认状态为禁用
      this.form.status = 'disabled'
      // 自动填充创建人
      this.form.createdBy = this.$store.getters.id // 使用用户ID而不是用户名

      // 根据主店面筛选店面选项
      if (this.currentUserStoreId) {
        this.filteredStoreOptions = this.storeOptions.filter(store =>
          store.id === this.currentUserStoreId || store.parentId === this.currentUserStoreId)
      } else {
        this.filteredStoreOptions = this.storeOptions
      }

      this.open = true
      this.title = "添加标准话术单元"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // 启用状态下不能编辑
      if (row.status === 'enabled') {
        this.$message.warning('启用状态的话术单元不可修改')
        return
      }
      this.reset()
      const id = row.id || this.ids
      getItem(id).then(response => {
        this.form = response.data
        // 设置店面名称
        const store = this.storeOptions.find(s => s.id === response.data.storeId)
        if (store) {
          this.form.storeName = store.name
        }

        // 根据主店面筛选店面选项
        if (this.currentUserStoreId) {
          this.filteredStoreOptions = this.storeOptions.filter(store =>
            store.id === this.currentUserStoreId || store.parentId === this.currentUserStoreId)
        } else {
          this.filteredStoreOptions = this.storeOptions
        }

        this.open = true
        this.title = "修改标准话术单元"
      })
    },
    /** 提交按钮 */
    async submitForm() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          // 根据店面名称获取店面ID
          if (this.form.storeName) {
            try {
              const storeResponse = await getStoreIdByName(this.form.storeName)
              if (storeResponse.code === 200) {
                this.form.storeId = storeResponse.data
              }
            } catch (error) {
              console.error("获取店面ID失败:", error)
            }
          }

          if (this.form.id != null) {
            updateItem(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            // 确保在新增时也自动填充创建人和创建时间
            if (!this.form.createdBy) {
              this.form.createdBy = this.$store.getters.id // 使用用户ID而不是用户名
            }
            if (!this.form.createdAt) {
              this.form.createdAt = this.parseTime(new Date(), '{y}-{m}-{d}')
            }

            addItem(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除标准话术单元编号为"' + ids + '"的数据项？').then(function() {
        return delItem(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('api/item/export', {
        ...this.queryParams
      }, `item_${new Date().getTime()}.xlsx`)
    },

    handleStatusChange(row) {
      let text = row.status === "enabled" ? "启用" : "禁用"
      this.$modal.confirm('确认要"' + text + '""' + row.title + '"话术单元吗？').then(() => {

        return updateItem(row)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
      }).catch(() => {
        // 用户点击取消，需要手动恢复状态
        row.status = row.status === "enabled" ? "disabled" : "enabled"
      })
    },
    getUserNickName(userId) {
      // 如果是当前用户，则显示当前用户的昵称
      if (userId && userId == this.$store.getters.id) {
        return this.$store.getters.nickName;
      }
      // 对于其他用户，暂时显示用户ID，实际项目中应通过API获取用户信息
      return userId || '';
    }
  }
}
</script>
