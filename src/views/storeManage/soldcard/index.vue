<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="注册时间" prop="createdAt">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          align="right">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="disabled">
        <el-select v-model="queryParams.disabled" placeholder="请选择状态" clearable>
          <el-option label="有效" :value="false"></el-option>
          <el-option label="无效" :value="true"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="会员" prop="memberId">
        <el-select v-model="queryParams.memberId" placeholder="请选择会员" clearable filterable>
          <el-option
            v-for="member in memberOptions"
            :key="member.id"
            :label="member.name"
            :value="member.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="会员卡名称" prop="storeProductId" width="120px">
        <el-select v-model="queryParams.storeProductId" placeholder="请选择会员卡" clearable filterable>
          <el-option
            v-for="product in filteredProductOptions"
            :key="product.id"
            :label="product.name"
            :value="product.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="直充金额" prop="realBalance">
        <el-input
          v-model.number="queryParams.minRealBalance"
          placeholder="最小金额"
          clearable
          style="width: 120px;"
          type="number"
        />
        <span style="padding: 0 5px;">-</span>
        <el-input
          v-model.number="queryParams.maxRealBalance"
          placeholder="最大金额"
          clearable
          style="width: 120px;"
          type="number"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
      </el-col>
      <el-col :span="1.5">

      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['storeManage:soldcard:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 添加基于卡类型的标签页 -->
    <el-tabs v-model="activeTab" type="card" @tab-click="handleTabChange">
      <el-tab-pane label="储值卡" name="balance">
        <el-table v-loading="loading" :data="balanceSoldcardList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="卡片唯一编码" align="center" prop="cardSn" />
          <el-table-column label="会员卡名称" align="center" prop="storeProductId">
            <template slot-scope="scope">
              {{ getProductNameById(scope.row.storeProductId) }}
            </template>
          </el-table-column>
          <el-table-column label="会员卡类型" align="center" prop="cardType">
            <template slot-scope="scope">
              {{ getCardTypeById(scope.row.storeProductId) }}
            </template>
          </el-table-column>
          <el-table-column label="所属会员姓名" align="center" prop="memberId">
            <template slot-scope="scope">
              {{ getMemberNameById(scope.row.memberId) }}
            </template>
          </el-table-column>

          <el-table-column label="直充金额" align="center" prop="realBalance" />
          <el-table-column label="赠送金额" align="center" prop="giftBalance" />
          <el-table-column label="过期日期" align="center" prop="expireDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.expireDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="disabled">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.disabled"
                :active-value="false"
                :inactive-value="true"
                @change="handleStatusChange(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="注册会员信息的员工" align="center">
            <template slot-scope="scope">
              {{ getEmployeeNameById(scope.row.createdByEmployeeId) }}
            </template>
          </el-table-column>
          <el-table-column label="开卡时间" align="center" prop="createdAt" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="比例折扣" align="center" prop="discountRatio" />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>

      <el-tab-pane label="优惠卡" name="discount">
        <el-table v-loading="loading" :data="discountSoldcardList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="卡片唯一编码" align="center" prop="cardSn" />
          <el-table-column label="会员卡名称" align="center" prop="storeProductId">
            <template slot-scope="scope">
              {{ getProductNameById(scope.row.storeProductId) }}
            </template>
          </el-table-column>
          <el-table-column label="会员卡类型" align="center" prop="cardType">
            <template slot-scope="scope">
              {{ getCardTypeById(scope.row.storeProductId) }}
            </template>
          </el-table-column>
          <el-table-column label="所属会员姓名" align="center" prop="memberId">
            <template slot-scope="scope">
              {{ getMemberNameById(scope.row.memberId) }}
            </template>
          </el-table-column>

          <el-table-column label="当前直充余额" align="center" prop="realBalance" />
          <el-table-column label="当前赠送余额" align="center" prop="giftBalance" />
          <el-table-column label="消费时直充与赠送余额扣除比例" align="center" prop="realGiftRatio" />
          <el-table-column label="过期日期" align="center" prop="expireDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.expireDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="disabled">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.disabled"
                :active-value="false"
                :inactive-value="true"
                @change="handleStatusChange(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="注册会员信息的员工" align="center">
            <template slot-scope="scope">
              {{ getEmployeeNameById(scope.row.createdByEmployeeId) }}
            </template>
          </el-table-column>
          <el-table-column label="注册时间" align="center" prop="createdAt" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="比例折扣" align="center" prop="discountRatio" />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>

      <el-tab-pane label="次卡" name="times">
        <el-table v-loading="loading" :data="timesSoldcardList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="卡片唯一编码" align="center" prop="cardSn" />
          <el-table-column label="会员卡名称" align="center" prop="storeProductId">
            <template slot-scope="scope">
              {{ getProductNameById(scope.row.storeProductId) }}
            </template>
          </el-table-column>
          <el-table-column label="会员卡类型" align="center" prop="cardType">
            <template slot-scope="scope">
              {{ getCardTypeById(scope.row.storeProductId) }}
            </template>
          </el-table-column>
          <el-table-column label="所属会员姓名" align="center" prop="memberId">
            <template slot-scope="scope">
              {{ getMemberNameById(scope.row.memberId) }}
            </template>
          </el-table-column>

          <el-table-column label="当前直充余额" align="center" prop="realBalance" />
          <el-table-column label="当前赠送余额" align="center" prop="giftBalance" />
          <el-table-column label="消费时直充与赠送余额扣除比例" align="center" prop="realGiftRatio" />
          <el-table-column label="过期日期" align="center" prop="expireDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.expireDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="disabled">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.disabled"
                :active-value="false"
                :inactive-value="true"
                @change="handleStatusChange(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="注册会员信息的员工" align="center">
            <template slot-scope="scope">
              {{ getEmployeeNameById(scope.row.createdByEmployeeId) }}
            </template>
          </el-table-column>
          <el-table-column label="注册时间" align="center" prop="createdAt" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="比例折扣" align="center" prop="discountRatio" />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>

      <el-tab-pane label="体验卡" name="experience">
        <el-table v-loading="loading" :data="experienceSoldcardList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="卡片唯一编码" align="center" prop="cardSn" />
          <el-table-column label="会员卡名称" align="center" prop="storeProductId">
            <template slot-scope="scope">
              {{ getProductNameById(scope.row.storeProductId) }}
            </template>
          </el-table-column>
          <el-table-column label="会员卡类型" align="center" prop="cardType">
            <template slot-scope="scope">
              {{ getCardTypeById(scope.row.storeProductId) }}
            </template>
          </el-table-column>
          <el-table-column label="所属会员姓名" align="center" prop="memberId">
            <template slot-scope="scope">
              {{ getMemberNameById(scope.row.memberId) }}
            </template>
          </el-table-column>

          <el-table-column label="当前直充余额" align="center" prop="realBalance" />
          <el-table-column label="当前赠送余额" align="center" prop="giftBalance" />
          <el-table-column label="消费时直充与赠送余额扣除比例" align="center" prop="realGiftRatio" />
          <el-table-column label="过期日期" align="center" prop="expireDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.expireDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="disabled">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.disabled"
                :active-value="false"
                :inactive-value="true"
                @change="handleStatusChange(scope.row)">
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column label="注册会员信息的员工" align="center">
            <template slot-scope="scope">
              {{ getEmployeeNameById(scope.row.createdByEmployeeId) }}
            </template>
          </el-table-column>
          <el-table-column label="注册时间" align="center" prop="createdAt" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="比例折扣" align="center" prop="discountRatio" />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 添加或修改已售消费卡对话框 -->

  </div>
</template>

<script>
import { listSoldcard, getSoldcard, addSoldcard, updateSoldcard, delSoldcard } from "@/api/store/soldcard";
import { listAllMember } from "@/api/store/member";
import { listUser } from "@/api/system/user";
import { listProductC, getProduct } from "@/api/productManage/product";
import { listCardByProductIds } from "@/api/productManage/card";

export default {
  name: "Soldcard",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 已售消费卡表格数据
      soldcardList: [],
      // 按卡类型分组的已售消费卡表格数据
      balanceSoldcardList: [],
      discountSoldcardList: [],
      timesSoldcardList: [],
      experienceSoldcardList: [],
      // 当前激活的标签页
      activeTab: "balance",
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 日期范围选项
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        disabled: null,
        beginCreatedAt: null,
        endCreatedAt: null,
        cardType: null,
        storeProductId: null,
        productName: null, // 添加会员卡名称查询参数
        minRealBalance: null,
        maxRealBalance: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        storeProductId: [
          { required: true, message: "卡片的店面会员卡ID不能为空", trigger: "blur" }
        ],
        memberId: [
          { required: true, message: "所属会员姓名不能为空", trigger: "blur" }
        ],
        cardSn: [
          { required: true, message: "卡片唯一编码不能为空", trigger: "blur" }
        ],
        realBalance: [
          { required: true, message: "当前直充余额不能为空", trigger: "blur" }
        ],
        giftBalance: [
          { required: true, message: "当前赠送余额不能为空", trigger: "blur" }
        ],
        realGiftRatio: [
          { required: true, message: "消费时直充与赠送余额扣除比例不能为空", trigger: "blur" }
        ],
        disabled: [
          { required: true, message: "状态: ", trigger: "change" }
        ],
        createdByEmployeeId: [
          { required: true, message: "注册会员信息的员工ID不能为空", trigger: "blur" }
        ],

        discountRatio: [
          { required: true, message: "比例折扣不能为空", trigger: "blur" }
        ]
      },
      memberOptions: [], // 新增: 存储会员选项数据
      employeeOptions: [], // 新增: 存储员工选项数据
      productOptions: [] // 新增: 存储产品选项数据
    }
  },
  created() {
    // 初始化时加载储值卡数据
    this.getList()
    // 新增: 初始化会员选项数据
    this.getMemberOptions()
    // 新增: 初始化员工选项数据
    this.getEmployeeOptions()
    // 新增: 初始化产品选项数据
    this.getProductOptions()
  },
  methods: {
    /** 查询已售消费卡列表 */
    getList() {
      this.loading = true;
      // 设置当前标签页对应的卡类型查询参数
      this.queryParams.cardType = this.activeTab;

      listSoldcard(this.queryParams).then(response => {
        this.soldcardList = response.rows;
        this.total = response.total;
        this.loading = false;

        // 根据卡类型分组数据
        this.processSoldcardData();

        // 获取缺失的产品信息
        this.fetchMissingProductInfo();
        // 获取消费卡类型信息
        this.fetchConsumptionCardTypes();
      }).catch(() => {
        this.loading = false;
      });
    },

    // 处理已售消费卡数据，按卡类型分组
    processSoldcardData() {
      // 根据当前标签页设置对应的数据
      switch (this.activeTab) {
        case 'balance':
          this.balanceSoldcardList = this.soldcardList;
          break;
        case 'discount':
          this.discountSoldcardList = this.soldcardList;
          break;
        case 'times':
          this.timesSoldcardList = this.soldcardList;
          break;
        case 'experience':
          this.experienceSoldcardList = this.soldcardList;
          break;
        default:
          this.balanceSoldcardList = this.soldcardList;
      }
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        storeProductId: null,
        memberId: null,
        cardSn: null,
        realBalance: null,
        giftBalance: null,
        realGiftRatio: null,
        expireDate: null,
        disabled: false,
        createdByEmployeeId: null,
        createdAt: null,
        discountRatio: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.beginCreatedAt = this.dateRange[0];
      this.queryParams.endCreatedAt = this.dateRange[1];
      // 不再设置cardType参数，因为通过标签页切换控制
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.beginCreatedAt = null;
      this.queryParams.endCreatedAt = null;
      this.queryParams.storeProductId = null;
      this.queryParams.productName = null; // 清除会员卡名称查询条件
      this.queryParams.minRealBalance = null;
      this.queryParams.maxRealBalance = null;
      // 不再重置cardType参数
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },

    // 状态修改
    handleStatusChange(row) {
      let text = row.disabled ? "停用" : "启用"
      this.$modal.confirm('确认要"' + text + '""' + this.getMemberNameById(row.memberId) + '"的消费卡吗？').then(function() {
        // 用户点击确认，状态已由switch组件自动更新
        // 如果需要调用后端API更新状态，可以在这里添加
        return updateSoldcard(row)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
      }).catch(function() {
        // 用户点击取消，需要手动恢复状态
        row.disabled = !row.disabled
      })
    },


    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 创建一个新对象用于提交，将disabled转换为布尔值
          const submitForm = { ...this.form };

        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除已售消费卡编号为"' + ids + '"的数据项？').then(function() {
        return delSoldcard(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('storeManage/soldcard/export', {
        ...this.queryParams,
        cardType: this.activeTab // 导出时添加当前标签页的卡类型
      }, `soldcard_${new Date().getTime()}.xlsx`)
    },
    /** 获取会员选项数据 */
    getMemberOptions() {
      this.loading = true
      // 调用接口获取会员列表
      listAllMember().then(response => {
        this.memberOptions = response.data.map(item => ({
          id: item.id,
          name: item.name // 根据实际返回数据结构调整
        }))
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 获取会员名字 */
    getMemberNameById(memberId) {
      const member = this.memberOptions.find(item => item.id === memberId)
      return member ? member.name : '未知会员'
    },
    /** 获取员工选项数据 */
    getEmployeeOptions() {
      // 调用接口获取员工列表
      listUser({}).then(response => {
        if (response && response.rows) {
          this.employeeOptions = response.rows.map(item => ({
            id: item.userId,
            name: item.userName // 使用员工姓名
          }));
        } else {
          console.error('员工列表数据格式错误');
        }
      }).catch(error => {
        console.error('获取员工列表失败', error);
      });
    },
    /** 获取员工名字 */
    getEmployeeNameById(employeeId) {
      const employee = this.employeeOptions.find(item => item.id === employeeId)
      return employee ? employee.name : '未知员工'
    },
    /** 获取产品选项数据 */
    getProductOptions() {
      // 只获取会员卡数据
      listProductC({}).then(response => {
        const cardData = response.rows || [];
        // 设置会员卡数据
        this.productOptions = cardData.map(item => ({
          id: item.id,
          name: item.name,
          type: item.type // 添加卡类型信息
        }));
      }).catch(error => {
        console.error('获取会员卡列表失败', error);
      });
    },

    /** 批量获取缺失的产品信息 */
    async fetchMissingProductInfo() {
      // 收集所有在soldcardList中但不在productOptions中的产品ID
      const missingProductIds = this.soldcardList
        .map(item => item.storeProductId)
        .filter(storeProductId =>
          storeProductId && !this.productOptions.some(option => option.id === storeProductId)
        );

      // 去重
      const uniqueProductIds = [...new Set(missingProductIds)];

      // 如果没有缺失的产品信息，则直接返回
      if (uniqueProductIds.length === 0) {
        return;
      }

      try {
        // 批量获取产品信息，这里我们逐个获取，因为没有批量接口
        // 在实际项目中，应该提供一个批量获取的接口来优化性能
        const promises = uniqueProductIds.map(id => getProduct(id));
        const results = await Promise.all(promises);

        // 将获取到的产品信息添加到productOptions中
        results.forEach((response, index) => {
          const product = response.data;
          if (product && !this.productOptions.some(option => option.id === product.id)) {
            this.productOptions.push({
              id: product.id,
              name: product.name,
              type: product.type
            });
          }
        });
      } catch (error) {
        console.error('批量获取产品信息失败', error);
      }
    },

    /** 批量获取消费卡类型信息 */
    async fetchConsumptionCardTypes() {
      // 收集所有产品ID
      const productIds = this.soldcardList
        .map(item => item.storeProductId)
        .filter(storeProductId => storeProductId);

      // 去重
      const uniqueProductIds = [...new Set(productIds)];

      // 如果没有产品ID，则直接返回
      if (uniqueProductIds.length === 0) {
        return;
      }

      try {
        // 批量获取消费卡信息
        const response = await listCardByProductIds(uniqueProductIds);
        const consumptionCards = response.rows || response.data || [];

        // 将获取到的消费卡类型信息添加到productOptions中
        consumptionCards.forEach(card => {
          // 查找是否已存在该产品
          const existingProductIndex = this.productOptions.findIndex(option => option.id === card.productId);

          if (existingProductIndex !== -1) {
            // 如果已存在，更新type字段
            this.productOptions[existingProductIndex].type = card.type;
          } else {
            // 如果不存在，添加新的产品信息（仅包含ID和type）
            this.productOptions.push({
              id: card.productId,
              name: '', // 名称将在需要时通过getProduct获取
              type: card.type
            });
          }
        });
      } catch (error) {
        console.error('批量获取消费卡类型信息失败', error);
      }
    },

    /** 通过productId获取产品详细信息（包括type字段） */
    async getProductDetailById(productId) {
      // 先从缓存中查找
      let product = this.productOptions.find(item => item.id === productId);

      // 如果缓存中没有，则通过API获取
      if (!product) {
        try {
          const response = await getProduct(productId);
          product = response.data;

          // 将获取到的产品信息添加到缓存中
          this.productOptions.push({
            id: product.id,
            name: product.name,
            type: product.type
          });
        } catch (error) {
          console.error('获取产品详细信息失败', error);
          return null;
        }
      }

      return product;
    },

    /** 获取产品名称 */
    async getProductTypeName(productId) {
      const product = await this.getProductDetailById(productId);
      if (!product) return '未知类型';

      // 根据type字段返回对应的中文名称
      const typeMap = {
        'balance': '储值卡',
        'discount': '优惠卡',
        'times': '次卡',
        'experience': '体验卡'
      };

      return typeMap[product.type] || product.type || '未知类型';
    },

    /** 获取产品名称 */
    getProductNameById(productId) {
      const product = this.productOptions.find(item => item.id === productId);
      return product ? product.name : '未知产品';
    },

    /** 获取卡类型 */
    getCardTypeById(productId) {
      const product = this.productOptions.find(item => item.id === productId);

      if (!product) return '未知类型';

      // 根据type字段返回对应的中文名称
      const typeMap = {
        'balance': '储值卡',
        'discount': '优惠卡',
        'times': '次卡',
        'experience': '体验卡'
      };

      return typeMap[product.type] || product.type || '未知类型';
    },

    // 标签页切换处理
    handleTabChange(tab) {
      this.activeTab = tab.name;
      this.queryParams.pageNum = 1;
      // 设置卡类型查询参数
      this.queryParams.cardType = tab.name;
      this.getList();
    }
  }
}
</script>
