<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="head-container">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入会员姓名" clearable style="width: 200px" @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable style="width: 200px" @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="会员类型" prop="memberLevelId">
              <el-select v-model="queryParams.memberLevelId" placeholder="请选择会员等级" clearable style="width: 200px">
                <el-option v-for="item in memberLevelOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="注册时间" prop="createdAt">
              <el-date-picker
                v-model="createdAtRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 260px"
                @change="handleDateRangeChange">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="head-container">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['storeManage:memberLevel:add']"
          >新增</el-button>

          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['storeManage:memberLevel:remove']"
          >删除</el-button>
        </div>
        <el-table
          v-loading="loading"
          :data="memberList"
          @selection-change="handleSelectionChange"
          height="600"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column type="selection" width="50" align="center" fixed="left" />
          <el-table-column label="姓名" align="center" prop="name" width="100" />
          <el-table-column label="手机号" align="center" prop="phone" width="120" />
          <el-table-column label="性别" align="center" prop="gender" width="80">
            <template slot-scope="scope">
              <span>{{ genderLabel(scope.row.gender) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="会员等级" align="center" prop="memberLevelName" width="120" />
          <el-table-column label="活跃" align="center" prop="isActive" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.isActive ? 'success' : 'info'">{{ scope.row.isActive ? '是' : '否' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="注册员工" align="center" prop="createdByEmployeeName" width="120"/>
          <el-table-column label="注册时间" align="center" prop="createdAt" width="150">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="主店面" align="center" prop="rootStoreName">
            <template slot-scope="scope">
              <span>{{ scope.row.rootStoreName || '暂无该店铺' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="注册店面" align="center" prop="storeName">
            <template slot-scope="scope">
              <span>{{ scope.row.storeName || '暂无该店铺' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="直充余额" align="center" prop="realBalance" width="100" />
          <el-table-column label="赠送余额" align="center" prop="giftBalance" width="100" />
          <el-table-column label="现金消费总额" align="center" prop="totalCash" width="120" />
          <el-table-column label="最近一次消费日期" align="center" prop="lastConsumption" width="150">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.lastConsumption) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="250" fixed="right">
            <template slot-scope="scope">
              <div style="white-space: nowrap;">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleEdit(scope.row)"
                  v-hasPermi="['storeManage:member:edit']"
                >编辑</el-button>

                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['storeManage:member:remove']"
                >删除</el-button>

                <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)">
                  <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="viewProjects" icon="el-icon-view">查看项目</el-dropdown-item>
                    <el-dropdown-item command="viewFeatures" icon="el-icon-view">查看特性</el-dropdown-item>
                    <el-dropdown-item command="viewHistoryProjects" icon="el-icon-time">历史推荐项目</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
    <!-- 添加或编辑会员对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入姓名" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入手机号" maxlength="20" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="form.gender" placeholder="请选择性别">
                <el-option label="男" value="male" />
                <el-option label="女" value="female" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会员等级" prop="memberLevelId">
              <el-select
                v-model="form.memberLevelId"
                placeholder="请选择会员等级"
                clearable
                @change="handleMemberLevelChange">
                <el-option
                  v-for="item in memberLevelOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">

          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="登录密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入登录密码" type="password" maxlength="20" show-password />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册店面" prop="storeName">
              <el-select
                v-model="form.storeName"
                placeholder="请选择注册店面"
                filterable
                clearable
                style="width: 100%">
                <el-option
                  v-for="store in filteredStoreOptions"
                  :key="store.id"
                  :label="store.name"
                  :value="store.name">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>


        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 会员特性对话框 -->
    <el-dialog
      title="会员特性"
      :visible.sync="featureDialogVisible"
      width="800px"
      append-to-body
      :before-close="closeFeatureDialog"
      class="feature-dialog"
    >
      <div v-if="selectedMember.id" class="dialog-header">
        <span class="member-info">会员：{{ selectedMember.name }} (ID: {{ selectedMember.id }})</span>
      </div>

      <!-- 特性列表工具栏 -->
      <div class="feature-toolbar">
        <el-button type="primary" size="mini" icon="el-icon-plus"
                   @click="handleAddFeature">新增特性</el-button>
        <el-input
          v-model="featureSearch"
          placeholder="搜索特性"
          clearable
          size="mini"
          style="width: 200px; margin-left: 10px;"
          @keyup.enter.native="filterFeatures"
        >
          <el-button slot="append" icon="el-icon-search" @click="filterFeatures"></el-button>
        </el-input>
      </div>

      <!-- 使用el-scrollbar包裹表格部分 -->
      <el-scrollbar style="height: 350px; margin-bottom: 10px;">
        <!-- 特性列表 -->
        <el-table
          v-loading="featureLoading"
          :data="featureList"
          style="width: 100%; margin-top: 15px;"
        >
          <el-table-column label="会员姓名" align="center" prop="memberName" />
          <el-table-column label="特性类型" align="center" prop="featureTypeName" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.featureTypeName || '未知特性类型' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="特性值" align="center" prop="value" />
          <el-table-column label="录入人" align="center" prop="inputByName" />
          <el-table-column label="录入时间" align="center" prop="inputTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.inputTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEditFeature(scope.row)">编辑</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteFeature(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>

      <!-- 特性分页 -->
      <pagination
        v-show="featureTotal > 0"
        :total="featureTotal"
        :page.sync="featureQuery.pageNum"
        :limit.sync="featureQuery.pageSize"
        @pagination="getFeatureList"
        layout="total, sizes, prev, pager, next, jumper"
        small
      />
    </el-dialog>

    <!-- 新增：添加或编辑会员特性对话框 -->
    <el-dialog
      :title="featureForm.id ? '编辑会员特性' : '新增会员特性'"
      :visible.sync="featureFormVisible"
      width="450px"
      append-to-body
    >
      <el-form ref="featureForm" :model="featureForm" :rules="featureRules" label-width="100px">
        <el-form-item label="特性类型" prop="memberFeatureId">
          <el-select v-model="featureForm.memberFeatureId" placeholder="请选择特性类型" filterable style="width: 100%">
            <el-option
              v-for="featureType in featureTypeOptions"
              :key="featureType.id"
              :label="featureType.name"
              :value="featureType.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="特性值" prop="value">
          <el-input v-model="featureForm.value" placeholder="请输入特性值" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFeatureForm">确 定</el-button>
        <el-button @click="cancelFeatureForm">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 新增：推荐项目对话框 -->
    <el-dialog
      title="推荐项目"
      :visible.sync="projectDialogVisible"
      width="800px"
      append-to-body
      :before-close="closeProjectDialog"
      class="project-dialog"
    >
      <div v-if="selectedMember.id" class="dialog-header">
        <span class="member-info">会员：{{ selectedMember.name }} (ID: {{ selectedMember.id }})</span>
      </div>

      <!-- 项目列表工具栏 -->
      <div class="project-toolbar">
        <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAddProject">新增推荐项目</el-button>
        <el-input
          v-model="projectSearch"
          placeholder="搜索项目"
          clearable
          size="mini"
          style="width: 200px; margin-left: 10px;"
          @keyup.enter.native="filterProjects"
        >
          <el-button slot="append" icon="el-icon-search" @click="filterProjects"></el-button>
        </el-input>
      </div>

      <!-- 使用el-scrollbar包裹表格部分 -->
      <el-scrollbar style="height: 350px; margin-bottom: 10px;">
        <!-- 项目列表 -->
        <el-table
          v-loading="projectLoading"
          :data="projectList"
          style="width: 100%; margin-top: 15px;"
        >
          <el-table-column label="项目名称" align="center" prop="storeProductId">
            <template slot-scope="scope">
              <span>{{ getProductNameById(scope.row.storeProductId) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="建议间隔时长" align="center" prop="cycleLength">
            <template slot-scope="scope">
              <span>{{ scope.row.cycleLength }}日</span>
            </template>
          </el-table-column>
          <el-table-column label="最后执行时间" align="center" prop="lastDo" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.lastDo, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center" prop="createdByEmployeeName" />
          <el-table-column label="操作" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEditProject(scope.row)">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteProject(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>

      <!-- 项目分页 -->
      <pagination
        v-show="projectTotal > 0"
        :total="projectTotal"
        :page.sync="projectQuery.pageNum"
        :limit.sync="projectQuery.pageSize"
        @pagination="getProjectList"
        layout="total, sizes, prev, pager, next, jumper"
        small
      />
    </el-dialog>

    <!-- 新增：添加或编辑推荐项目对话框 -->
    <el-dialog
      :title="projectForm.id ? '编辑推荐项目' : '新增推荐项目'"
      :visible.sync="projectFormVisible"
      width="500px"
      append-to-body
    >
      <el-form ref="projectForm" :model="projectForm" :rules="projectRules" label-width="120px">
        <el-form-item label="推荐项目" prop="storeProductId">
          <el-select
            v-model="projectForm.storeProductId"
            placeholder="请选择推荐项目"
            filterable
            clearable
            style="width: 100%">
            <el-option
              v-for="product in productOptions"
              :key="product.id"
              :label="product.name"
              :value="product.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="建议间隔时长" prop="cycleLength">
          <el-input v-model="projectForm.cycleLength" placeholder="请输入建议间隔时长" style="width: 100%">
            <template slot="append">日</template>
          </el-input>
        </el-form-item>
        <el-form-item label="最后执行时间" prop="lastDo">
          <el-date-picker
            v-model="projectForm.lastDo"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
            @change="onLastDoChange"
          />
        </el-form-item>
        <!-- 新增字段：推荐是否成功、是否明确拒绝、备注 -->
        <el-form-item label="推荐是否成功" prop="accept" v-if="showAdditionalFields">
          <el-switch
            v-model="projectForm.accept"
            active-text="是"
            inactive-text="否"
            :active-value="true"
            :inactive-value="false">
          </el-switch>
        </el-form-item>
        <el-form-item label="是否明确拒绝" prop="refuse" v-if="showAdditionalFields">
          <el-switch
            v-model="projectForm.refuse"
            active-text="是"
            inactive-text="否"
            :active-value="true"
            :inactive-value="false">
          </el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="description" v-if="showAdditionalFields">
          <el-input
            v-model="projectForm.description"
            type="textarea"
            placeholder="请输入备注"
            style="width: 100%"
            :rows="3">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitProjectForm">确 定</el-button>
        <el-button @click="cancelProjectForm">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 历史推荐项目对话框 -->
    <el-dialog
      title="历史推荐项目"
      :visible.sync="historyProjectDialogVisible"
      width="800px"
      append-to-body
      :before-close="closeHistoryProjectDialog"
      class="history-project-dialog"
    >
      <div v-if="selectedMember.id" class="dialog-header">
        <span class="member-info">会员：{{ selectedMember.name }} (ID: {{ selectedMember.id }})</span>
      </div>

      <!-- 历史项目列表工具栏 -->
      <div class="history-project-toolbar">
        <el-input
          v-model="historyProjectSearch"
          placeholder="搜索历史项目"
          clearable
          size="mini"
          style="width: 200px; margin-left: 10px;"
          @keyup.enter.native="filterHistoryProjects"
        >
          <el-button slot="append" icon="el-icon-search" @click="filterHistoryProjects"></el-button>
        </el-input>
      </div>

      <!-- 使用el-scrollbar包裹表格部分 -->
      <el-scrollbar style="height: 350px; margin-bottom: 10px;">
        <el-table
          v-loading="historyProjectLoading"
          :data="historyProjectList"
          style="width: 100%; margin-top: 15px;"
        >
          <el-table-column label="推荐项目" align="center" prop="storeProductName">
            <template slot-scope="scope">
              <span>{{ getProductNameById(scope.row.storeProductId) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="推荐间隔时长" align="center" prop="cycleLength" />
          <el-table-column label="推荐是否成功" align="center" prop="accept">
            <template slot-scope="scope">
              <span>{{ scope.row.accept ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否明确拒绝" align="center" prop="refuse">
            <template slot-scope="scope">
              <span>{{ scope.row.refuse ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center" prop="description" />
          <el-table-column label="录入员工" align="center" prop="createdByEmployeeName" />
          <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>

      <!-- 历史项目分页 -->
      <pagination
        v-show="historyProjectTotal > 0"
        :total="historyProjectTotal"
        :page.sync="historyProjectQuery.pageNum"
        :limit.sync="historyProjectQuery.pageSize"
        @pagination="getHistoryProjectList"
        layout="total, sizes, prev, pager, next, jumper"
        small
      />
    </el-dialog>

    <!-- 添加或编辑历史推荐项目对话框 -->
    <el-dialog
      :title="historyProjectForm.id ? '编辑历史推荐项目' : '新增历史推荐项目'"
      :visible.sync="historyProjectFormVisible"
      width="600px"
      append-to-body
    >
      <!-- 修正历史推荐项目表单字段 -->
      <el-form ref="historyProjectForm" :model="historyProjectForm" :rules="historyProjectRules" label-width="120px">
        <el-form-item label="推荐项目" prop="storeProductId">
          <el-input v-model="historyProjectForm.storeProductId" placeholder="请输入推荐项目" style="width: 100%" />
        </el-form-item>
        <el-form-item label="建议间隔时长" prop="cycleLength">
          <el-input v-model="historyProjectForm.cycleLength" placeholder="请输入建议间隔时长" style="width: 100%" />
        </el-form-item>
        <el-form-item label="推荐是否成功" prop="accept">
          <el-radio-group v-model="historyProjectForm.accept">
            <el-radio :label="true">成功</el-radio>
            <el-radio :label="false">未成功</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否明确拒绝" prop="refuse">
          <el-radio-group v-model="historyProjectForm.refuse">
            <el-radio :label="true">拒绝</el-radio>
            <el-radio :label="false">未拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input v-model="historyProjectForm.description" type="textarea" placeholder="请输入备注" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHistoryProjectForm">确 定</el-button>
        <el-button @click="cancelHistoryProjectForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addMember, delMember, getMember, listMember, updateMember, getStoreRootId} from '@/api/store/member'
import { listAllMemberLevel} from '@/api/store/memberLevel'
import {listStore} from '@/api/store/store'
import {parseTime} from '@/utils/ruoyi'
import Pagination from '@/components/Pagination'
import { getInfo } from "@/api/login"
// 导入会员特性和推荐项目相关API
import {listFeature, getFeature, delFeature, addFeature, updateFeature} from '@/api/member/feature'
import {listProduct, getProduct, delProduct, addProduct, updateProduct} from '@/api/member/product'
import {listFeaturetype} from '@/api/member/featuretype'
// 在script标签中导入API
import {listProductadvance, getProductadvance, delProductadvance, addProductadvance, updateProductadvance}
  from '@/api/member/productadvance'
import { productOption } from "@/api/productManage/product"

export default {
  name: 'MemberPage',
  components: { Pagination },
  data() {
    return {
      // 添加single和multiple属性定义
      single: true,
      multiple: true,
      loading: false,
      memberList: [],
      total: 0,
      open: false,
      title: '',
      // 当前用户店铺ID
      currentUserStoreId: null,
      form: {
        name: '',
        phone: '',
        gender: '',
        memberLevelId: 0,
        storeId: 0,
        rootStoreId: 0,
        giftBalance: 0,
        realBalance: 0,
        password: '',
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式', trigger: 'blur' },
          { min: 11, max: 11, message: '手机号码必须为11位', trigger: 'blur' }
        ],
        gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
        memberLevelId: [{ required: true, message: '请选择会员等级', trigger: 'change' }],
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        phone: undefined,
        memberLevelId: undefined,
      },
      ids: [],
      memberLevelOptions: [],
      storeOptions: [],
      productOptions: [],

      //会员特性相关数据
      featureDialogVisible: false,
      featureFormVisible: false,
      featureLoading: false,
      featureList: [],
      featureTotal: 0,
      featureSearch: '',
      featureQuery: {
        pageNum: 1,
        pageSize: 10,
        memberId: null
      },
      featureForm: {
        id: null,
        memberId: null,
        memberFeatureId: null,
        value: null,
        inputBy: null
      },
      featureRules: {
        memberFeatureId: [{ required: true, message: '请输入特性ID', trigger: 'blur' }],
        value: [{ required: true, message: '请输入特性值', trigger: 'blur' }]
      },

      // 新增：推荐项目相关数据
      projectDialogVisible: false,
      projectFormVisible: false,
      projectLoading: false,
      projectList: [],
      projectTotal: 0,
      projectSearch: '',
      projectQuery: {
        pageNum: 1,
        pageSize: 10,
        memberId: null
      },
      projectForm: {
        id: null,
        memberId: null,
        storeProductId: null,
        cycleLength: null,
        lastDo: null,
        createdByEmployeeId: null,
        createdAt: new Date(),
        // 新增字段
        accept: false,
        refuse: false,
        description: null
      },
      // 保存原始的lastDo值，用于比较是否发生变化
      originalLastDo: null,
      projectRules: {
        storeProductId: [{ required: true, message: '请输入推荐项目', trigger: 'blur' }],
        cycleLength: [{ required: true, message: '请输入建议间隔时长', trigger: 'blur' }]
      },
      // 控制额外字段显示的标志
      showAdditionalFields: false,
// 在data中添加历史推荐项目相关数据
// 历史推荐项目相关数据
      historyProjectDialogVisible: false,
      historyProjectFormVisible: false,
      historyProjectLoading: false,
      historyProjectList: [],
      historyProjectTotal: 0,
      historyProjectSearch: '',
      historyProjectQuery: {
        pageNum: 1,
        pageSize: 10,
        memberId: null
      },
      historyProjectForm: {
        id: null,
        memberId: null,
        storeProductId: null,
        cycleLength: null,
        accept: false,
        refuse: false,
        remark: null
      },
      historyProjectRules: {
        storeProductId: [{ required: true, message: '请输入推荐项目', trigger: 'blur' }],
        cycleLength: [{ required: true, message: '请输入建议间隔时长', trigger: 'blur' }],
        accept: [{ required: true, message: '请选择推荐是否成功', trigger: 'change' }],
        refuse: [{ required: true, message: '请选择是否明确拒绝', trigger: 'change' }]
      },
      // 当前选中的会员
      selectedMember: {
        id: null,
        name: ''
      },
      // 特性类型选项
      featureTypeOptions: [],
      createdAtRange: null,
    }
  },
  computed: {
    // 根据主店面ID过滤店面选项，只显示主店面和其下属分店
    filteredStoreOptions() {
      if (!this.form.rootStoreId) {
        return this.storeOptions;
      }

      // 筛选出主店面和其下属分店
      return this.storeOptions.filter(store => {
        return store.id === this.form.rootStoreId || store.parentId === this.form.rootStoreId;
      });
    }
  },
  watch: {
    // 监听主店面ID变化，确保店面选项正确更新
    'form.rootStoreId': {
      handler(newVal) {
        // 当主店面ID变化时，如果当前选择的店面不在新的选项中，则清空选择
        if (this.form.storeName) {
          const store = this.filteredStoreOptions.find(s => s.name === this.form.storeName);
          if (!store) {
            this.form.storeName = '';
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    parseTime,
    /** 加载所有必要数据 */
    async loadData() {
      try {
        // 先加载基础数据
        await Promise.all([
          this.getMemberLevels(),
          this.getFeatureTypeOptions()
        ])

        // 然后获取用户信息并设置权限
        await this.getCurrentUser()

        // 最后加载会员列表
        this.getList()
      } catch (error) {
        console.error("数据加载失败:", error)
      }
    },
    /** 获取当前用户信息 */
    async getCurrentUser() {
      try {
        const response = await getInfo()
        let userData = response.data || response

        // 提取用户信息中的店铺ID
        if (userData.user && userData.user.rootStoreId !== undefined && userData.user.rootStoreId !== null) {
          this.currentUserStoreId = userData.user.rootStoreId
        } else if (userData.user && userData.user.storeId !== undefined && userData.user.storeId !== null) {
          this.currentUserStoreId = userData.user.storeId
        } else if (userData.rootStoreId !== undefined && userData.rootStoreId !== null) {
          this.currentUserStoreId = userData.rootStoreId
        } else if (userData.storeId !== undefined && userData.storeId !== null) {
          this.currentUserStoreId = userData.storeId
        }

        // 等待店铺列表加载完成后再判断店铺类型
        await this.getStoreList()

        // 根据店铺ID类型设置数据访问权限
        if (this.currentUserStoreId) {
          const currentStore = this.storeOptions.find(store => store.id === this.currentUserStoreId)
          if (currentStore) {
            if (!currentStore.parentId || currentStore.parentId === 0) {
              // 当前是主店面，可以查看主店面及其所有分店的会员
              this.queryParams.rootStoreId = this.currentUserStoreId
              delete this.queryParams.storeId
            } else {
              // 当前是分店，只能查看本店的会员
              this.queryParams.storeId = this.currentUserStoreId
              delete this.queryParams.rootStoreId
            }
          }
        }
      } catch (error) {
        console.error("获取用户信息失败:", error)
        this.$modal.msgError("获取用户信息失败")
      }
    },
    genderLabel(gender) {
      return gender === 'male' ? '男' : gender === 'female' ? '女' : ''
    },
    getList() {
      this.loading = true
      // 构建查询参数，包含店铺权限控制
      const queryParams = {
        ...this.queryParams,
        startDate: this.queryParams.startDate,
        endDate: this.queryParams.endDate
      }

      listMember(queryParams).then(res => {
        this.memberList = res.rows
        this.total = res.total
        this.loading = false
      }).catch(error => {
        console.error("获取会员列表失败:", error)
        this.loading = false
      })
    },
    getMemberLevels() {
      return listAllMemberLevel().then(res => {
        this.memberLevelOptions = res.data || []
        console.log('会员等级选项:', this.memberLevelOptions)
        return this.memberLevelOptions
      }).catch(error => {
        console.error('获取会员等级失败:', error)
        this.memberLevelOptions = []
        return []
      })
    },
    getStoreList() {
      listStore({}).then(response => {
        this.storeOptions = response.rows
      })
    },
    handleDateRangeChange(val) {
      if (val) {
        this.queryParams.startDate = val[0];
        this.queryParams.endDate = val[1];
      } else {
        this.queryParams.startDate = undefined;
        this.queryParams.endDate = undefined;
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;

      if (this.createdAtRange) {
        this.queryParams.beginCreatedAt = this.createdAtRange[0];
        this.queryParams.endCreatedAt = this.createdAtRange[1];
      } else {
        this.queryParams.beginCreatedAt = undefined;
        this.queryParams.endCreatedAt = undefined;
      }
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        phone: undefined,
        memberLevelId: undefined,
      }
      this.createdAtRange = null

      // 重新应用店铺权限
      this.getCurrentUser().then(() => {
        this.getList()
      })
    },
    handleAdd() {
      this.resetForm()
      // 自动填充主店面ID
      if (this.currentUserStoreId) {
        // 自动填充主店面ID和名称
        const store = this.storeOptions.find(s => s.id === this.currentUserStoreId)
        if (store) {
          this.form.rootStoreId = this.currentUserStoreId
          this.form.rootStoreName = store.name
        }
      } else {
        // 如果currentUserStoreId不存在，则尝试调用getStoreRootId接口
        getStoreRootId().then(response => {
          const rootStoreId = response.data;
          const store = this.storeOptions.find(s => s.id === rootStoreId)
          if (store) {
            this.form.rootStoreId = rootStoreId
            this.form.rootStoreName = store.name
          }
        }).catch(error => {
          console.error("获取主店面ID异常:", error)
        })
      }

      this.open = true
      this.title = '新增会员'
    },
    handleEdit(row) {
      getMember(row.id).then(res => {
        this.form = Object.assign({}, res.data)

        // 设置店面名称
        if (res.data.rootStoreName) {
          this.form.rootStoreName = res.data.rootStoreName
        } else if (this.form.rootStoreId) {
          const rootStore = this.storeOptions.find(s => s.id === this.form.rootStoreId)
          if (rootStore) {
            this.form.rootStoreName = rootStore.name
          }
        }

        if (res.data.storeName) {
          this.form.storeName = res.data.storeName
        } else if (this.form.storeId) {
          const store = this.storeOptions.find(s => s.id === this.form.storeId)
          if (store) {
            this.form.storeName = store.name
          }
        }

        this.open = true
        this.title = '编辑会员'
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除该会员吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (row && row.id) {
          delMember(row.id).then(() => {
            this.$message.success('删除成功')
            this.getList()
          }).catch(error => {
            console.error('删除会员失败:', error)
            this.$message.error('删除会员失败')
          })
        } else {
          this.$message.error('无效的会员ID')
        }
      })
    },

    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 根据店面名称获取店面ID
          if (this.form.storeName) {
            const store = this.storeOptions.find(s => s.name === this.form.storeName)
            if (store) {
              this.form.storeId = store.id
            }
          }

          // 确保主店面ID存在
          if (!this.form.rootStoreId && this.form.rootStoreName) {
            const rootStore = this.storeOptions.find(s => s.name === this.form.rootStoreName)
            if (rootStore) {
              this.form.rootStoreId = rootStore.id
            }
          }

          // 自动填充主店面ID（如果未设置）
          if (!this.form.rootStoreId && this.currentUserStoreId) {
            this.form.rootStoreId = this.currentUserStoreId;
            const store = this.storeOptions.find(s => s.id === this.currentUserStoreId);
            if (store) {
              this.form.rootStoreName = store.name;
            }
          }

          // 验证主店面ID是否存在
          if (!this.form.rootStoreId) {
            this.$message.error('主店面ID不能为空');
            return;
          }

          const submitData = {
            ...this.form,
            memberLevelId: this.form.memberLevelId ? Number(this.form.memberLevelId) : null,
            storeId: this.form.storeId ? Number(this.form.storeId) : null,
            rootStoreId: this.form.rootStoreId ? Number(this.form.rootStoreId) : null,
            giftBalance: Number(this.form.giftBalance) || 0,
            realBalance: Number(this.form.realBalance) || 0
          }

          if (submitData.id) {
            updateMember(submitData).then(() => {
              this.$message.success('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addMember(submitData).then(() => {
              this.$message.success('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    cancel() {
      this.open = false
      this.resetForm()
    },
    handleMemberLevelChange(levelId) {
      this.form.member_level_id = levelId
    },
    resetForm() {
      this.form = {
        name: '',
        phone: '',
        gender: '',
        memberLevelId: null,
        storeId: '',
        rootStoreId: '',
        storeName: '',
        rootStoreName: '',
        giftBalance: 0,
        realBalance: 0,
        password: '',
        createdAt: null,
        createdByEmployeeId: 111,
        createdByEmployeeName: ''
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    // 修改：查看会员特性
    viewMemberFeatures(row) {
      this.selectedMember = {
        id: row.id,
        name: row.name
      }
      this.featureQuery.memberId = row.id
      this.featureQuery.pageNum = 1
      this.getFeatureList()
      this.featureDialogVisible = true
    },

    // 查看推荐项目
    viewRecommendedProjects(row) {
      this.selectedMember = {
        id: row.id,
        name: row.name
      }
      this.projectQuery.memberId = row.id
      this.projectQuery.pageNum = 1
      this.getProjectList() // 调用 getProjectList 方法
      this.projectDialogVisible = true
    },

    // 获取会员特性列表
    getFeatureList() {
      this.featureLoading = true
      listFeature(this.featureQuery).then(response => {
        this.featureList = response.rows
        this.featureTotal = response.total
        this.featureLoading = false
      }).catch(() => {
        this.featureLoading = false
      })
    },

    // 获取推荐项目列表
    getProjectList() {
      this.projectLoading = true
      listProduct(this.projectQuery).then(response => {
        this.projectList = response.rows
        this.projectTotal = response.total
        this.projectLoading = false
      }).catch(() => {
        this.projectLoading = false
      })
    },

    // 新增：删除项目
    handleDeleteProject(row) {
      this.$modal.confirm('是否确认删除该推荐项目？').then(() => {
        return delProduct(row.id)
      }).then(() => {
        this.getProjectList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },

    // 新增：提交项目表单
    submitProjectForm() {
      this.$refs.projectForm.validate(valid => {
        if (valid) {
          // 检查是否已存在相同的推荐项目（同一会员不能添加重复的推荐项目）
          const isDuplicate = this.projectList.some(item => {
            // 编辑时排除自身
            if (this.projectForm.id && item.id === this.projectForm.id) {
              return false;
            }
            // 检查会员ID和项目ID是否相同
            return item.memberId === this.projectForm.memberId &&
              item.storeProductId === this.projectForm.storeProductId;
          });

          if (isDuplicate) {
            this.$message.warning('该会员已存在相同的推荐项目，请勿重复添加');
            return;
          }

          // 判断是否是更新操作且显示了额外字段（说明是修改最后执行时间的操作）
          const isUpdateWithAdditionalFields = this.projectForm.id && this.showAdditionalFields;

          if (this.projectForm.id) {
            updateProduct(this.projectForm).then(() => {
              this.$modal.msgSuccess("修改成功")
              this.projectFormVisible = false
              this.getProjectList()

              // 检查最后执行时间是否发生了变化
              const currentLastDo = this.projectForm.lastDo ? new Date(this.projectForm.lastDo).getTime() : null;
              const lastDoChanged = currentLastDo !== this.originalLastDo;

              // 只有在最后执行时间发生变化且显示了额外字段时，才保存到历史推荐项目记录中
              if (isUpdateWithAdditionalFields && lastDoChanged) {
                // 构造历史推荐项目数据
                const historyProjectData = {
                  memberId: this.projectForm.memberId,
                  storeProductId: this.projectForm.storeProductId,
                  cycleLength: this.projectForm.cycleLength,
                  accept: this.projectForm.accept,
                  refuse: this.projectForm.refuse,
                  description: this.projectForm.description,
                  createdByEmployeeId: this.$store.getters.id,
                  createdAt: new Date()
                };

                // 保存到历史推荐项目记录中
                addProductadvance(historyProjectData).then(() => {
                  console.log("历史推荐项目记录保存成功");
                }).catch(error => {
                  console.error("历史推荐项目记录保存失败:", error);
                  this.$modal.msgError("历史推荐项目记录保存失败");
                });
              }
            })
          } else {
            addProduct(this.projectForm).then(() => {
              this.$modal.msgSuccess("新增成功")
              this.projectFormVisible = false
              this.getProjectList()
            })
          }
        }
      })
    },

    // 关闭特性对话框
    closeFeatureDialog() {
      this.featureDialogVisible = false
      this.featureSearch = ''
    },

    // 关闭项目对话框
    closeProjectDialog() {
      this.projectDialogVisible = false
      this.projectSearch = ''
    },

    // 搜索特性
    filterFeatures() {
      this.featureQuery.pageNum = 1
      // 可以根据需要添加搜索条件
      this.getFeatureList()
    },

    // 搜索项目
    filterProjects() {
      this.projectQuery.pageNum = 1
      // 可以根据需要添加搜索条件
      this.getProjectList()
    },

    // 添加特性
    handleAddFeature() {
      // 确保特性类型选项已加载
      if (!this.featureTypeOptions || this.featureTypeOptions.length === 0) {
        this.getFeatureTypeOptions()
      }

      this.featureForm = {
        id: null,
        memberId: this.selectedMember.id,
        memberFeatureId: null,
        value: null,
        inputBy: this.$store.getters.id,
        inputByName: this.$store.getters.nickName || this.$store.getters.name
      }
      this.featureFormVisible = true
    },

    // 编辑特性
    handleEditFeature(row) {
      // 确保特性类型选项已加载
      if (!this.featureTypeOptions || this.featureTypeOptions.length === 0) {
        this.getFeatureTypeOptions()
      }

      getFeature(row.id).then(response => {
        this.featureForm = response.data
        this.featureFormVisible = true
      })
    },

    //删除特性
    handleDeleteFeature(row) {
      this.$modal.confirm('是否确认删除该会员特性？').then(() => {
        return delFeature(row.id)
      }).then(() => {
        this.getFeatureList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },

    // 提交特性表单
    submitFeatureForm() {
      this.$refs.featureForm.validate(valid => {
        if (valid) {
          // 检查是否已存在相同的会员特性（会员姓名+特性类型，不包括特性值）
          const isDuplicate = this.featureList.some(item => {
            // 编辑时排除自身
            if (this.featureForm.id && item.id === this.featureForm.id) {
              return false;
            }
            // 检查会员ID和特性类型是否相同（不包括特性值）
            return item.memberId === this.featureForm.memberId &&
              item.memberFeatureId === this.featureForm.memberFeatureId;
          });

          if (isDuplicate) {
            this.$message.warning('该会员已存在相同的特性类型');
            return;
          }

          if (this.featureForm.id) {
            updateFeature(this.featureForm).then(() => {
              this.$modal.msgSuccess("修改成功")
              this.featureFormVisible = false
              this.getFeatureList()
            })
          } else {
            addFeature(this.featureForm).then(() => {
              this.$modal.msgSuccess("新增成功")
              this.featureFormVisible = false
              this.getFeatureList()
            })
          }
        }
      })
    },

    // 取消特性表单
    cancelFeatureForm() {
      this.featureFormVisible = false
    },

    // 添加项目
    handleAddProject() {
      this.projectForm = {
        memberId: this.selectedMember.id,
        storeProductId: null,
        cycleLength: null,
        lastDo: null,
        createdByEmployeeId: this.$store.getters.id,
        createdAt: new Date(),
        // 初始化新增字段
        accept: false,
        refuse: false,
        description: null
      }
      // 隐藏额外字段
      this.showAdditionalFields = false
      // 获取产品选项
      this.getProductOptions()
      this.projectFormVisible = true
    },

    // 新增：取消项目表单
    cancelProjectForm() {
      this.projectFormVisible = false
      // 重置额外字段显示标志
      this.showAdditionalFields = false
      // 清除原始lastDo值
      this.originalLastDo = null
    },

    // 新增：编辑项目
    handleEditProject(row) {
      getProduct(row.id).then(response => {
        this.projectForm = response.data
        // 不再默认显示额外字段，只在用户修改最后执行时间时显示
        this.showAdditionalFields = false
        // 保存原始的lastDo值，用于后续比较
        this.originalLastDo = response.data.lastDo ? new Date(response.data.lastDo).getTime() : null
        // 获取产品选项
        this.getProductOptions()
        this.projectFormVisible = true
      })
    },

    // 在methods中添加历史推荐项目相关方法
// 查看历史推荐项目 - 打开模态框
    viewHistoryProjects(row) {
      this.selectedMember = {
        id: row.id,
        name: row.name
      }
      this.historyProjectQuery.memberId = row.id
      this.historyProjectQuery.pageNum = 1
      this.getHistoryProjectList()
      // 获取产品选项以确保推荐项目名称能正确显示
      this.getProductOptions()
      this.historyProjectDialogVisible = true
    },

// 获取历史推荐项目列表
    getHistoryProjectList() {
      this.historyProjectLoading = true
      listProductadvance(this.historyProjectQuery).then(response => {
        this.historyProjectList = response.rows
        this.historyProjectTotal = response.total
        this.historyProjectLoading = false
      }).catch(() => {
        this.historyProjectLoading = false
      })
    },

// 关闭历史项目对话框
    closeHistoryProjectDialog() {
      this.historyProjectDialogVisible = false
      this.historyProjectSearch = ''
    },

// 搜索历史项目
    filterHistoryProjects() {
      this.historyProjectQuery.pageNum = 1
      this.getHistoryProjectList()
    },

// 添加历史项目
    handleAddHistoryProject() {
      this.historyProjectForm = {
        memberId: this.selectedMember.id,
        storeProductId: null,
        cycleLength: null,
        accept: false,
        refuse: false,
        description: null,
        // 自动填充创建人和创建时间
        createdByEmployeeId: this.$store.getters.id,
        createdAt: new Date() // 自动填充当前时间为创建时间
      }
      this.historyProjectFormVisible = true
    },

    handleEditHistoryProject(row) {
      getProductadvance(row.id).then(response => {
        this.historyProjectForm = {
          ...response.data,
          accept: response.data.accept === true || response.data.accept === '是' || response.data.accept === 'true',
          refuse: response.data.refuse === true || response.data.refuse === '是' || response.data.refuse === 'true'
        };
        // 编辑时保持原有的录入员工和时间信息不变
        this.historyProjectFormVisible = true
      })
    },

    submitHistoryProjectForm() {
      this.$refs.historyProjectForm.validate(valid => {
        if (valid) {
          const formData = {
            ...this.historyProjectForm,
            accept: this.historyProjectForm.accept ? true : false,
            refuse: this.historyProjectForm.refuse ? true : false,
            // 新增或编辑时自动填充创建人和创建时间
            createdByEmployeeId: this.$store.getters.id,
          };
          if (this.historyProjectForm.id) {
            updateProductadvance(formData).then(() => {
              this.$modal.msgSuccess("修改成功")
              this.historyProjectFormVisible = false
              this.getHistoryProjectList()
            })
          } else {
            addProductadvance(formData).then(() => {
              this.$modal.msgSuccess("新增成功")
              this.historyProjectFormVisible = false
              this.getHistoryProjectList()
            })
          }
        }
      })
    },
// 取消历史项目表单
    cancelHistoryProjectForm() {
      this.historyProjectFormVisible = false
    },
    /** 获取特性类型选项 */
    getFeatureTypeOptions() {
      listFeaturetype().then(response => {
        this.featureTypeOptions = response.rows || []
        console.log('特性类型选项:', this.featureTypeOptions)
      }).catch(error => {
        console.error('获取特性类型失败:', error)
        this.featureTypeOptions = []
      })
    },

    /** 根据ID获取产品名称 */
    getProductNameById(productId) {
      if (!productId) return '';

      const product = this.productOptions.find(item => item.id === productId);
      return product ? product.name : productId;
    },

    /** 获取产品选项数据 */
    getProductOptions() {
      // 使用 productOption 接口获取所有产品选项，参照 KPI 设置模块的实现方式
      productOption({}).then(response => {
        this.productOptions = response.data || []
      }).catch(error => {
        console.error('获取产品列表失败', error)
        this.productOptions = []
      })
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      switch (command) {
        case 'viewProjects':
          this.viewRecommendedProjects(row)
          break
        case 'viewFeatures':
          this.viewMemberFeatures(row)
          break
        case 'viewHistoryProjects':
          this.viewHistoryProjects(row)
          break
      }
    },
    // 当最后执行时间发生变化时调用
    onLastDoChange(value) {
      // 当用户修改最后执行时间时，显示额外字段
      this.showAdditionalFields = true;
    },
  },
}
</script>

<style scoped>
.head-container {
  margin-bottom: 20px;
}

/* 表格容器样式 */
.app-container {
  padding: 20px;
}

/* 表格样式优化 */
.el-table {
  border: 1px solid #ebeef5;
}

/* 表头样式 */
.el-table :deep(.el-table__header-wrapper) {
  background-color: #f5f7fa;
}

.el-table :deep(.el-table__header th) {
  background-color: #f5f7fa !important;
  color: #606266;
  font-weight: 600;
  border-bottom: 2px solid #ebeef5;
}

/* 冻结列样式优化 */
.el-table .el-table__fixed-left,
.el-table .el-table__fixed-right {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
  z-index: 10;
}

/* 冻结列表头样式 */
.el-table .el-table__fixed-left .el-table__fixed-header-wrapper,
.el-table .el-table__fixed-right .el-table__fixed-header-wrapper {
  background-color: #f5f7fa;
}

.el-table .el-table__fixed-left .el-table__fixed-header-wrapper th,
.el-table .el-table__fixed-right .el-table__fixed-header-wrapper th {
  background-color: #f5f7fa !important;
  border-bottom: 2px solid #ebeef5;
}

/* 冻结列背景色 */
.el-table .el-table__fixed-left .el-table__fixed-body-wrapper,
.el-table .el-table__fixed-right .el-table__fixed-body-wrapper {
  background-color: #fff;
}

/* 冻结列边框 */
.el-table .el-table__fixed-left::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 1px;
  background-color: #ebeef5;
  z-index: 11;
}

.el-table .el-table__fixed-right::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 1px;
  background-color: #ebeef5;
  z-index: 11;
}

/* 表格行样式 */
.el-table :deep(.el-table__row) {
  transition: background-color 0.25s ease;
}

.el-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 新增样式 */
.dialog-header {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.member-info {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.feature-toolbar,
.project-toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}

/* 确保模态框内的表格不会太拥挤 */
.el-dialog .el-table {
  margin-bottom: 15px;
}

/* 确保分页控件样式适合模态框 */
.el-dialog .pagination-container {
  padding: 5px 0;
}

/* 自定义滚动条样式 */
.feature-dialog :deep(.el-scrollbar__wrap),
.project-dialog :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}
/* 在样式中添加历史项目对话框样式 */
/* 历史项目对话框样式 */
.history-project-dialog :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

.history-project-dialog .el-table {
  min-width: 750px;
}

.history-project-dialog .el-table .el-table__fixed-right {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

.history-project-toolbar {
  margin-bottom: 15px;
}
/* 设置表格最小宽度，确保所有列都能显示 */
.feature-dialog .el-table,
.project-dialog .el-table {
  min-width: 750px;
}

/* 确保操作列始终可见 */
.feature-dialog .el-table .el-table__fixed-right,
.project-dialog .el-table .el-table__fixed-right {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}
</style>
