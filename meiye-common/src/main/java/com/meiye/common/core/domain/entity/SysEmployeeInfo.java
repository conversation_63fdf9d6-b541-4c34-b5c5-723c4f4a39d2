package com.meiye.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.util.Date;

/**
 * 员工扩展信息表
 */

@Data

public class SysEmployeeInfo {
    /**
     * 员工ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出生日期
     */
    @TableField(value = "birthday")
    private Date birthday;

    @TableField(value = "employment_date")

    private Date employmentData;

    /**
     * 头像
     */
    @TableField(value = "avatar")
    private String avatar;

    /**
     * 照片
     */
    @TableField(value = "photo")
    private String photo;

    /**
     * 身份证号码
     */
    @TableField(value = "identity_card")
    private String identityCard;

    /**
     * 联系电话
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 自我介绍
     */
    @TableField(value = "summary")
    private String summary;

    /**
     * 特长
     */
    @TableField(value = "specialty")
    private String specialty;

    /**
     * 学习能力 C-普通,B-良好,A-优秀,S-极强
     */
    @TableField(value = "learning_ability")
    private Object learningAbility;

    /**
     * 教育背景
     */
    @TableField(value = "education")
    private String education;

    /**
     * 毕业时间
     */
    @TableField(value = "edu_time")
    private Date eduTime;

    /**
     * 证书，多项使用逗号分隔
     */
    @TableField(value = "certificate")
    private String certificate;
}