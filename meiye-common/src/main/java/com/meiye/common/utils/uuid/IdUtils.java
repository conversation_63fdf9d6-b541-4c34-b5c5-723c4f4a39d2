package com.meiye.common.utils.uuid;

import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;

/**
 * ID生成器工具类
 * 
 * <AUTHOR>
 */
public class IdUtils
{
    static {
        // 初始化雪花ID生成器（使用默认配置）
        initSnowflake();
    }

    /**
     * 初始化雪花ID生成器
     */
    public static void initSnowflake() {
        IdGeneratorOptions options = new IdGeneratorOptions((short) 1); // 使用默认方法1
        YitIdHelper.setIdGenerator(options);
    }

    /**
     * 生成雪花ID (long类型)
     *
     * @return 雪花ID
     */
    public static long getSnowflakeId() {
        return YitIdHelper.nextId();
    }
    /**
     * 获取随机UUID
     * 
     * @return 随机UUID
     */
    public static String randomUUID()
    {
        return UUID.randomUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线
     * 
     * @return 简化的UUID，去掉了横线
     */
    public static String simpleUUID()
    {
        return UUID.randomUUID().toString(true);
    }

    /**
     * 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID
     * 
     * @return 随机UUID
     */
    public static String fastUUID()
    {
        return UUID.fastUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID
     * 
     * @return 简化的UUID，去掉了横线
     */
    public static String fastSimpleUUID()
    {
        return UUID.fastUUID().toString(true);
    }
}
