package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.SoldCard;
import com.meiye.api.service.SoldCardService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 已售消费卡Controller
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequestMapping("/storeManage/soldcard")
public class SoldCardController extends BaseController
{
    @Autowired
    private SoldCardService soldCardService;

    /**
     * 查询已售消费卡列表
     */
    @PreAuthorize("@ss.hasPermi('storeManage:soldcard:list')")
    @GetMapping("/list")
    public TableDataInfo list(SoldCard soldCard)
    {
        startPage();
        List<SoldCard> list = soldCardService.selectSoldCardList(soldCard);
        return getDataTable(list);
    }

    /**
     * 导出已售消费卡列表
     */
    @PreAuthorize("@ss.hasPermi('storeManage:soldcard:export')")
    @Log(title = "已售消费卡", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SoldCard soldCard)
    {
        List<SoldCard> list = soldCardService.selectSoldCardList(soldCard);
        ExcelUtil<SoldCard> util = new ExcelUtil<SoldCard>(SoldCard.class);
        util.exportExcel(response, list, "已售消费卡数据");
    }

    /**
     * 获取已售消费卡详细信息
     */
    @PreAuthorize("@ss.hasPermi('storeManage:soldcard:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(soldCardService.selectSoldCardById(id));
    }

    /**
     * 新增已售消费卡
     */
    @PreAuthorize("@ss.hasPermi('storeManage:soldcard:add')")
    @Log(title = "已售消费卡", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SoldCard soldCard)
    {
        return toAjax(soldCardService.insertSoldCard(soldCard));
    }

    /**
     * 修改已售消费卡
     */
    @PreAuthorize("@ss.hasPermi('storeManage:soldcard:edit')")
    @Log(title = "已售消费卡", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SoldCard soldCard)
    {
        return toAjax(soldCardService.updateSoldCard(soldCard));
    }

    /**
     * 删除已售消费卡
     */
    @PreAuthorize("@ss.hasPermi('storeManage:soldcard:remove')")
    @Log(title = "已售消费卡", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(soldCardService.deleteSoldCardByIds(ids));
    }
    
    /**
     * 根据店面销售物ID获取销售物名称
     */
    @PreAuthorize("@ss.hasPermi('storeManage:soldcard:list')")
    @GetMapping(value = "/product/name/{storeProductId}")
    public AjaxResult getProductNameByStoreProductId(@PathVariable("storeProductId") Long storeProductId)
    {
        String productName = soldCardService.getProductNameByStoreProductId(storeProductId);
        return success(productName);
    }
}