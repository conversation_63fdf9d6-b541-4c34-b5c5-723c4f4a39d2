package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.MemberFeatureLog;
import com.meiye.api.service.MemberFeatureLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 会员特性变更记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/member/featurelog")
public class MemberFeatureLogController extends BaseController
{
    @Autowired
    private MemberFeatureLogService memberFeatureLogService;

    /**
     * 查询会员特性变更记录列表
     */
    @PreAuthorize("@ss.hasPermi('member:featurelog:list')")
    @GetMapping("/list")
    public TableDataInfo list(MemberFeatureLog memberFeatureLog)
    {
        startPage();
        List<MemberFeatureLog> list = memberFeatureLogService.selectMemberFeatureLogList(memberFeatureLog);
        return getDataTable(list);
    }

    /**
     * 导出会员特性变更记录列表
     */
    @PreAuthorize("@ss.hasPermi('member:featurelog:export')")
    @Log(title = "会员特性变更记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MemberFeatureLog memberFeatureLog)
    {
        List<MemberFeatureLog> list = memberFeatureLogService.selectMemberFeatureLogList(memberFeatureLog);
        ExcelUtil<MemberFeatureLog> util = new ExcelUtil<MemberFeatureLog>(MemberFeatureLog.class);
        util.exportExcel(response, list, "会员特性变更记录数据");
    }

    /**
     * 获取会员特性变更记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('member:featurelog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(memberFeatureLogService.selectMemberFeatureLogById(id));
    }

    /**
     * 新增会员特性变更记录
     */
    @PreAuthorize("@ss.hasPermi('member:featurelog:add')")
    @Log(title = "会员特性变更记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MemberFeatureLog memberFeatureLog)
    {
        return toAjax(memberFeatureLogService.insertMemberFeatureLog(memberFeatureLog));
    }

    /**
     * 修改会员特性变更记录
     */
    @PreAuthorize("@ss.hasPermi('member:featurelog:edit')")
    @Log(title = "会员特性变更记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MemberFeatureLog memberFeatureLog)
    {
        return toAjax(memberFeatureLogService.updateMemberFeatureLog(memberFeatureLog));
    }

    /**
     * 删除会员特性变更记录
     */
    @PreAuthorize("@ss.hasPermi('member:featurelog:remove')")
    @Log(title = "会员特性变更记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(memberFeatureLogService.deleteMemberFeatureLogByIds(ids));
    }
}
