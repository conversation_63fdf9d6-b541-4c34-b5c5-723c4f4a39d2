package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.MemberProductAdvance;
import com.meiye.api.service.MemberProductAdvanceService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 为会员推荐过的项目记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/member/productadvance")
public class MemberProductAdvanceController extends BaseController
{
    @Autowired
    private MemberProductAdvanceService memberProductAdvanceService;

    /**
     * 查询为会员推荐过的项目记录列表
     */
    @PreAuthorize("@ss.hasPermi('member:productadvance:list')")
    @GetMapping("/list")
    public TableDataInfo list(MemberProductAdvance memberProductAdvance)
    {
        startPage();
        List<MemberProductAdvance> list = memberProductAdvanceService.selectMemberProductAdvanceList(memberProductAdvance);
        return getDataTable(list);
    }

    /**
     * 导出为会员推荐过的项目记录列表
     */
    @PreAuthorize("@ss.hasPermi('member:productadvance:export')")
    @Log(title = "为会员推荐过的项目记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MemberProductAdvance memberProductAdvance)
    {
        List<MemberProductAdvance> list = memberProductAdvanceService.selectMemberProductAdvanceList(memberProductAdvance);
        ExcelUtil<MemberProductAdvance> util = new ExcelUtil<MemberProductAdvance>(MemberProductAdvance.class);
        util.exportExcel(response, list, "为会员推荐过的项目记录数据");
    }

    /**
     * 获取为会员推荐过的项目记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('member:productadvance:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(memberProductAdvanceService.selectMemberProductAdvanceById(id));
    }

    /**
     * 新增为会员推荐过的项目记录
     */
    @PreAuthorize("@ss.hasPermi('member:productadvance:add')")
    @Log(title = "为会员推荐过的项目记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MemberProductAdvance memberProductAdvance)
    {
        return toAjax(memberProductAdvanceService.insertMemberProductAdvance(memberProductAdvance));
    }

    /**
     * 修改为会员推荐过的项目记录
     */
    @PreAuthorize("@ss.hasPermi('member:productadvance:edit')")
    @Log(title = "为会员推荐过的项目记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MemberProductAdvance memberProductAdvance)
    {
        return toAjax(memberProductAdvanceService.updateMemberProductAdvance(memberProductAdvance));
    }

    /**
     * 删除为会员推荐过的项目记录
     */
    @PreAuthorize("@ss.hasPermi('member:productadvance:remove')")
    @Log(title = "为会员推荐过的项目记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(memberProductAdvanceService.deleteMemberProductAdvanceByIds(ids));
    }
}