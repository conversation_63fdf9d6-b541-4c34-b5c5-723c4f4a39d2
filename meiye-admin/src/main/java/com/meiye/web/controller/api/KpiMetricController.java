package com.meiye.kpi.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.KpiMetric;
import com.meiye.api.service.KpiMetricService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * KPI指标Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
@RequestMapping("/kpi/metric")
public class KpiMetricController extends BaseController
{
    @Autowired
    private KpiMetricService kpiMetricService;

    /**
     * 查询KPI指标列表
     */
    @PreAuthorize("@ss.hasPermi('kpi:metric:list')")
    @GetMapping("/list")
    public TableDataInfo list(KpiMetric kpiMetric)
    {
        startPage();
        List<KpiMetric> list = kpiMetricService.selectKpiMetricList(kpiMetric);
        return getDataTable(list);
    }

    /**
     * 导出KPI指标列表
     */
    @PreAuthorize("@ss.hasPermi('kpi:metric:export')")
    @Log(title = "KPI指标", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KpiMetric kpiMetric)
    {
        List<KpiMetric> list = kpiMetricService.selectKpiMetricList(kpiMetric);
        ExcelUtil<KpiMetric> util = new ExcelUtil<KpiMetric>(KpiMetric.class);
        util.exportExcel(response, list, "KPI指标数据");
    }

    /**
     * 获取KPI指标详细信息
     */
    @PreAuthorize("@ss.hasPermi('kpi:metric:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(kpiMetricService.selectKpiMetricById(id));
    }

    /**
     * 新增KPI指标
     */
    @PreAuthorize("@ss.hasPermi('kpi:metric:add')")
    @Log(title = "KPI指标", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KpiMetric kpiMetric)
    {
        return toAjax(kpiMetricService.insertKpiMetric(kpiMetric));
    }

    /**
     * 修改KPI指标
     */
    @PreAuthorize("@ss.hasPermi('kpi:metric:edit')")
    @Log(title = "KPI指标", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KpiMetric kpiMetric)
    {
        return toAjax(kpiMetricService.updateKpiMetric(kpiMetric));
    }

    /**
     * 删除KPI指标
     */
    @PreAuthorize("@ss.hasPermi('kpi:metric:remove')")
    @Log(title = "KPI指标", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kpiMetricService.deleteKpiMetricByIds(ids));
    }
}
