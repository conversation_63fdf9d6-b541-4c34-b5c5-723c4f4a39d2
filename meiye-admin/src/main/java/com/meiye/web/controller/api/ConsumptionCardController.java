package com.meiye.web.controller.api;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.framework.web.domain.server.Sys;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.api.domain.ConsumptionCard;
import com.meiye.api.service.ConsumptionCardService;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 消费卡补充定义Controller
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/card")
public class ConsumptionCardController extends BaseController
{
    @Autowired
    private ConsumptionCardService consumptionCardService;

    /**
     * 查询消费卡补充定义列表
     */
    @PreAuthorize("@ss.hasPermi('system:card:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConsumptionCard consumptionCard)
    {
        System.out.println(Arrays.toString(consumptionCard.getProductIds()));
        startPage();
        List<ConsumptionCard> list = consumptionCardService.selectConsumptionCardList(consumptionCard);
        return getDataTable(list);
    }

    /**
     * 导出消费卡补充定义列表
     */
    @PreAuthorize("@ss.hasPermi('system:card:export')")
    @Log(title = "消费卡补充定义", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConsumptionCard consumptionCard)
    {
        List<ConsumptionCard> list = consumptionCardService.selectConsumptionCardList(consumptionCard);
        ExcelUtil<ConsumptionCard> util = new ExcelUtil<ConsumptionCard>(ConsumptionCard.class);
        System.out.println("导出数据量：" + list.size());

        util.exportExcel(response, list, "消费卡补充定义数据");
    }

    /**
     * 获取消费卡补充定义详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:card:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(consumptionCardService.selectConsumptionCardById(id));
    }

    /**
     * 新增消费卡补充定义
     */
    @PreAuthorize("@ss.hasPermi('system:card:add')")
    @Log(title = "消费卡补充定义", businessType = BusinessType.INSERT)
    @PostMapping
    public int insertConsumptionCard(@RequestBody ConsumptionCard consumptionCard) {
        return consumptionCardService.insertConsumptionCard(consumptionCard);
    }
    /**
     * 修改消费卡补充定义
     */
    @PreAuthorize("@ss.hasPermi('system:card:edit')")
    @Log(title = "消费卡补充定义", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ConsumptionCard consumptionCard)
    {
        return toAjax(consumptionCardService.updateConsumptionCard(consumptionCard));
    }

    /**
     * 删除消费卡补充定义
     */
    @PreAuthorize("@ss.hasPermi('system:card:remove')")
    @Log(title = "消费卡补充定义", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(consumptionCardService.deleteConsumptionCardByIds(ids));
    }
}
