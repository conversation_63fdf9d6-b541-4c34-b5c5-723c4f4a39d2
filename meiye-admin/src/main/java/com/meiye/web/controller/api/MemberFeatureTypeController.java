package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.service.MemberFeatureTypeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.api.domain.MemberFeatureType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 特性类型Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/member/featuretype")
public class MemberFeatureTypeController extends BaseController
{
    @Autowired
    private MemberFeatureTypeService memberFeatureTypeService;

    /**
     * 查询特性类型列表
     */
    @PreAuthorize("@ss.hasPermi('member:featuretype:list')")
    @GetMapping("/list")
    public TableDataInfo list(MemberFeatureType memberFeatureType)
    {
        startPage();
        List<MemberFeatureType> list = memberFeatureTypeService.selectMemberFeatureTypeList(memberFeatureType);
        return getDataTable(list);
    }

    /**
     * 获取所有特性类型（不分页）
     */
    @PreAuthorize("@ss.hasPermi('member:featuretype:list')")
    @GetMapping("/listAll")
    public AjaxResult listAllFeatureTypes()
    {
        List<MemberFeatureType> list = memberFeatureTypeService.selectAllMemberFeatureTypes();
        return AjaxResult.success(list);
    }

    /**
     * 导出特性类型列表
     */
    @PreAuthorize("@ss.hasPermi('member:featuretype:export')")
    @Log(title = "特性类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MemberFeatureType memberFeatureType)
    {
        List<MemberFeatureType> list = memberFeatureTypeService.selectMemberFeatureTypeList(memberFeatureType);
        ExcelUtil<MemberFeatureType> util = new ExcelUtil<MemberFeatureType>(MemberFeatureType.class);
        util.exportExcel(response, list, "特性类型数据");
    }

    /**
     * 获取特性类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('member:featuretype:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(memberFeatureTypeService.selectMemberFeatureTypeById(id));
    }

    /**
     * 新增特性类型
     */
    @PreAuthorize("@ss.hasPermi('member:featuretype:add')")
    @Log(title = "特性类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MemberFeatureType memberFeatureType)
    {
        try {
            // 自动填充当前登录用户的店铺ID作为主店面ID
            Long storeId = getStoreId();
            System.out.println("新增特性类型 - 获取到的店铺ID: " + storeId);
            System.out.println("新增特性类型 - 传入的memberFeatureType: " + memberFeatureType);

            if (storeId != null) {
                memberFeatureType.setRootStoreId(storeId);
                System.out.println("新增特性类型 - 设置后的rootStoreId: " + memberFeatureType.getRootStoreId());
            } else {
                System.out.println("新增特性类型 - 警告: 店铺ID为空");
            }

            return toAjax(memberFeatureTypeService.insertMemberFeatureType(memberFeatureType));
        } catch (Exception e) {
            e.printStackTrace();
            return error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 修改特性类型
     */
    @PreAuthorize("@ss.hasPermi('member:featuretype:edit')")
    @Log(title = "特性类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MemberFeatureType memberFeatureType)
    {
        try {
            // 确保修改时也有正确的店铺ID
            if (memberFeatureType.getRootStoreId() == null) {
                Long storeId = getStoreId();
                if (storeId != null) {
                    memberFeatureType.setRootStoreId(storeId);
                }
            }

            return toAjax(memberFeatureTypeService.updateMemberFeatureType(memberFeatureType));
        } catch (Exception e) {
            e.printStackTrace();
            return error("修改失败: " + e.getMessage());
        }
    }

    /**
     * 删除特性类型
     */
    @PreAuthorize("@ss.hasPermi('member:featuretype:remove')")
    @Log(title = "特性类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(memberFeatureTypeService.deleteMemberFeatureTypeByIds(ids));
    }
    
    /**
     * 获取当前登录用户的店铺ID
     */
    @PreAuthorize("@ss.hasPermi('member:featuretype:list')")
    @GetMapping("/getStoreRootId")
    public AjaxResult getStoreRootId() {
        try {
            Long storeId = getStoreId();
            System.out.println("获取到的店铺ID: " + storeId); // 添加日志
            if (storeId == null) {
                return error("当前用户未绑定店铺");
            }
            return success(storeId);
        } catch (Exception e) {
            e.printStackTrace(); // 添加异常打印
            return error("获取店铺ID失败: " + e.getMessage());
        }
    }
}