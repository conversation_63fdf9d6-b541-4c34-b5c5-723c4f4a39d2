package com.meiye.web.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.meiye.api.domain.EmployeeInfo;
import com.meiye.api.domain.Member;
import com.meiye.api.domain.RecordingDevice;
import com.meiye.api.domain.Store;
import com.meiye.api.service.EmployeeInfoService;
import com.meiye.api.service.IStoreService;
import com.meiye.api.service.MemberService;
import com.meiye.api.service.RecordingDeviceService;
import com.meiye.api.vo.RecordingDeviceVo;
import com.meiye.common.annotation.Log;
import com.meiye.common.constant.Constants;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.domain.entity.SysDictData;
import com.meiye.common.core.domain.entity.SysUser;
import com.meiye.common.core.domain.model.LoginBody;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.exception.ServiceException;
import com.meiye.common.utils.SecurityUtils;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.utils.uuid.IdUtils;
import com.meiye.framework.web.service.SysLoginService;
import com.meiye.system.domain.SysPost;
import com.meiye.system.service.ISysPostService;
import com.meiye.system.service.impl.SysUserServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 店面Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/store")
public class StoreController extends BaseController {
    @Autowired
    private IStoreService storeService;

    @Autowired
    private ISysPostService postService;
    @Autowired
    private SysUserServiceImpl sysUserServiceImpl;
    @Autowired
    private RecordingDeviceService recordingDeviceService;
   @Autowired
    private SysLoginService loginService;
   @Autowired
   private MemberService memberService;
   @Autowired
   EmployeeInfoService employeeInfoService;

    /**
     * 查询店面列表
     */
    @PreAuthorize("@ss.hasPermi('store:store:list')")
    @GetMapping("/list")
    public TableDataInfo list(Store store) {
        startPage();
        List<Store> list = storeService.selectStoreList(store);
        return getDataTable(list);
    }

    //获取铺列表
    @GetMapping("/option")
    public AjaxResult option_list(Store store) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        if(store.getParentId()!=null&&store.getParentId()==0){
            /*获取当前登录店铺*/
            if (getStoreId()!=null){
                queryWrapper.eq("parent_id", getStoreId())
                        .or()
                        .eq("id", getStoreId());
            }
        }else{
            if (store.getType() != null) {
                queryWrapper.eq("type", store.getType());
            }

        }

        List<Store> list = storeService.list(queryWrapper);
        return success(list);
    }

    //获取未分配店铺列表
    @GetMapping("/unallocated")
    public AjaxResult unallocated() {
        List<Store> list = storeService.unallocated();
        return success(list);
    }

    @GetMapping("/list-by-store-id/{storeId}")
    public AjaxResult getList(@PathVariable("storeId") Long storeId) {
        return success(storeService.selectNoSysStoreById(storeId));
    }


    /**
     * 导出店面列表
     */
    @PreAuthorize("@ss.hasPermi('store:store:export')")
    @Log(title = "店面", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Store store) {
        List<Store> list = storeService.selectStoreList(store);
        ExcelUtil<Store> util = new ExcelUtil<Store>(Store.class);
        util.exportExcel(response, list, "店面数据");
    }

    /**
     * 获取店面详细信息
     */
    @PreAuthorize("@ss.hasPermi('store:store:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {

        //return success(storeService.selectStoreById(id));
        return success(storeService.selectStoreInfo(id));
    }

    /**
     * 新增店面
     */
    @PreAuthorize("@ss.hasPermi('store:store:add')")
    @Log(title = "店面", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Store store) {
        store.setCreatedBy(getUserId());
        store.setCreatedAt(new Date());
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", store.getName());
        if (storeService.count(queryWrapper) > 0) {
            return error("店铺名称已存在");
        }
        String storeCode = IdUtils.fastSimpleUUID();
        store.setStoreCode(storeCode);
        return toAjax(storeService.insertStoreAndDept(store));
    }

    /**
     * 修改店面
     */
    @PreAuthorize("@ss.hasPermi('store:store:edit')")
    @Log(title = "店面", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Store store) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", store.getName());
        queryWrapper.ne("id", store.getId());
        if (storeService.count(queryWrapper) > 0) {
            return error("店铺名称已存在");
        }

        return toAjax(storeService.updateStoreAndDept(store));
    }

    /**
     * 修改当前登录店面的员工信息
     */
    @PutMapping("/current-update-user")
    public AjaxResult editCurrent(@RequestBody EmployeeInfo employeeInfo) {
        // 更新员工信息
        employeeInfo.setId(getUserId());
        EmployeeInfo newEmployeeInfo=new EmployeeInfo();
        BeanUtils.copyProperties(employeeInfo, newEmployeeInfo,"employmentDate");

        int updateResult = employeeInfoService.updateEmployeeInfo(newEmployeeInfo);

        if (updateResult > 0) {
            // 更新成功，重新查询完整的员工信息
            EmployeeInfo updatedEmployee = employeeInfoService.selectEmployeeInfoById(newEmployeeInfo.getId());

            // 返回完整的员工信息
            return success(storeService.selectStoreUserInfo(getUserId()));
        } else {
            // 更新失败
            return AjaxResult.error("更新失败");
        }
    }

    /**
     * 删除店面
     */
    @PreAuthorize("@ss.hasPermi('store:store:remove')")
    @Log(title = "店面", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(storeService.deleteStoreByIds(ids));
    }


    /**
     * 获取当前登录店面信息
     */
    @GetMapping(value = "/current-store")
    public AjaxResult getCurrentStore() {
        return success(storeService.selectStoreInfo(getStoreId()));
    }

    /**
     * 获取当前登录店面信息
     */
    @GetMapping(value = "/current-department-position-list")
    public AjaxResult getStorePostList() {
        SysPost post = new SysPost();
        post.setStoreId(getStoreId());
        return success(postService.selectPostList(post));
    }

    /**
     * 获取当前登录店面信息
     */
    @GetMapping(value = "/current-user")
    public AjaxResult getCurrentUser() {

        return success(storeService.selectStoreUserInfo(getUserId()));
    }

    /**
     * 获取当前登录店面员工列表
     */
    @GetMapping(value = "/store-user-list")
    public AjaxResult getStoreUserList(Integer root) {
        SysUser user = sysUserServiceImpl.selectUserById(getUserId());
        List<Map<String, Object> > list = new ArrayList<>();
        if (user != null) {
            if (user.getRootStoreId() == null || user.getStoreId() == null) {
                return error("该用户未绑定店铺");
            }
            if (root == 1) {
                //获取主店铺的员工列表
                list = sysUserServiceImpl.getStoreUserList(user.getRootStoreId());
            } else {
                list =   sysUserServiceImpl.getStoreUserList(user.getStoreId());
            }
        }
        return success(list);
    }
    /**
     * 获取当前登录店面的录音设备列表
     */
    @GetMapping(value = "/record-device-list")
    public AjaxResult getRecordDeviceList() {
        List<RecordingDeviceVo> list = recordingDeviceService.getRecordDeviceVoList();
        return success(list);
    }


    /**
     * 获取所有店铺（不分页）
     */
    @PreAuthorize("@ss.hasPermi('store:store:list')")
    @GetMapping("/listAll")
    public AjaxResult listAll() {
        List<Store> list = storeService.listAllStores();
        return AjaxResult.success(list);
    }
    
    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword());
        Map<String, Object> map = new HashMap<>();
        map.put("token", token);
        return AjaxResult.success(map);

    }


    @GetMapping("/member/member-list")
    public TableDataInfo getMemberList(Member member)
    {
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        if (sysUser.getRootStoreId()==null){
             throw new ServiceException("用户未分配门店，请联系管理员");
        }
        startPage();
        QueryWrapper<Member> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("root_store_id",sysUser.getRootStoreId());
        if (member.getMemberLevelId()!=null){
            queryWrapper.eq("member_level_id",member.getMemberLevelId());

        }
        if (member.getName()!=null){
            queryWrapper.eq("name",member.getName());

        }
        if (member.getActive()!=null){
            queryWrapper.eq("active",member.getActive());

        }
        if (member.getGender()!=null){
            queryWrapper.eq("gender",member.getGender());

        }
        if (member.getPhone()!=null){
            queryWrapper.like("phone",member.getPhone());
        }
        if (member.getSn()!=null){
            queryWrapper.like("sn",member.getSn());
        }


        List<Member> list = memberService.list(queryWrapper);
        return getDataTable(list);
    }


}
