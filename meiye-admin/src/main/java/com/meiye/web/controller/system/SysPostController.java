package com.meiye.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.domain.entity.SysUser;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.system.domain.SysPost;
import com.meiye.system.service.ISysPostService;

/**
 * 岗位信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/post")
public class SysPostController extends BaseController
{
    @Autowired
    private ISysPostService postService;

    /**
     * 获取岗位列表
     */
    @PreAuthorize("@ss.hasPermi('system:post:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysPost post)
    {
        // 非管理员用户只能查看自己店铺的岗位
        if (!getLoginUser().getUser().isAdmin()) {
            post.setStoreId(getStoreId());
        }
        
        startPage();
        List<SysPost> list = postService.selectPostList(post);
        return getDataTable(list);
    }
    
    @Log(title = "岗位管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:post:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysPost post)
    {
        // 非管理员用户只能导出自己店铺的岗位
        if (!getLoginUser().getUser().isAdmin()) {
            post.setStoreId(getStoreId());
        }
        
        List<SysPost> list = postService.selectPostList(post);
        ExcelUtil<SysPost> util = new ExcelUtil<SysPost>(SysPost.class);
        util.exportExcel(response, list, "岗位数据");
    }

    /**
     * 根据岗位编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:post:query')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable Long postId)
    {
        SysPost post = postService.selectPostById(postId);
        
        // 非管理员用户只能查看自己店铺的岗位
        if (!getLoginUser().getUser().isAdmin() && !getStoreId().equals(post.getStoreId())) {
            return error("您没有权限访问该岗位信息");
        }
        
        return success(post);
    }

    /**
     * 新增岗位
     */
    @PreAuthorize("@ss.hasPermi('system:post:add')")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysPost post)
    {
        // 非管理员用户新增岗位时，强制设置为当前用户所在店铺
        if (!getLoginUser().getUser().isAdmin()) {
            post.setStoreId(getStoreId());
        }
        
        if (!postService.checkPostNameUnique(post))
        {
            return error("新增岗位'" + post.getPostName() + "'失败，岗位名称已存在");
        }
        else if (!postService.checkPostCodeUnique(post))
        {
            return error("新增岗位'" + post.getPostName() + "'失败，岗位编码已存在");
        }
        post.setCreateBy(getUsername());
        return toAjax(postService.insertPost(post));
    }

    /**
     * 修改岗位
     */
    @PreAuthorize("@ss.hasPermi('system:post:edit')")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysPost post)
    {
        // 非管理员用户修改岗位时，强制设置为当前用户所在店铺
        if (!getLoginUser().getUser().isAdmin()) {
            post.setStoreId(getStoreId());
        }
        
        if (!postService.checkPostNameUnique(post))
        {
            return error("修改岗位'" + post.getPostName() + "'失败，岗位名称已存在");
        }
        else if (!postService.checkPostCodeUnique(post))
        {
            return error("修改岗位'" + post.getPostName() + "'失败，岗位编码已存在");
        }
        post.setUpdateBy(getUsername());
        return toAjax(postService.updatePost(post));
    }

    /**
     * 删除岗位
     */
    @PreAuthorize("@ss.hasPermi('system:post:remove')")
    @Log(title = "岗位管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    public AjaxResult remove(@PathVariable Long[] postIds)
    {
        // 检查要删除的岗位是否都属于当前用户所在店铺（非管理员用户）
        if (!getLoginUser().getUser().isAdmin()) {
            for (Long postId : postIds) {
                SysPost post = postService.selectPostById(postId);
                if (post == null || !getStoreId().equals(post.getStoreId())) {
                    return error("您没有权限删除岗位[" + postId + "]");
                }
            }
        }
        
        return toAjax(postService.deletePostByIds(postIds));
    }

    /**
     * 获取岗位选择框列表
     */
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        List<SysPost> posts;
        
        // 非管理员用户只能获取自己店铺的岗位选择列表
        if (!getLoginUser().getUser().isAdmin()) {
            SysPost post = new SysPost();
            post.setStoreId(getStoreId());
            posts = postService.selectPostList(post);
        } else {
            posts = postService.selectPostAll();
        }
        
        return success(posts);
    }

    /**
     * 获取所有岗位列表（不分页）
     */
    @PreAuthorize("@ss.hasPermi('system:post:list')")
    @GetMapping("/listAll")
    public AjaxResult listAllPost(SysPost post)
    {
        // 非管理员用户只能查看自己店铺的岗位
        if (!getLoginUser().getUser().isAdmin()) {
            post.setStoreId(getStoreId());
        }
        
        List<SysPost> posts = postService.selectPostList(post);
        return AjaxResult.success(posts);
    }
}