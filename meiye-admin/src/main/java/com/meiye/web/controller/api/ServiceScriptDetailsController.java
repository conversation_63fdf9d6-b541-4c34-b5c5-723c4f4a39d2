package com.meiye.web.controller.api;

import com.meiye.api.domain.ServiceScriptDetails;
import com.meiye.api.service.ServiceScriptDetailsService;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 标准话术详情Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/service/details")
public class ServiceScriptDetailsController extends BaseController
{
    @Autowired
    private ServiceScriptDetailsService serviceScriptDetailsService;

    /**
     * 查询标准话术详情列表
     */
    @PreAuthorize("@ss.hasPermi('api:details:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServiceScriptDetails serviceScriptDetails)
    {
        startPage();
        List<ServiceScriptDetails> list = serviceScriptDetailsService.selectServiceScriptDetailsList(serviceScriptDetails);
        return getDataTable(list);
    }

    /**
     * 导出标准话术详情列表
     */
    @PreAuthorize("@ss.hasPermi('api:details:export')")
    @Log(title = "标准话术详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceScriptDetails serviceScriptDetails)
    {
        List<ServiceScriptDetails> list = serviceScriptDetailsService.selectServiceScriptDetailsList(serviceScriptDetails);
        ExcelUtil<ServiceScriptDetails> util = new ExcelUtil<ServiceScriptDetails>(ServiceScriptDetails.class);
        util.exportExcel(response, list, "标准话术详情数据");
    }

    /**
     * 获取标准话术详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('api:details:query')")
    @GetMapping(value = "/{serviceScriptId}")
    public AjaxResult getInfo(@PathVariable("serviceScriptId") Long serviceScriptId)
    {
        return success(serviceScriptDetailsService.selectServiceScriptDetailsByServiceScriptId(serviceScriptId));
    }

    /**
     * 新增标准话术详情
     */
    @PreAuthorize("@ss.hasPermi('api:details:add')")
    @Log(title = "标准话术详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServiceScriptDetails serviceScriptDetails)
    {
        return toAjax(serviceScriptDetailsService.insertServiceScriptDetails(serviceScriptDetails));
    }

    /**
     * 修改标准话术详情
     */
    @PreAuthorize("@ss.hasPermi('api:details:edit')")
    @Log(title = "标准话术详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServiceScriptDetails serviceScriptDetails)
    {
        return toAjax(serviceScriptDetailsService.updateServiceScriptDetails(serviceScriptDetails));
    }

    /**
     * 删除标准话术详情
     */
    @PreAuthorize("@ss.hasPermi('api:details:remove')")
    @Log(title = "标准话术详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{serviceScriptIds}")
    public AjaxResult remove(@PathVariable Long[] serviceScriptIds)
    {
        return toAjax(serviceScriptDetailsService.deleteServiceScriptDetailsByServiceScriptIds(serviceScriptIds));
    }
}
