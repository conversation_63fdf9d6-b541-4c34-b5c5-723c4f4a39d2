package com.meiye.web.controller.api;

import com.meiye.api.domain.MemberFeature;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Date;

/**
 * 会员特性Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/member")
public class MemberFeatureController extends BaseController
{
    @Autowired
    private com.meiye.api.service.MemberFeatureService memberFeatureService;

    /**
     * 查询会员特性列表
     */
    @PreAuthorize("@ss.hasPermi('member:feature:list')")
    @GetMapping("/feature/list")
    public TableDataInfo list(MemberFeature memberFeature)
    {
        startPage();
        List<MemberFeature> list = memberFeatureService.selectMemberFeatureList(memberFeature);
        return getDataTable(list);
    }

    /**
     * 导出会员特性列表
     */
    @PreAuthorize("@ss.hasPermi('member:feature:export')")    
    @Log(title = "会员特性", businessType = BusinessType.EXPORT)
    @PostMapping("/feature/export")
    public void export(HttpServletResponse response, MemberFeature memberFeature)
    {
        List<MemberFeature> list = memberFeatureService.selectMemberFeatureList(memberFeature);
        ExcelUtil<MemberFeature> util = new ExcelUtil<MemberFeature>(MemberFeature.class);
        util.exportExcel(response, list, "会员特性数据");
    }

    /**
     * 获取会员特性详细信息
     */
    @PreAuthorize("@ss.hasPermi('member:feature:query')")
    @GetMapping(value = "/feature/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(memberFeatureService.selectMemberFeatureById(id));
    }

    /**
     * 新增会员特性
     */
    @PreAuthorize("@ss.hasPermi('member:feature:add')")
    @Log(title = "会员特性", businessType = BusinessType.INSERT)
    @PostMapping("/feature")
    public AjaxResult add(@RequestBody MemberFeature memberFeature)
    {
        memberFeature.setInputBy(getUserId());
        memberFeature.setInputTime(new Date());
        return toAjax(memberFeatureService.insertMemberFeature(memberFeature));
    }

    /**
     * 添加会员特性信息
     */
    @PreAuthorize("@ss.hasPermi('member:feature:add')")
    @Log(title = "会员特性", businessType = BusinessType.INSERT)
    @PostMapping("/member-feature-add")
    public AjaxResult featureAdd(@RequestBody MemberFeature memberFeature)
    {
        System.out.println("member"+memberFeature);
        memberFeature.setInputBy(getUserId());
        memberFeature.setInputTime(new Date());
        return toAjax(memberFeatureService.insertMemberFeature(memberFeature));
    }

    /**
     * 修改会员特性
     */
    @PreAuthorize("@ss.hasPermi('member:feature:edit')")
    @Log(title = "会员特性", businessType = BusinessType.UPDATE)
    @PutMapping("/feature")
    public AjaxResult edit(@RequestBody MemberFeature memberFeature)
    {
        return toAjax(memberFeatureService.updateMemberFeature(memberFeature));
    }

    /**
     * 删除会员特性
     */
    @PreAuthorize("@ss.hasPermi('member:feature:remove')")
    @Log(title = "会员特性", businessType = BusinessType.DELETE)
	@DeleteMapping("/feature/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(memberFeatureService.deleteMemberFeatureByIds(ids));
    }
}
