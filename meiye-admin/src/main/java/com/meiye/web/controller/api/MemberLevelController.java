package com.meiye.web.controller.api;

import com.meiye.api.domain.MemberLevel;
import com.meiye.api.query.MemberQuery;
import com.meiye.api.vo.MemberVO;
import com.meiye.common.annotation.Anonymous;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 会员等级Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/member/level")
public class MemberLevelController extends BaseController {
    @Autowired
    private com.meiye.api.service.MemberLevelService memberLevelService;

    @PreAuthorize("@ss.hasPermi('member:level:queryMemberLevelList')")
    @Anonymous
    @PostMapping("/queryMemberLevelList")
    public AjaxResult queryMemberLevelList(MemberLevel memberLevel) {
        List<MemberLevel> list = memberLevelService.selectMemberLevelList(memberLevel);
        return success(list);
    }

    /**
     * 查询会员等级列表
     */
    @PreAuthorize("@ss.hasPermi('storeManage:memberLevel:list')")
    @GetMapping("/list")
    public TableDataInfo list(MemberLevel memberLevel) {
        startPage();
        List<MemberLevel> list = memberLevelService.selectMemberLevelList(memberLevel);
        return getDataTable(list);
    }

    /**
     * 查询会员等级列表(不分页，用于下拉框)
     */
    @PreAuthorize("@ss.hasPermi('storeManage:memberLevel:list')")
    @GetMapping("/listAll")
    public AjaxResult listAll(MemberLevel memberLevel)
    {
        List<MemberLevel> list = memberLevelService.selectMemberLevelList(memberLevel);
        return AjaxResult.success(list);
    }

    /**
     * 导出会员等级列表
     */
    @PreAuthorize("@ss.hasPermi('storeManage:memberLevel:export')")
    @Log(title = "会员等级", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MemberLevel memberLevel) {
        List<MemberLevel> list = memberLevelService.selectMemberLevelList(memberLevel);
        ExcelUtil<MemberLevel> util = new ExcelUtil<MemberLevel>(MemberLevel.class);
        util.exportExcel(response, list, "会员等级数据");
    }

    /**
     * 获取会员等级详细信息
     */
    @PreAuthorize("@ss.hasPermi('storeManage:memberLevel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(memberLevelService.selectMemberLevelById(id));
    }

    /**
     * 新增会员等级
     */
    @PreAuthorize("@ss.hasPermi('storeManage:memberLevel:add')")
    @Log(title = "会员等级", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MemberLevel memberLevel)
    {
        try {
            // 自动填充当前登录用户的店铺ID作为主店面ID
            Long storeId = getStoreId();
            System.out.println("新增会员等级 - 获取到的店铺ID: " + storeId);
            System.out.println("新增会员等级 - 传入的memberLevel: " + memberLevel);

            if (storeId != null) {
                memberLevel.setRootStoreId(storeId);
                System.out.println("新增会员等级 - 设置后的rootStoreId: " + memberLevel.getRootStoreId());
            } else {
                System.out.println("新增会员等级 - 警告: 店铺ID为空");
            }

            return toAjax(memberLevelService.insertMemberLevel(memberLevel));
        } catch (Exception e) {
            e.printStackTrace();
            return error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 修改会员等级
     */
    @PreAuthorize("@ss.hasPermi('storeManage:memberLevel:edit')")
    @Log(title = "会员等级", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MemberLevel memberLevel)
    {
        try {
            // 确保修改时也有正确的店铺ID
            if (memberLevel.getRootStoreId() == null) {
                Long storeId = getStoreId();
                if (storeId != null) {
                    memberLevel.setRootStoreId(storeId);
                }
            }

            return toAjax(memberLevelService.updateMemberLevel(memberLevel));
        } catch (Exception e) {
            e.printStackTrace();
            return error("修改失败: " + e.getMessage());
        }
    }
/**
     * 批量更新会员等级
     */
@PreAuthorize("@ss.hasPermi('storeManage:memberLevel:edit')")
@Log(title = "会员等级", businessType = BusinessType.UPDATE)
@PutMapping("/batch")
public AjaxResult batchEdit(@RequestBody List<MemberLevel> memberLevels)
{
    return toAjax(memberLevelService.updateMemberLevels(memberLevels));
}
    /**
     * 删除会员等级
     */
    @PreAuthorize("@ss.hasPermi('storeManage:memberLevel:remove')")
    @Log(title = "会员等级", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(memberLevelService.deleteMemberLevelByIds(ids));
    }


    /**
     * 获取当前登录用户的店铺ID
     */
    @PreAuthorize("@ss.hasPermi('storeManage:memberLevel:list')")
    @GetMapping("/getStoreRootId")
    public AjaxResult getStoreRootId() {
        try {
            Long storeId = getStoreId();
            System.out.println("获取到的店铺ID: " + storeId); // 添加日志
            if (storeId == null) {
                return error("当前用户未绑定店铺");
            }
            return success(storeId);
        } catch (Exception e) {
            e.printStackTrace(); // 添加异常打印
            return error("获取店铺ID失败: " + e.getMessage());
        }
    }

}