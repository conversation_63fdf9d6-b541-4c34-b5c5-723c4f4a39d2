package com.meiye.web.controller.api;

import com.meiye.api.domain.StoreProduct;
import com.meiye.api.query.StoreProductQuery;
import com.meiye.api.service.StoreProductService;
import com.meiye.api.vo.StoreProductVO;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;

@RestController
@RequestMapping("/storeProduct")
public class StoreProductController extends BaseController {
    @Autowired
    StoreProductService storeProductService;

    @PreAuthorize("@ss.hasPermi('storeProduct:storeProduct:list')")
    @PostMapping("/queryStoreProductList")
    public AjaxResult queryStoreProductList(StoreProductQuery storeProductQuery) {
        storeProductQuery.setStoreId(getStoreId());
        List<StoreProductVO> list = storeProductService.queryStoreProductList(storeProductQuery);
        return success(list);
    }

    /**
     * 查询店面实际销售物列表
     */
    @PreAuthorize("@ss.hasPermi('system:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(StoreProduct storeProduct)
    {
        startPage();
        List<StoreProduct> list = storeProductService.selectStoreProductList(storeProduct);
        return getDataTable(list);
    }

    /**
     * 导出店面实际销售物列表
     */
    @PreAuthorize("@ss.hasPermi('system:product:export')")
    @Log(title = "店面实际销售物", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StoreProduct storeProduct)
    {
        List<StoreProduct> list = storeProductService.selectStoreProductList(storeProduct);
        ExcelUtil<StoreProduct> util = new ExcelUtil<StoreProduct>(StoreProduct.class);
        util.exportExcel(response, list, "店面实际销售物数据");
    }

    /**
     * 获取店面实际销售物详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:product:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeProductService.selectStoreProductById(id));
    }

    /**
     * 新增店面实际销售物
     */
    @PreAuthorize("@ss.hasPermi('system:product:add')")
    @Log(title = "店面实际销售物", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StoreProduct storeProduct)
    {
        return toAjax(storeProductService.insertStoreProduct(storeProduct));
    }

    /**
     * 修改店面实际销售物
     */
    @PreAuthorize("@ss.hasPermi('system:product:edit')")
    @Log(title = "店面实际销售物", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StoreProduct storeProduct)
    {
        return toAjax(storeProductService.updateStoreProduct(storeProduct));
    }

    /**
     * 删除店面实际销售物
     */
    @PreAuthorize("@ss.hasPermi('system:product:remove')")
    @Log(title = "店面实际销售物", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(storeProductService.deleteStoreProductByIds(ids));
    }
}
