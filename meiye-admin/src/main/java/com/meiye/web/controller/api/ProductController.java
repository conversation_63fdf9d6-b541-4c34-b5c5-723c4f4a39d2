package com.meiye.web.controller.api;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.query.StoreProductQuery;
import com.meiye.api.service.ConsumptionCardService;
import com.meiye.api.vo.OptionVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.api.domain.Product;
import com.meiye.api.service.ProductService;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 主店面销售物定义Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/system/product")
public class ProductController extends BaseController
{
    private static final Set<String> VALID_TYPES = new HashSet<>(Arrays.asList("card", "service", "type"));

    @Autowired
    private ProductService productService;
    @Autowired
    private ConsumptionCardService consumptionCardService;

    /**
     * 查询主店面销售物定义列表(产品)
     */
    @PreAuthorize("@ss.hasPermi('system:product:list')")
    @GetMapping("/list-product")
    public TableDataInfo listP(Product product)
    {
        startPage();
        List<Product> list = productService.selectProductList(product);
        return getDataTable(list);
    }

    /**
     * 查询主店面销售物定义列表(服务)
     */
    @PreAuthorize("@ss.hasPermi('system:product:list')")
    @GetMapping("/list-service")
    public TableDataInfo listS(Product product)
    {
        startPage();
        List<Product> list = productService.selectServiceList(product);

        return getDataTable(list);
    }

    /**
     * 查询主店面销售物定义列表(卡)
     */
    @PreAuthorize("@ss.hasPermi('system:product:list')")
    @GetMapping("/list-card")
    public TableDataInfo listC(Product product)
    {
        startPage();
        List<Product> list = productService.selectCardList(product);

        return getDataTable(list);
    }

    /**
     * 获取所有产品列表(不分页) - 用于下拉框显示
     */
    @PreAuthorize("@ss.hasPermi('system:product:list')")
    @GetMapping("/list-all-product")
    public AjaxResult listAllProduct(Product product)
    {
        List<Product> list = productService.selectProductList(product);
        return AjaxResult.success(list);
    }

    /**
     * 获取所有服务列表(不分页) - 用于下拉框显示
     */
    @PreAuthorize("@ss.hasPermi('system:product:list')")
    @GetMapping("/list-all-service")
    public AjaxResult listAllService(Product product)
    {
        List<Product> list = productService.selectServiceList(product);
        return AjaxResult.success(list);
    }
    @GetMapping("/option")
    public AjaxResult option(Product product)
    {
// 优化后的判断逻辑
        if (product.getType() != null && !VALID_TYPES.contains(product.getType())) {
            product.setType(null);
        }
        if (getStoreId()==null){
            return AjaxResult.error("用户未绑定店铺");
        }
        product.setRootStoreId(getStoreId());
        List<Product> list = productService.selectProductOption(product);
        return AjaxResult.success(list);
    }

    @GetMapping("/option-sp")
    public AjaxResult optionSp(StoreProductQuery storeProductQuery)
    {
// 优化后的判断逻辑
        if (storeProductQuery.getType() != null && !VALID_TYPES.contains(storeProductQuery.getType())) {
            storeProductQuery.setType(null);
        }
        if (getStoreId()==null){
            return AjaxResult.error("用户未绑定店铺");
        }
        storeProductQuery.setStoreId(getStoreId());
        List<OptionVo> list = productService.selectOptionSp(storeProductQuery);
        return AjaxResult.success(list);
    }

    /**
     * 导出主店面销售物定义列表
     */
    @PreAuthorize("@ss.hasPermi('system:product:export')")
    @Log(title = "主店面销售物定义", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Product product)
    {
        List<Product> list = productService.selectProductList(product);
        ExcelUtil<Product> util = new ExcelUtil<Product>(Product.class);
        util.exportExcel(response, list, "主店面销售物定义数据");
    }

    /**
     * 获取主店面销售物定义详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:product:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(productService.selectProductById(id));
    }

    /**
     * 新增主店面销售物定义
     */
    @PreAuthorize("@ss.hasPermi('system:product:add')")
    @Log(title = "主店面销售物定义", businessType = BusinessType.INSERT)
    @PostMapping("/product")
    public AjaxResult addP(@RequestBody Product product) {
        product.setCreatedByEmployeeId(getUserId());
        Date now = new Date();
        product.setCreatedAt(now);

        int result = productService.insertProduct(product);

        if (result > 0) {
            AjaxResult response = AjaxResult.success("操作成功", product);
            return response;
        } else {
            return AjaxResult.error("新增失败");
        }
    }

    /**
     * 新增主店面销售物定义
     */
    @PreAuthorize("@ss.hasPermi('system:product:add')")
    @Log(title = "主店面销售物定义", businessType = BusinessType.INSERT)
    @PostMapping("/serve")
    public AjaxResult addS(@RequestBody Product product)
    {
        product.setCreatedByEmployeeId(getUserId());
        Date now = new Date();
        product.setCreatedAt(now);

        int result = productService.insertService(product);

        if (result > 0) {
            AjaxResult response = AjaxResult.success("操作成功", product);
            return response;
        } else {
            return AjaxResult.error("新增失败");
        }
    }

    /**
     * 新增主店面销售物定义
     */
    @PreAuthorize("@ss.hasPermi('system:product:add')")
    @Log(title = "主店面销售物定义", businessType = BusinessType.INSERT)
    @PostMapping("/card")
    public AjaxResult addC(@RequestBody Product product) {
        product.setCreatedByEmployeeId(getUserId());
        Date now = new Date();
        product.setCreatedAt(now);

        int result = productService.insertCard(product);

        if (result > 0) {
            return AjaxResult.success("操作成功", product);
        } else {
            return AjaxResult.error("新增失败");
        }
    }

    /**
     * 修改主店面销售物定义
     */
    @PreAuthorize("@ss.hasPermi('system:product:edit')")
    @Log(title = "主店面销售物定义", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Product product)
    {
        return toAjax(productService.updateProduct(product));
    }

    /**
     * 删除主店面销售物定义
     */
    @PreAuthorize("@ss.hasPermi('system:product:remove')")
    @Log(title = "主店面销售物定义", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(productService.deleteProductById(id));
    }
}