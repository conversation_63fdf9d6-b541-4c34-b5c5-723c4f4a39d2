package com.meiye.kpi.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.KpiSetting;
import com.meiye.api.service.KpiSettingService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * KPI设置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
@RequestMapping("/kpi/setting")
public class KpiSettingController extends BaseController
{
    @Autowired
    private KpiSettingService kpiSettingService;

    /**
     * 查询KPI设置列表
     */
    @PreAuthorize("@ss.hasPermi('kpi:setting:list')")
    @GetMapping("/list")
    public TableDataInfo list(KpiSetting kpiSetting)
    {
        startPage();
        List<KpiSetting> list = kpiSettingService.selectKpiSettingList(kpiSetting);
        return getDataTable(list);
    }

    /**
     * 导出KPI设置列表
     */
    @PreAuthorize("@ss.hasPermi('kpi:setting:export')")
    @Log(title = "KPI设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KpiSetting kpiSetting)
    {
        List<KpiSetting> list = kpiSettingService.selectKpiSettingList(kpiSetting);
        ExcelUtil<KpiSetting> util = new ExcelUtil<KpiSetting>(KpiSetting.class);
        util.exportExcel(response, list, "KPI设置数据");
    }

    /**
     * 获取KPI设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('kpi:setting:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(kpiSettingService.selectKpiSettingById(id));
    }

    /**
     * 新增KPI设置
     */
    @PreAuthorize("@ss.hasPermi('kpi:setting:add')")
    @Log(title = "KPI设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KpiSetting kpiSetting)
    {
        return toAjax(kpiSettingService.insertKpiSetting(kpiSetting));
    }

    /**
     * 修改KPI设置
     */
    @PreAuthorize("@ss.hasPermi('kpi:setting:edit')")
    @Log(title = "KPI设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KpiSetting kpiSetting)
    {
        return toAjax(kpiSettingService.updateKpiSetting(kpiSetting));
    }

    /**
     * 删除KPI设置
     */
    @PreAuthorize("@ss.hasPermi('kpi:setting:remove')")
    @Log(title = "KPI设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kpiSettingService.deleteKpiSettingByIds(ids));
    }
}
