package com.meiye.evaluation.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.ServiceEvaluationGroup;
import com.meiye.api.service.ServiceEvaluationGroupService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 服务评价维度分组Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/evaluation/group")
public class ServiceEvaluationGroupController extends BaseController
{
    @Autowired
    private ServiceEvaluationGroupService serviceEvaluationGroupService;

    /**
     * 查询服务评价维度分组列表
     */
    @PreAuthorize("@ss.hasPermi('evaluation:group:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServiceEvaluationGroup serviceEvaluationGroup)
    {
        startPage();
        List<ServiceEvaluationGroup> list = serviceEvaluationGroupService.selectServiceEvaluationGroupList(serviceEvaluationGroup);
        return getDataTable(list);
    }

    /**
     * 导出服务评价维度分组列表
     */
    @PreAuthorize("@ss.hasPermi('evaluation:group:export')")
    @Log(title = "服务评价维度分组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceEvaluationGroup serviceEvaluationGroup)
    {
        List<ServiceEvaluationGroup> list = serviceEvaluationGroupService.selectServiceEvaluationGroupList(serviceEvaluationGroup);
        ExcelUtil<ServiceEvaluationGroup> util = new ExcelUtil<ServiceEvaluationGroup>(ServiceEvaluationGroup.class);
        util.exportExcel(response, list, "服务评价维度分组数据");
    }

    /**
     * 获取服务评价维度分组详细信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:group:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(serviceEvaluationGroupService.selectServiceEvaluationGroupById(id));
    }

    /**
     * 新增服务评价维度分组
     */
    @PreAuthorize("@ss.hasPermi('evaluation:group:add')")
    @Log(title = "服务评价维度分组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServiceEvaluationGroup serviceEvaluationGroup)
    {
        return toAjax(serviceEvaluationGroupService.insertServiceEvaluationGroup(serviceEvaluationGroup));
    }

    /**
     * 修改服务评价维度分组
     */
    @PreAuthorize("@ss.hasPermi('evaluation:group:edit')")
    @Log(title = "服务评价维度分组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServiceEvaluationGroup serviceEvaluationGroup)
    {
        return toAjax(serviceEvaluationGroupService.updateServiceEvaluationGroup(serviceEvaluationGroup));
    }

    /**
     * 删除服务评价维度分组
     */
    @PreAuthorize("@ss.hasPermi('evaluation:group:remove')")
    @Log(title = "服务评价维度分组", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(serviceEvaluationGroupService.deleteServiceEvaluationGroupByIds(ids));
    }
}
