package com.meiye.service.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.ServiceRecord;
import com.meiye.api.service.ServiceRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 服务记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@RestController
@RequestMapping("/service/servicerecord")
public class ServiceRecordController extends BaseController
{
    @Autowired
    private ServiceRecordService serviceRecordService;

    /**
     * 查询服务记录列表
     */
    @PreAuthorize("@ss.hasPermi('service:servicerecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServiceRecord serviceRecord)
    {
        startPage();
        List<ServiceRecord> list = serviceRecordService.selectServiceRecordList(serviceRecord);
        return getDataTable(list);
    }

    /**
     * 导出服务记录列表
     */
    @PreAuthorize("@ss.hasPermi('service:servicerecord:export')")
    @Log(title = "服务记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceRecord serviceRecord)
    {
        List<ServiceRecord> list = serviceRecordService.selectServiceRecordList(serviceRecord);
        ExcelUtil<ServiceRecord> util = new ExcelUtil<ServiceRecord>(ServiceRecord.class);
        util.exportExcel(response, list, "服务记录数据");
    }

    /**
     * 获取服务记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('service:servicerecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(serviceRecordService.selectServiceRecordById(id));
    }

    /**
     * 新增服务记录
     */
    @PreAuthorize("@ss.hasPermi('service:servicerecord:add')")
    @Log(title = "服务记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServiceRecord serviceRecord)
    {
        return toAjax(serviceRecordService.insertServiceRecord(serviceRecord));
    }

    /**
     * 修改服务记录
     */
    @PreAuthorize("@ss.hasPermi('service:servicerecord:edit')")
    @Log(title = "服务记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServiceRecord serviceRecord)
    {
        return toAjax(serviceRecordService.updateServiceRecord(serviceRecord));
    }

    /**
     * 删除服务记录
     */
    @PreAuthorize("@ss.hasPermi('service:servicerecord:remove')")
    @Log(title = "服务记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(serviceRecordService.deleteServiceRecordByIds(ids));
    }
}
