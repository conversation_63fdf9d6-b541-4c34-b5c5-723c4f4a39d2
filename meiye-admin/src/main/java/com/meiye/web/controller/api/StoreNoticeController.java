package com.meiye.web.controller.api;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.meiye.api.domain.Store;
import com.meiye.api.domain.StoreNotice;
import com.meiye.api.domain.StoreNoticeItem;
import com.meiye.api.service.IStoreService;
import com.meiye.api.service.StoreNoticeItemService;
import com.meiye.api.service.StoreNoticeService;
import com.meiye.api.vo.EmployeeInfoVo;
import com.meiye.api.vo.StoreNoticeVo;
import com.meiye.common.core.domain.entity.SysUser;
import com.meiye.common.core.page.PageDomain;
import com.meiye.system.mapper.SysUserMapper;
import org.apache.catalina.User;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 店铺公告Controller
 * 
 * <AUTHOR>
 * @date 2025-08-10
 */
@RestController
@RequestMapping("/store/notice")
public class StoreNoticeController extends BaseController
{
    @Autowired
    private StoreNoticeService storeNoticeService;
    @Autowired SysUserMapper sysUserMapper;

    @Autowired
    private StoreNoticeItemService storeNoticeItemService;

    @Autowired
    private IStoreService storeService;
    /**
     * 查询店铺公告列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @GetMapping("/to-employee-api")
    public TableDataInfo toEmployeeApi(StoreNotice storeNotice)
    {
        startPage();
        List<StoreNoticeVo> list = storeNoticeService.selectStoreNoticeVoList(storeNotice);
        return getDataTable(list);
    }

    /**
     * 查询店铺公告列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(StoreNotice storeNotice)
    {
        startPage();
        List<StoreNotice> list = storeNoticeService.selectStoreNoticeList(storeNotice);
        return getDataTable(list);
    }

    /**
     * 根据店铺公告ID查询接收详情
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @GetMapping("/receivers/{storeNoticeId}")
    public AjaxResult listReceiversByStoreNoticeId(@PathVariable Long storeNoticeId) {
        StoreNoticeItem storeNoticeItem = new StoreNoticeItem();
        storeNoticeItem.setStoreNoticeId(storeNoticeId);
        List<StoreNoticeItem> list = storeNoticeItemService.selectStoreNoticeItemList(storeNoticeItem);
        return success(list);
    }

    /**
     * 导出店铺公告列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:export')")
    @Log(title = "店铺公告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StoreNotice storeNotice)
    {
        List<StoreNotice> list = storeNoticeService.selectStoreNoticeList(storeNotice);
        ExcelUtil<StoreNotice> util = new ExcelUtil<StoreNotice>(StoreNotice.class);
        util.exportExcel(response, list, "店铺公告数据");
    }

    /**
     * 获取店铺公告详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:notice:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeNoticeService.selectStoreNoticeById(id));
    }

    /**
     * 新增店铺公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:add')")
    @Log(title = "店铺公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StoreNotice storeNotice)

    {
        storeNotice.setStoreId(getStoreId());
        storeNotice.setCreatedAt(new Date());
        storeNotice.setCreatedBy(String.valueOf(getUserId()));
        return toAjax(storeNoticeService.insertStoreNotice(storeNotice));
    }

    /**
     * 修改店铺公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:edit')")
    @Log(title = "店铺公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StoreNotice storeNotice)
    {
        return toAjax(storeNoticeService.updateStoreNotice(storeNotice));
    }

    /**
     * 删除店铺公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:remove')")
    @Log(title = "店铺公告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(storeNoticeService.deleteStoreNoticeByIds(ids));
    }

    /**
     * 查询员工列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @GetMapping("/users/listAllUsers")
    public TableDataInfo listAllUsers(SysUser user)
    {
        List<SysUser> list = sysUserMapper.selectUserList(user);
        return getDataTable(list);
    }

}
