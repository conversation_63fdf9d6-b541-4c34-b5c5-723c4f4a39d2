package com.meiye.web.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.meiye.api.domain.ServiceScript;
import com.meiye.api.domain.ServiceScriptItem;
import com.meiye.api.service.ServiceScriptItemService;
import com.meiye.api.service.ServiceScriptService;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.domain.entity.SysRole;
import com.meiye.common.core.domain.model.LoginUser;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.SecurityUtils;
import com.meiye.common.utils.poi.ExcelUtil;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 标准话术Controller
 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/service/script")
public class ServiceScriptController extends BaseController
{
    @Autowired
    private ServiceScriptService serviceScriptService;
    @Autowired
    private ServiceScriptItemService serviceScriptItemService;

    @GetMapping("/standard-script-list")
    public TableDataInfo standardScriptList(ServiceScript serviceScript) {
        startPage();

        QueryWrapper<ServiceScript> queryWrapper = new QueryWrapper<>();

        // 获取用户角色
        List<SysRole> roles = getLoginUser().getUser().getRoles();

        // 根据不同角色构建查询条件
        if (roles != null && !roles.isEmpty()) {
            for (SysRole role : roles) {
                String roleKey = role.getRoleKey();
                if ("Operator".equals(roleKey)) {
                    // Operator角色：只能查看公共数据（root_store_id为空）
                    queryWrapper.and(wrapper -> wrapper.isNull("root_store_id"));
                    break;
                } else if ("MainStoreManager".equals(roleKey)) {
                    // MainStoreManager角色：查看主店数据和公共数据
                    Long rootStoreId = getLoginUser().getUser().getRootStoreId();
                    queryWrapper.and(wrapper -> wrapper.eq("root_store_id", rootStoreId)
                            .or()
                            .isNull("root_store_id"));
                    break;
                } else if ("SubStoreManager".equals(roleKey)) {
                    // SubStoreManager角色：查看分店数据和公共数据
                    Long rootStoreId = getLoginUser().getUser().getRootStoreId();
                    Long storeId = getLoginUser().getUser().getStoreId();
                    queryWrapper.and(wrapper -> wrapper.eq("root_store_id", rootStoreId)
                            .or()
                            .eq("store_id", storeId)
                            .or()
                            .isNull("root_store_id"));
                    break;
                }
            }
        }

        // 添加标题搜索条件
        if (serviceScript.getTitle() != null && !serviceScript.getTitle().trim().isEmpty()) {
            queryWrapper.like("title", serviceScript.getTitle().trim());
        }

        List<ServiceScript> list = serviceScriptService.list(queryWrapper);
        return getDataTable(list);
    }

    @GetMapping("/standard-script-item-list")
    public TableDataInfo standardScriptItemList(ServiceScriptItem serviceScriptItem) {
        startPage();

        QueryWrapper<ServiceScriptItem> queryWrapper = new QueryWrapper<>();

        // 获取用户角色
        List<SysRole> roles = getLoginUser().getUser().getRoles();

        // 根据不同角色构建查询条件
        if (roles != null && !roles.isEmpty()) {
            for (SysRole role : roles) {
                String roleKey = role.getRoleKey();
                if ("Operator".equals(roleKey)) {
                    // Operator角色：只能查看公共数据（root_store_id为空）
                    queryWrapper.and(wrapper -> wrapper.isNull("root_store_id"));
                    break;
                } else if ("MainStoreManager".equals(roleKey)) {
                    // MainStoreManager角色：查看主店数据和公共数据
                    Long rootStoreId = getLoginUser().getUser().getRootStoreId();
                    queryWrapper.and(wrapper -> wrapper.eq("root_store_id", rootStoreId)
                            .or()
                            .isNull("root_store_id"));
                    break;
                } else if ("SubStoreManager".equals(roleKey)) {
                    // SubStoreManager角色：查看分店数据和公共数据
                    Long rootStoreId = getLoginUser().getUser().getRootStoreId();
                    Long storeId = getLoginUser().getUser().getStoreId();
                    queryWrapper.and(wrapper -> wrapper.eq("root_store_id", rootStoreId)
                            .or()
                            .eq("store_id", storeId)
                            .or()
                            .isNull("root_store_id"));
                    break;
                }
            }
        }

        // 添加标题搜索条件
        if (serviceScriptItem.getTitle() != null && !serviceScriptItem.getTitle().trim().isEmpty()) {
            queryWrapper.like("title", serviceScriptItem.getTitle().trim());
        }
        if (serviceScriptItem.getTarget() != null && !serviceScriptItem.getTarget().trim().isEmpty()) {
            queryWrapper.like("target", serviceScriptItem.getTarget().trim());
        }

        List<ServiceScriptItem> list = serviceScriptItemService.list(queryWrapper);
        return getDataTable(list);
    }
    /**
     * 查询标准话术列表
     */
    @PreAuthorize("@ss.hasPermi('service:script:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServiceScript serviceScript)
    {
        startPage();
        List<ServiceScript> list = serviceScriptService.selectServiceScriptList(serviceScript);
        return getDataTable(list);
    }

    /**
     * 导出标准话术列表
     */
    @PreAuthorize("@ss.hasPermi('service:script:export')")
    @Log(title = "标准话术", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceScript serviceScript)
    {
        List<ServiceScript> list = serviceScriptService.selectServiceScriptList(serviceScript);
        ExcelUtil<ServiceScript> util = new ExcelUtil<ServiceScript>(ServiceScript.class);
        util.exportExcel(response, list, "标准话术数据");
    }

    /**
     * 获取标准话术详细信息
     */
    @PreAuthorize("@ss.hasPermi('service:script:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        if (id == null || id <= 0) {
            return AjaxResult.error("无效的ID参数");
        }
        
        ServiceScript script = serviceScriptService.selectServiceScriptById(id);
        if (script == null) {
            return AjaxResult.error("未找到对应的话术记录");
        }
        
        return AjaxResult.success(script);
    }

    /**
     * 新增标准话术
     */
    @PreAuthorize("@ss.hasPermi('service:script:add')")
    @Log(title = "标准话术", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServiceScript serviceScript)
    {
        serviceScript.setCreatedBy( getUserId());
        return toAjax(serviceScriptService.insertServiceScript(serviceScript));
    }

    /**
     * 修改标准话术
     */
    @PreAuthorize("@ss.hasPermi('service:script:edit')")
    @Log(title = "标准话术", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServiceScript serviceScript)
    {
        return toAjax(serviceScriptService.updateServiceScript(serviceScript));
    }

    /**
     * 删除标准话术
     */
    @PreAuthorize("@ss.hasPermi('service:script:remove')")
    @Log(title = "标准话术", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(serviceScriptService.deleteServiceScriptByIds(ids));
    }

    /**
     * 更新话术状态
     */
    @PreAuthorize("@ss.hasPermi('service:script:edit')")
    @Log(title = "标准话术", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody Map<String, Object> params) {
        Long id = Long.valueOf(params.get("id").toString());
        String status = params.get("status").toString();
        
        ServiceScript serviceScript = new ServiceScript();
        serviceScript.setId(id);
        serviceScript.setStatus(status);
        
        return toAjax(serviceScriptService.updateServiceScript(serviceScript));
    }



        /**
     * 获取主店面ID
     */
    @PreAuthorize("@ss.hasPermi('service:script:list')")
    @GetMapping("/getStoreRootId")
    public AjaxResult getStoreRootId() {
        try {
            Long storeId = getStoreId();
            if (storeId == null) {
                return error("当前用户未绑定店铺");
            }
            return success(storeId);
        } catch (Exception e) {
            return error("获取店铺ID失败: " + e.getMessage());
        }
    }
    }

