package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;


import com.meiye.api.domain.KpiResult;
import com.meiye.api.service.KpiResultService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * KPI结果Controller
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
@RequestMapping("/kpi/result")
public class KpiResultController extends BaseController
{
    @Autowired
    private KpiResultService kpiResultService;

    /**
     * 查询KPI结果列表
     */
    @PreAuthorize("@ss.hasPermi('kpi:result:list')")
    @GetMapping("/list")
    public TableDataInfo list(KpiResult kpiResult)
    {
        startPage();
        List<KpiResult> list = kpiResultService.selectKpiResultList(kpiResult);
        return getDataTable(list);
    }

    /**
     * 导出KPI结果列表
     */
    @PreAuthorize("@ss.hasPermi('kpi:result:export')")
    @Log(title = "KPI结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KpiResult kpiResult)
    {
        List<KpiResult> list = kpiResultService.selectKpiResultList(kpiResult);
        ExcelUtil<KpiResult> util = new ExcelUtil<KpiResult>(KpiResult.class);
        util.exportExcel(response, list, "KPI结果数据");
    }

    /**
     * 获取KPI结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('kpi:result:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(kpiResultService.selectKpiResultById(id));
    }

    /**
     * 新增KPI结果
     */
    @PreAuthorize("@ss.hasPermi('kpi:result:add')")
    @Log(title = "KPI结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KpiResult kpiResult)
    {
        return toAjax(kpiResultService.insertKpiResult(kpiResult));
    }

    /**
     * 修改KPI结果
     */
    @PreAuthorize("@ss.hasPermi('kpi:result:edit')")
    @Log(title = "KPI结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KpiResult kpiResult)
    {
        return toAjax(kpiResultService.updateKpiResult(kpiResult));
    }

    /**
     * 删除KPI结果
     */
    @PreAuthorize("@ss.hasPermi('kpi:result:remove')")
    @Log(title = "KPI结果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(kpiResultService.deleteKpiResultByIds(ids));
    }
}
