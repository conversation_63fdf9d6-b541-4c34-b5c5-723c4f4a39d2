package com.meiye.web.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.meiye.api.domain.Product;
import com.meiye.api.domain.ProductCategory;
import com.meiye.api.domain.Store;
import com.meiye.api.service.ProductCategoryService;
import com.meiye.api.service.ProductService;
import com.meiye.common.annotation.DataScope;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.uuid.IdUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.meiye.common.core.controller.BaseController;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


@RestController
@RequestMapping("/product")
public class ProductCategoryController extends BaseController{
    @Autowired
    private ProductCategoryService productCategoryService;
    @Autowired
    private ProductService productService;
    private static final Set<String> VALID_TYPES = new HashSet<>(Arrays.asList("card", "service", "goods"));


    /**
     * 查询销售物分类列表
     */
    //@PreAuthorize("@ss.hasPermi('*:*:*')"
    @GetMapping("/category-list-api")
    public TableDataInfo listApi(ProductCategory productCategory)
    {
        startPage();
        List<ProductCategory> list = productCategoryService.selectProductCategoryList(productCategory);
        return getDataTable(list);
    }

    /**
     * 查询主店面销售物分类列表
     */
    @GetMapping("/root-product-info-list")
    public AjaxResult rootList(ProductCategory productCategory)
    {
        return success(productCategoryService.selectProductCategoryList(productCategory));
    }

    /**
     * 查询主店面销售物分类列表
     */
    @GetMapping("/product-info-list")
    public AjaxResult storeList(Product product)
    {
        return success(productService.selectProductList(product));
    }

    /**
     * 查询销售物分类列表
     */
    @GetMapping("/category-list")
    public AjaxResult list(ProductCategory productCategory)
    {
        return success(productCategoryService.selectProductCategoryList(productCategory));
    }

    /**
     * 新增销售物分类
     */
    @PostMapping
    public AjaxResult add(@RequestBody ProductCategory productCategory)
    {
        try {
            return toAjax(productCategoryService.insertProductCategory(productCategory));
        } catch (DuplicateKeyException e) {
            // 捕获唯一性约束异常，返回具体错误消息
            return AjaxResult.error("主店面ID相同，分类名称不能重复");
        }
    }

    /**
     * 修改销售物分类
     */
    @PutMapping
    public AjaxResult edit(@RequestBody ProductCategory productCategory)
    {
        try {
            return toAjax(productCategoryService.updateProductCategoryList(productCategory));
        } catch (DuplicateKeyException e) {
            // 捕获唯一性约束异常，返回具体错误消息
            return AjaxResult.error("主店面ID相同，分类名称不能重复");
        }
    }

    /**
     * 获取销售物分类详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(productCategoryService.selectProductCategoryById(id));
    }

    /**
     * 删除销售物分类
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(productCategoryService.deleteProductCategoryByIds(ids));
    }


    @GetMapping("/category/childOption")
    public AjaxResult childOption(String type)
    {
        if (type != null && !VALID_TYPES.contains(type)) {
            type = null;
        }
        QueryWrapper<ProductCategory> queryWrapper = new QueryWrapper<>();
        Long storeId = getStoreId();
        Long rootStoreId = getLoginUser().getUser().getRootStoreId();
        if (rootStoreId==null&& storeId!=null) {
            queryWrapper.eq("root_store_id", storeId);
        } else if (rootStoreId!=null) {
            queryWrapper.eq("root_store_id", rootStoreId);
        }
        if (type != null){
            queryWrapper.eq("type", type);
        }

        queryWrapper.eq("status", "0");

        List<ProductCategory> list = productCategoryService.list(queryWrapper);
        // 构建树状结构
        List<ProductCategory> tree = ProductCategory.buildTree(list);
        return success(tree);
    }

    @GetMapping("/category/childOptionAll")
    public AjaxResult childOptionAll(String type)
    {
        if (type != null && !VALID_TYPES.contains(type)) {
            type = null;
        }
        QueryWrapper<ProductCategory> queryWrapper = new QueryWrapper<>();
        Long storeId = getStoreId();
        Long rootStoreId = getLoginUser().getUser().getRootStoreId();
        if (rootStoreId==null&& storeId!=null) {
            queryWrapper.eq("root_store_id", storeId);
        } else if (rootStoreId!=null) {
            queryWrapper.eq("root_store_id", rootStoreId);
        }
        if (type != null){
            queryWrapper.eq("type", type);
        }

        List<ProductCategory> list = productCategoryService.list(queryWrapper);
        // 构建树状结构
        List<ProductCategory> tree = ProductCategory.buildTree(list);
        return success(tree);
    }
}