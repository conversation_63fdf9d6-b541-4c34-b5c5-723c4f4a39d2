package com.meiye.web.controller.api;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageInfo;
import com.meiye.api.domain.EmployeeInfo;
import com.meiye.api.domain.StoreNotice;
import com.meiye.api.mapper.AttendanceMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.api.domain.Attendance;
import com.meiye.api.service.AttendanceService;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 出勤Controller
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequestMapping("/user")
public class AttendanceController extends BaseController
{
    @Autowired
    private AttendanceService attendanceService;
    @Autowired
    private AttendanceMapper attendanceMapper;

    /**
     * 查询出勤列表
     */
    @PreAuthorize("@ss.hasPermi('system:attendance:list')")
    @GetMapping("/list")
    public TableDataInfo list(Attendance attendance)
    {
        startPage();
        List<Attendance> list = attendanceService.selectAttendanceList(attendance);

        return getDataTable(list);
    }

    /**
     * 导出出勤列表
     */
    @PreAuthorize("@ss.hasPermi('system:attendance:export')")
    @Log(title = "出勤", businessType = BusinessType.EXPORT)
    @PostMapping("/attendance/export")
    public void export(HttpServletResponse response, Attendance attendance)
    {
        List<Attendance> list = attendanceService.selectAttendanceList(attendance);
        ExcelUtil<Attendance> util = new ExcelUtil<Attendance>(Attendance.class);
        util.exportExcel(response, list, "出勤数据");
    }

    /**
     * 获取出勤详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:attendance:query')")
    @GetMapping(value = "/attendance/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(attendanceService.selectAttendanceById(id));
    }

    /**
     * 登录员工考勤记录查询
     */
    @GetMapping(value = "/owner-checking-in-list")
    public AjaxResult getCurrent(Attendance attendance)
    {
        // 启动分页
        startPage();

        // 查询数据
        List<Attendance> list = attendanceService.selectAttendanceList(attendance);

        // 获取分页信息
        PageInfo<Attendance> pageInfo = new PageInfo<>(list);

        // 构建返回数据
        Map<String, Object> data = new HashMap<>();
        data.put("rows", list);
        data.put("page", pageInfo.getPageNum());
        data.put("total", pageInfo.getTotal());
        attendance.setEmployeeId(getUserId());
        return success(data);
    }

    /**
     * 新增出勤
     */
    @PreAuthorize("@ss.hasPermi('system:attendance:add')")
    @Log(title = "出勤", businessType = BusinessType.INSERT)
    @PostMapping(value = "/own-checking-in-add")
    public AjaxResult add(@RequestBody Attendance attendance)
    {
        int updateResult = attendanceService.insertAttendance(attendance);

        if (updateResult > 0) {
            // 更新成功，重新查询完整的员工信息
            Date date=new Date();
            Attendance newAttendance = attendanceMapper.selectAttendanceByUserAndDate(getUserId(),date);

            // 返回完整的员工信息
            return AjaxResult.success(newAttendance);
        } else {
            // 更新失败
            return AjaxResult.error("更新失败");
        }
    }

    /**
     * 登录员工打卡
     */
    @Log(title = "出勤", businessType = BusinessType.INSERT)
    @PostMapping(value = "/checking-in-add")
    public AjaxResult addApp(@RequestBody Attendance attendance)
    {
        return toAjax(attendanceService.insertAttendance(attendance));
    }

    /**
     * 修改出勤
     */
    @PreAuthorize("@ss.hasPermi('system:attendance:edit')")
    @Log(title = "出勤", businessType = BusinessType.UPDATE)
    @PutMapping("/checking-in-alter")
    public AjaxResult edit(@RequestBody Attendance attendance)
    {
        return toAjax(attendanceService.updateAttendance(attendance));
    }

    /**
     * 删除出勤
     */
    @PreAuthorize("@ss.hasPermi('system:attendance:remove')")
    @Log(title = "出勤", businessType = BusinessType.DELETE)
    @DeleteMapping("/delchecking/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(attendanceService.deleteAttendanceById(id));
    }
}
