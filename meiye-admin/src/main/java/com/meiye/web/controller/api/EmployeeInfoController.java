package com.meiye.web.controller.api;

import java.util.List;
import javax.management.Query;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.meiye.api.domain.EmployeeInfo;
import com.meiye.api.service.EmployeeInfoService;
import com.meiye.api.vo.EmployeeCommissionOptionVO;
import com.meiye.api.vo.EmployeeInfoVo;
import com.meiye.common.core.domain.entity.SysUser;
import com.meiye.common.utils.SecurityUtils;
import com.meiye.common.utils.StringUtils;
import com.meiye.system.service.ISysUserService;
import com.meiye.system.service.impl.SysUserServiceImpl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 员工扩展信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@RestController
@RequestMapping("/employeeInfo")
public class EmployeeInfoController extends BaseController
{
    @Autowired
    private EmployeeInfoService employeeInfoService;
    @Autowired
    private ISysUserService userService;

    /**
     * 查询员工扩展信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmployeeInfoVo employeeInfo)
    {
        startPage();
        employeeInfo.setRootStoreId(getLoginUser().getUser().getRootStoreId());
        employeeInfo.setIsManager(false);
        List<EmployeeInfoVo> list = employeeInfoService.selectEmployeeVoList(employeeInfo);
        return getDataTable(list);
    }


    @GetMapping("/employee-commission-option")
    public AjaxResult employeeCommissionOption()
    {
        List<EmployeeCommissionOptionVO> result = employeeInfoService.selectECOptions(getStoreId());
        return success(result);
    }

    /**
     * 获取员工扩展信息详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(employeeInfoService.selectEmployeeInfoVoById(id));
    }

    /**
     * 导出员工扩展信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "员工扩展信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmployeeInfo employeeInfo)
    {
        List<EmployeeInfo> list = employeeInfoService.selectEmployeeInfoList(employeeInfo);
        ExcelUtil<EmployeeInfo> util = new ExcelUtil<EmployeeInfo>(EmployeeInfo.class);
        util.exportExcel(response, list, "员工扩展信息数据");
    }



    /**
     * 新增员工扩展信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "员工扩展信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmployeeInfo employeeInfo)
    {
        return toAjax(employeeInfoService.insertEmployeeInfo(employeeInfo));
    }

    /**
     * 修改员工扩展信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "员工扩展信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUser user)
    {
        if (!userService.checkUserNameUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
        {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateEmployee(user));
    }

    /**
     * 新增员工
     */

    //@Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody SysUser user)
    {

        if (!userService.checkUserNameUnique(user))
        {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
        {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
        {
            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertEmployee(user));
    }

    /**
     * 删除员工扩展信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:remove')")
    @Log(title = "员工扩展信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(employeeInfoService.deleteEmployeeInfoByIds(ids));
    }
}
