package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.api.domain.RecordingDevice;
import com.meiye.api.service.RecordingDeviceService;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 录音设备Controller
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("/system/device")
public class RecordingDeviceController extends BaseController
{
    @Autowired
    private RecordingDeviceService recordingDeviceService;

    /**
     * 查询录音设备列表
     */
    @PreAuthorize("@ss.hasPermi('system:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecordingDevice recordingDevice)
    {
        startPage();
        List<RecordingDevice> list = recordingDeviceService.selectRecordingDeviceList(recordingDevice);
        return getDataTable(list);
    }

    /**
     * 导出录音设备列表
     */
    @PreAuthorize("@ss.hasPermi('system:device:export')")
    @Log(title = "录音设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecordingDevice recordingDevice)
    {
        List<RecordingDevice> list = recordingDeviceService.selectRecordingDeviceList(recordingDevice);
        ExcelUtil<RecordingDevice> util = new ExcelUtil<RecordingDevice>(RecordingDevice.class);
        util.exportExcel(response, list, "录音设备数据");
    }

    /**
     * 获取录音设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:device:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recordingDeviceService.selectRecordingDeviceById(id));
    }

    /**
     * 新增录音设备
     */
    @PreAuthorize("@ss.hasPermi('system:device:add')")
    @Log(title = "录音设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecordingDevice recordingDevice)
    {
        return toAjax(recordingDeviceService.insertRecordingDevice(recordingDevice));
    }

    /**
     * 修改录音设备
     */
    @PreAuthorize("@ss.hasPermi('system:device:edit')")
    @Log(title = "录音设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecordingDevice recordingDevice)
    {
        return toAjax(recordingDeviceService.updateRecordingDevice(recordingDevice));
    }

    /**
     * 删除录音设备
     */
    @PreAuthorize("@ss.hasPermi('system:device:remove')")
    @Log(title = "录音设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recordingDeviceService.deleteRecordingDeviceByIds(ids));
    }
}
