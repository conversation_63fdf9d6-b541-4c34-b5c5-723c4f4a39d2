package com.meiye.service.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.ServiceRecordItem;
import com.meiye.api.service.ServiceRecordItemService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 服务记录的话术单元信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@RestController
@RequestMapping("/service/serviceitem")
public class ServiceRecordItemController extends BaseController
{
    @Autowired
    private ServiceRecordItemService serviceRecordItemService;

    /**
     * 查询服务记录的话术单元信息列表
     */
    @PreAuthorize("@ss.hasPermi('service:serviceitem:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServiceRecordItem serviceRecordItem)
    {
        startPage();
        List<ServiceRecordItem> list = serviceRecordItemService.selectServiceRecordItemList(serviceRecordItem);
        return getDataTable(list);
    }

    /**
     * 导出服务记录的话术单元信息列表
     */
    @PreAuthorize("@ss.hasPermi('service:serviceitem:export')")
    @Log(title = "服务记录的话术单元信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceRecordItem serviceRecordItem)
    {
        List<ServiceRecordItem> list = serviceRecordItemService.selectServiceRecordItemList(serviceRecordItem);
        ExcelUtil<ServiceRecordItem> util = new ExcelUtil<ServiceRecordItem>(ServiceRecordItem.class);
        util.exportExcel(response, list, "服务记录的话术单元信息数据");
    }

    /**
     * 获取服务记录的话术单元信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('service:serviceitem:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(serviceRecordItemService.selectServiceRecordItemById(id));
    }

    /**
     * 新增服务记录的话术单元信息
     */
    @PreAuthorize("@ss.hasPermi('service:serviceitem:add')")
    @Log(title = "服务记录的话术单元信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServiceRecordItem serviceRecordItem)
    {
        return toAjax(serviceRecordItemService.insertServiceRecordItem(serviceRecordItem));
    }

    /**
     * 修改服务记录的话术单元信息
     */
    @PreAuthorize("@ss.hasPermi('service:serviceitem:edit')")
    @Log(title = "服务记录的话术单元信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServiceRecordItem serviceRecordItem)
    {
        return toAjax(serviceRecordItemService.updateServiceRecordItem(serviceRecordItem));
    }

    /**
     * 删除服务记录的话术单元信息
     */
    @PreAuthorize("@ss.hasPermi('service:serviceitem:remove')")
    @Log(title = "服务记录的话术单元信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(serviceRecordItemService.deleteServiceRecordItemByIds(ids));
    }
}
