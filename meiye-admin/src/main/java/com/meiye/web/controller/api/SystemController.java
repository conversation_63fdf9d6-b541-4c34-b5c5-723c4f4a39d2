package com.meiye.web.controller.api;

import com.meiye.api.domain.PaymentMethod;
import com.meiye.api.vo.RecordingDeviceVo;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.domain.entity.SysDictData;
import com.meiye.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/system")
public class SystemController extends BaseController {

        @Autowired
        private ISysDictTypeService dictTypeService;


    /**
     * 获取当前登录店面的录音设备列表
     */
    @GetMapping(value = "/cash-methods")
    public AjaxResult getRecordDeviceList() {
        List<SysDictData> data = dictTypeService.selectDictDataByType("payment_cash_method");
        List<PaymentMethod> list = data.stream()
                .map(dict -> {
                    PaymentMethod paymentMethod = new PaymentMethod();
                    paymentMethod.setCode(dict.getDictValue());
                    paymentMethod.setName(dict.getDictLabel());
                    return paymentMethod;
                })
                .collect(Collectors.toList());
        return success(list);
    }

}
