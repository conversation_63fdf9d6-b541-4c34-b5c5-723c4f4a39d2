package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.api.domain.RecordingDeviceEmployee;
import com.meiye.api.service.RecordingDeviceEmployeeService;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 录音设备分配Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/system/employee")
public class RecordingDeviceEmployeeController extends BaseController
{
    @Autowired
    private RecordingDeviceEmployeeService recordingDeviceEmployeeService;

    /**
     * 查询录音设备分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:employee:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecordingDeviceEmployee recordingDeviceEmployee)
    {
        startPage();
        List<RecordingDeviceEmployee> list = recordingDeviceEmployeeService.selectRecordingDeviceEmployeeList(recordingDeviceEmployee);
        return getDataTable(list);
    }

    /**
     * 导出录音设备分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:employee:export')")
    @Log(title = "录音设备分配", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecordingDeviceEmployee recordingDeviceEmployee)
    {
        List<RecordingDeviceEmployee> list = recordingDeviceEmployeeService.selectRecordingDeviceEmployeeList(recordingDeviceEmployee);
        ExcelUtil<RecordingDeviceEmployee> util = new ExcelUtil<RecordingDeviceEmployee>(RecordingDeviceEmployee.class);
        util.exportExcel(response, list, "录音设备分配数据");
    }

    /**
     * 获取录音设备分配详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:employee:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recordingDeviceEmployeeService.selectRecordingDeviceEmployeeById(id));
    }

    /**
     * 新增录音设备分配
     */
    @PreAuthorize("@ss.hasPermi('system:employee:add')")
    @Log(title = "录音设备分配", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecordingDeviceEmployee recordingDeviceEmployee)
    {
        return toAjax(recordingDeviceEmployeeService.insertRecordingDeviceEmployee(recordingDeviceEmployee));
    }

    /**
     * 修改录音设备分配
     */
    @PreAuthorize("@ss.hasPermi('system:employee:edit')")
    @Log(title = "录音设备分配", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecordingDeviceEmployee recordingDeviceEmployee)
    {
        return toAjax(recordingDeviceEmployeeService.updateRecordingDeviceEmployee(recordingDeviceEmployee));
    }

    /**
     * 删除录音设备分配
     */
    @PreAuthorize("@ss.hasPermi('system:employee:remove')")
    @Log(title = "录音设备分配", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recordingDeviceEmployeeService.deleteRecordingDeviceEmployeeByIds(ids));
    }
}
