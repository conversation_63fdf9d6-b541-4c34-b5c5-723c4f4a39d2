package com.meiye.web.controller.api;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageInfo;
import com.meiye.api.domain.StoreNotice;
import com.meiye.api.vo.StoreNoticeVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.api.domain.StoreNoticeItem;
import com.meiye.api.service.StoreNoticeItemService;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 店铺公告发布记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@RestController
@RequestMapping("/store/notice")
public class StoreNoticeItemController extends BaseController
{
    @Autowired
    private StoreNoticeItemService storeNoticeItemService;

    /**
     * 查询店铺公告发布记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:item:list')")
    @GetMapping("/item/list")
    public TableDataInfo list(StoreNoticeItem storeNoticeItem)
    {
        startPage();
        List<StoreNoticeItem> list = storeNoticeItemService.selectStoreNoticeItemList(storeNoticeItem);
        return getDataTable(list);
    }

    /**
     * 查询店铺公告列表
     */
    @GetMapping("/to-employee")
    public AjaxResult toEmployee(StoreNoticeVo vo) {
        // 启动分页
        startPage();
        // 查询数据
        List<StoreNoticeVo> list = storeNoticeItemService.selectStoreNoticeVoList(vo);
        // 获取分页信息
        PageInfo<StoreNoticeVo> pageInfo = new PageInfo<>(list);
        // 构建返回数据
        Map<String, Object> data = new HashMap<>();
        data.put("rows", list);
        data.put("page", pageInfo.getPageNum());
        data.put("total", pageInfo.getTotal());

        return AjaxResult.success(data);
    }

    /**
     * 导出店铺公告发布记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:item:export')")
    @Log(title = "店铺公告发布记录", businessType = BusinessType.EXPORT)
    @PostMapping("/item/export")
    public void export(HttpServletResponse response, StoreNoticeItem storeNoticeItem)
    {
        List<StoreNoticeItem> list = storeNoticeItemService.selectStoreNoticeItemList(storeNoticeItem);
        ExcelUtil<StoreNoticeItem> util = new ExcelUtil<StoreNoticeItem>(StoreNoticeItem.class);
        util.exportExcel(response, list, "店铺公告发布记录数据");
    }

    /**
     * 获取店铺公告发布记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:item:query')")
    @GetMapping(value = "/item/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeNoticeItemService.selectStoreNoticeItemById(id));
    }

    /**
     * 员工已读消息
     */
    @Log(title = "员工已读消息", businessType = BusinessType.UPDATE)
    @PutMapping("/read/{id}")
    public AjaxResult read(@PathVariable Long id)
    {
        return toAjax(storeNoticeItemService.read(id));
    }
}
