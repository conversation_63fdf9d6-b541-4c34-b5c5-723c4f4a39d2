package com.meiye.web.controller.api;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.UploadedImage;
import com.meiye.api.dto.ImageDto;
import com.meiye.api.service.UploadedImageService;
import com.meiye.common.config.RuoYiConfig;
import com.meiye.common.constant.Constants;
import com.meiye.common.utils.StringUtils;
import com.meiye.common.utils.file.FileUploadUtils;
import com.meiye.common.utils.file.FileUtils;
import com.meiye.framework.config.ServerConfig;
import lombok.extern.log4j.Log4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户图片信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */

@RestController
@RequestMapping("/service/image")
public class UploadedImageController extends BaseController
{
    @Autowired
    private ServerConfig serverConfig;
    @Autowired
    private UploadedImageService uploadedImageService;

    /**
     * 查询用户图片信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:image:list')")
    @GetMapping("/list")
    public TableDataInfo list(UploadedImage uploadedImage)
    {
        startPage();
        List<UploadedImage> list = uploadedImageService.selectUploadedImageList(uploadedImage);
        return getDataTable(list);
    }

    /**
     * 导出用户图片信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:image:export')")
    @Log(title = "用户图片信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UploadedImage uploadedImage)
    {
        List<UploadedImage> list = uploadedImageService.selectUploadedImageList(uploadedImage);
        ExcelUtil<UploadedImage> util = new ExcelUtil<UploadedImage>(UploadedImage.class);
        util.exportExcel(response, list, "用户图片信息数据");
    }

    /**
     * 获取用户图片信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:image:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(uploadedImageService.selectUploadedImageById(id));
    }

//    /**
//     * 新增用户图片信息
//     */
//    @PreAuthorize("@ss.hasPermi('system:image:add')")
//    @Log(title = "用户图片信息", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody UploadedImage uploadedImage)
//    {
//        return toAjax(uploadedImageService.insertUploadedImage(uploadedImage));
//    }

    /**
     * 修改用户图片信息
     */
    @PreAuthorize("@ss.hasPermi('system:image:edit')")
    @Log(title = "用户图片信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestParam(value = "id") Long id,
                           @RequestParam(value = "name", required = false) String name,
                           @RequestParam(value = "description", required = false) String description,
                           @RequestParam(value = "file", required = false) MultipartFile file)
    {
        try {
            // 获取数据库中的原始记录
            UploadedImage originalImage = uploadedImageService.selectUploadedImageById(id);

            // 检查原始记录是否存在
            if (originalImage == null) {
                return AjaxResult.error("图片信息不存在");
            }

            // 如果上传了新文件，则处理文件更新
            if (file != null && !file.isEmpty()) {
                // 删除旧文件
                if (originalImage.getFileName() != null) {
                    String oldFilePath = RuoYiConfig.getUploadPath() + originalImage.getFileName();
                    try {
                        FileUtils.deleteFile(oldFilePath);
                    } catch (Exception e) {
                        logger.warn("删除旧文件失败: {}", oldFilePath, e);
                    }
                }

                // 上传新文件
                String filePath = RuoYiConfig.getUploadPath();
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;

                // 更新文件相关信息
                originalImage.setFileUrl(url);
                originalImage.setFileName(fileName);
                originalImage.setFileSize((int) file.getSize());
                originalImage.setFileType(file.getContentType());

                // 获取图片宽度和高度
                if (file.getContentType() != null && file.getContentType().startsWith("image/")) {
                    try {
                        java.awt.image.BufferedImage image = javax.imageio.ImageIO.read(file.getInputStream());
                        if (image != null) {
                            originalImage.setImageWidth(image.getWidth());
                            originalImage.setImageHeight(image.getHeight());
                        }
                    } catch (Exception e) {
                        logger.warn("读取图片信息失败", e);
                        originalImage.setImageWidth(0);
                        originalImage.setImageHeight(0);
                    }
                } else {
                    originalImage.setImageWidth(0);
                    originalImage.setImageHeight(0);
                }
            }

            // 更新基本信息
            if (name != null) {
                originalImage.setName(name);
            }
            if (description != null) {
                originalImage.setDescription(description);
            }
            uploadedImageService.updateUploadedImage(originalImage);
            // 保存更新
            return AjaxResult.success(originalImage);

        } catch (Exception e) {
            logger.error("更新图片信息失败", e);
            return AjaxResult.error("更新图片信息失败: " + e.getMessage());
        }
    }


    @PostMapping("/upload")
    public AjaxResult uploadImage(ImageDto imageDto)
    {
        UploadedImage uploadedImage = new UploadedImage();
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            MultipartFile file = imageDto.getFile();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;

            uploadedImage.setEmployeeId(getUserId());
            uploadedImage.setName(imageDto.getName());
            uploadedImage.setDescription(imageDto.getDescription());
            uploadedImage.setFileUrl(url);
            uploadedImage.setFileName(fileName);
            uploadedImage.setFileSize((int) file.getSize());
            uploadedImage.setFileType(file.getContentType());
            uploadedImage.setCreatedAt(new Date());
            uploadedImage.setCreatedBy(getUsername());

            // 获取图片宽度和高度
            if (file.getContentType() != null && file.getContentType().startsWith("image/")) {
                try {
                    java.awt.image.BufferedImage image = javax.imageio.ImageIO.read(file.getInputStream());
                    if (image != null) {
                        uploadedImage.setImageWidth(image.getWidth());
                        uploadedImage.setImageHeight(image.getHeight());;
                    }
                } catch (Exception e) {
                    // 如果读取图片信息失败，设置默认值
                    uploadedImage.setImageWidth(0);
                    uploadedImage.setImageHeight(0);;
                }
            } else {
                uploadedImage.setImageWidth(0);
                uploadedImage.setImageHeight(0);;
            }
            uploadedImageService.save(uploadedImage);

            return AjaxResult.success(uploadedImage);

        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }


    /**
     * 删除用户图片信息
     */
    @PreAuthorize("@ss.hasPermi('system:image:remove')")
    @Log(title = "用户图片信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 先查询要删除的记录，获取文件路径
        for (Long id : ids) {
            UploadedImage image = uploadedImageService.selectUploadedImageById(id);
            if (image != null && image.getFileName() != null) {
                // 构建文件的完整路径

                String filePath = RuoYiConfig.getUploadPath1() + StringUtils.substringAfter(image.getFileName(), Constants.RESOURCE_PREFIX);
                try {
                    // 删除物理文件
                    FileUtils.deleteFile(filePath);
                } catch (Exception e) {
                    logger.warn("删除文件失败: {}", filePath, e);
                }
            }
        }
        return toAjax(uploadedImageService.deleteUploadedImageByIds(ids));
    }
}
