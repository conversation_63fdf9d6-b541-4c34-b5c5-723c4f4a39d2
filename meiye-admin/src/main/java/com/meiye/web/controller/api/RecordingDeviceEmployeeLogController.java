package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.api.domain.RecordingDeviceEmployeeLog;
import com.meiye.api.service.RecordingDeviceEmployeeLogService;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 录音设备分配记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("/api/log")
public class RecordingDeviceEmployeeLogController extends BaseController
{
    @Autowired
    private RecordingDeviceEmployeeLogService recordingDeviceEmployeeLogService;

    /**
     * 查询录音设备分配记录列表
     */
    @PreAuthorize("@ss.hasPermi('api:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(RecordingDeviceEmployeeLog recordingDeviceEmployeeLog)
    {
        startPage();
        List<RecordingDeviceEmployeeLog> list = recordingDeviceEmployeeLogService.selectRecordingDeviceEmployeeLogList(recordingDeviceEmployeeLog);
        return getDataTable(list);
    }

    /**
     * 导出录音设备分配记录列表
     */
    @PreAuthorize("@ss.hasPermi('api:log:export')")
    @Log(title = "录音设备分配记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RecordingDeviceEmployeeLog recordingDeviceEmployeeLog)
    {
        List<RecordingDeviceEmployeeLog> list = recordingDeviceEmployeeLogService.selectRecordingDeviceEmployeeLogList(recordingDeviceEmployeeLog);
        ExcelUtil<RecordingDeviceEmployeeLog> util = new ExcelUtil<RecordingDeviceEmployeeLog>(RecordingDeviceEmployeeLog.class);
        util.exportExcel(response, list, "录音设备分配记录数据");
    }

    /**
     * 获取录音设备分配记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('api:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(recordingDeviceEmployeeLogService.selectRecordingDeviceEmployeeLogById(id));
    }

    /**
     * 新增录音设备分配记录
     */
    @PreAuthorize("@ss.hasPermi('api:log:add')")
    @Log(title = "录音设备分配记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RecordingDeviceEmployeeLog recordingDeviceEmployeeLog)
    {
        return toAjax(recordingDeviceEmployeeLogService.insertRecordingDeviceEmployeeLog(recordingDeviceEmployeeLog));
    }

    /**
     * 修改录音设备分配记录
     */
    @PreAuthorize("@ss.hasPermi('api:log:edit')")
    @Log(title = "录音设备分配记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RecordingDeviceEmployeeLog recordingDeviceEmployeeLog)
    {
        return toAjax(recordingDeviceEmployeeLogService.updateRecordingDeviceEmployeeLog(recordingDeviceEmployeeLog));
    }

    /**
     * 删除录音设备分配记录
     */
    @PreAuthorize("@ss.hasPermi('api:log:remove')")
    @Log(title = "录音设备分配记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(recordingDeviceEmployeeLogService.deleteRecordingDeviceEmployeeLogByIds(ids));
    }
}
