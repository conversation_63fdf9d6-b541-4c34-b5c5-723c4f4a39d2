package com.meiye.web.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.meiye.api.domain.Member;
import com.meiye.api.domain.MemberLevel;
import com.meiye.api.query.MemberQuery;
import com.meiye.api.service.MemberService;
import com.meiye.api.vo.MemberVO;
import com.meiye.common.annotation.Anonymous;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 会员Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/store/member")
public class MemberController extends BaseController {
    @Autowired
    private MemberService memberService;

    @PreAuthorize("@ss.hasPermi('member:member:list')")
    @Anonymous
    @PostMapping("/queryMemberList")
    public AjaxResult queryMemberList(MemberQuery query) {
        List<MemberVO> list = memberService.queryMemberList(query);
        return success(list);
    }

    /**
     * 查询会员列表（分页）
     */
    @PreAuthorize("@ss.hasPermi('storeManage:member:list')")
    @GetMapping("/list")
    public TableDataInfo list(Member member) {
        startPage();
        List<Member> list = memberService.selectMemberList(member);
        return getDataTable(list);
    }

    /**
     * 查询所有会员列表（不分页，用于下拉框）
     */
    @PreAuthorize("@ss.hasPermi('storeManage:member:list')")
    @GetMapping("/listAll")
    public AjaxResult listAll(Member member) {
        List<Member> list = memberService.selectMemberList(member);
        return AjaxResult.success(list);
    }

    /**
     * 导出会员列表
     */
    @PreAuthorize("@ss.hasPermi('store:member:export')")
    @Log(title = "会员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Member member) {
        List<Member> list = memberService.selectMemberList(member);
        ExcelUtil<Member> util = new ExcelUtil<Member>(Member.class);
        util.exportExcel(response, list, "会员数据");
    }

    /**
     * 获取会员详细信息
     */
    @PreAuthorize("@ss.hasPermi('storeManage:member:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(memberService.selectMemberById(id));
    }

    /**
     * 新增会员
     */
    @PreAuthorize("@ss.hasPermi('storeManage:member:add')")
    @Log(title = "会员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Member member) {
        if (member.getStoreId() == null) {
            member.setStoreId(getStoreId());
        }
        if (member.getRootStoreId() == null) {
            if (getLoginUser().getUser().getStore().getParentId()!=null){
                member.setRootStoreId(getLoginUser().getUser().getStore().getParentId());
            }else {
                member.setRootStoreId(member.getStoreId());
            }

        }
        if (member.getSn() == null|| member.getSn().isEmpty()){
            Integer num = memberService.getYearMemberNum();
            // 生成会员编号：M+年月+4位流水号
            String yearMonth = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMM"));
            String serialNumber = String.format("%04d", num + 1);
            String sn = "M" + yearMonth + serialNumber;
            member.setSn(sn);
        }

        member.setCreatedByEmployeeId(getUserId());
        if (memberService.insertMember(member)>0){
            return success(member);
        }else {
            return error("添加失败");
        }

    }

    /**
     * 修改会员
     */
    @PreAuthorize("@ss.hasPermi('storeManage:member:edit')")
    @Log(title = "会员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Member member) {
        try {
            // 编辑时也自动填充当前登录用户的店铺ID作为主店面ID
            Long storeId = getStoreId();
            if (storeId != null) {
                member.setRootStoreId(storeId);
            }

            return toAjax(memberService.updateMember(member));
        } catch (Exception e) {
            e.printStackTrace();
            return error("修改失败: " + e.getMessage());
        }
    }

    /**
     * 删除会员
     */
    @PreAuthorize("@ss.hasPermi('storeManage:member:remove')")
    @Log(title = "会员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(memberService.deleteMemberByIds(ids));
    }


    /**
     * 获取会员等级选项列表
     */
    @PreAuthorize("@ss.hasPermi('store:member:list')")
    @GetMapping("/memberLevels")
    public AjaxResult getMemberLevels() {
        List<MemberLevel> list = memberService.selectMemberLevelOptions();
        return success(list);
    }

    /**
     * 根据店面名称获取店面ID
     */
    @GetMapping("/getStoreIdByName")
    public AjaxResult getStoreIdByName(@RequestParam String storeName) {
        Long storeId = memberService.getStoreIdByName(storeName);
        return success(storeId);
    }

    /**
     * 获取当前登录用户的店铺ID
     */
    @PreAuthorize("@ss.hasPermi('storeManage:member:list')")
    @GetMapping("/getStoreRootId")
    public AjaxResult getStoreRootId() {
        try {
            Long storeId = getStoreId();
            System.out.println("获取到的店铺ID: " + storeId);
            if (storeId == null) {
                return error("当前用户未绑定店铺");
            }
            return success(storeId);
        } catch (Exception e) {
            e.printStackTrace();
            return error("获取店铺ID失败: " + e.getMessage());
        }
    }
}
