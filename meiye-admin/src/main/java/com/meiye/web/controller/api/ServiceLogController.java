package com.meiye.service.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.ServiceLog;
import com.meiye.api.service.ServiceLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 服务实施Controller
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@RestController
@RequestMapping("/service/servicelog")
public class ServiceLogController extends BaseController
{
    @Autowired
    private ServiceLogService serviceLogService;

    /**
     * 查询服务实施列表
     */
    @PreAuthorize("@ss.hasPermi('service:servicelog:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServiceLog serviceLog)
    {
        startPage();
        List<ServiceLog> list = serviceLogService.selectServiceLogList(serviceLog);
        return getDataTable(list);
    }

    /**
     * 导出服务实施列表
     */
    @PreAuthorize("@ss.hasPermi('service:servicelog:export')")
    @Log(title = "服务实施", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceLog serviceLog)
    {
        List<ServiceLog> list = serviceLogService.selectServiceLogList(serviceLog);
        ExcelUtil<ServiceLog> util = new ExcelUtil<ServiceLog>(ServiceLog.class);
        util.exportExcel(response, list, "服务实施数据");
    }

    /**
     * 获取服务实施详细信息
     */
    @PreAuthorize("@ss.hasPermi('service:servicelog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(serviceLogService.selectServiceLogById(id));
    }

    /**
     * 新增服务实施
     */
    @PreAuthorize("@ss.hasPermi('service:servicelog:add')")
    @Log(title = "服务实施", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServiceLog serviceLog)
    {
        return toAjax(serviceLogService.insertServiceLog(serviceLog));
    }

    /**
     * 修改服务实施
     */
    @PreAuthorize("@ss.hasPermi('service:servicelog:edit')")
    @Log(title = "服务实施", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServiceLog serviceLog)
    {
        return toAjax(serviceLogService.updateServiceLog(serviceLog));
    }

    /**
     * 删除服务实施
     */
    @PreAuthorize("@ss.hasPermi('service:servicelog:remove')")
    @Log(title = "服务实施", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(serviceLogService.deleteServiceLogByIds(ids));
    }
}
