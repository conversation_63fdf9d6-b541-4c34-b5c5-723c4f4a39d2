package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;
import com.meiye.api.domain.StoreProductCommission;
import com.meiye.api.service.StoreProductCommissionService;
import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 店面商品提成设置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@RestController
@RequestMapping("/StoreProductCommission/commission")
public class StoreProductCommissionController extends BaseController
{
    @Autowired
    private StoreProductCommissionService storeProductCommissionService;

    /**
     * 查询店面商品提成设置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(StoreProductCommission storeProductCommission)
    {
        startPage();
        List<StoreProductCommission> list = storeProductCommissionService.selectStoreProductCommissionList(storeProductCommission);
        return getDataTable(list);
    }
    
    /**
     * 根据店面销售物ID查询提成设置列表
     */
    @GetMapping("/listByProductId")
    public AjaxResult listByProductId(StoreProductCommission storeProductCommission)
    {
        List<StoreProductCommission> list = storeProductCommissionService.selectByStoreProductId(storeProductCommission.getStoreProductId());
        return  success(list);
    }
    
    /**
     * 导出店面商品提成设置列表
     */
    @Log(title = "店面商品提成设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StoreProductCommission storeProductCommission)
    {
        List<StoreProductCommission> list = storeProductCommissionService.selectStoreProductCommissionList(storeProductCommission);
        ExcelUtil<StoreProductCommission> util = new ExcelUtil<StoreProductCommission>(StoreProductCommission.class);
        util.exportExcel(response, list, "店面商品提成设置数据");
    }

    /**
     * 获取店面商品提成设置详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeProductCommissionService.selectStoreProductCommissionById(id));
    }

    /**
     * 新增店面商品提成设置
     */
    @Log(title = "店面商品提成设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StoreProductCommission storeProductCommission)
    {
        return toAjax(storeProductCommissionService.insertStoreProductCommission(storeProductCommission));
    }

    /**
     * 修改店面商品提成设置
     */
    @Log(title = "店面商品提成设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StoreProductCommission storeProductCommission)
    {
        return toAjax(storeProductCommissionService.updateStoreProductCommission(storeProductCommission));
    }

    /**
     * 删除店面商品提成设置
     */
    @Log(title = "店面商品提成设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(storeProductCommissionService.deleteStoreProductCommissionByIds(ids));
    }
}