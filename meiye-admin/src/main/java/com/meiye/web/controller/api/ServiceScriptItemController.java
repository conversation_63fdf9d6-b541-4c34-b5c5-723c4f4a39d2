package com.meiye.web.controller.api;

import com.meiye.api.domain.ServiceScriptItem;
import com.meiye.api.service.ServiceScriptItemService;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 标准话术单元Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/service/item")
public class ServiceScriptItemController extends BaseController
{
    @Autowired
    private ServiceScriptItemService serviceScriptItemService;

    /**
     * 查询标准话术单元列表
     */
    @PreAuthorize("@ss.hasPermi('service:item:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServiceScriptItem serviceScriptItem)
    {
        startPage();
        List<ServiceScriptItem> list = serviceScriptItemService.selectServiceScriptItemList(serviceScriptItem);
        return getDataTable(list);
    }

    /**
     * 导出标准话术单元列表
     */
    @PreAuthorize("@ss.hasPermi('api:item:export')")
    @Log(title = "标准话术单元", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceScriptItem serviceScriptItem)
    {
        List<ServiceScriptItem> list = serviceScriptItemService.selectServiceScriptItemList(serviceScriptItem);
        ExcelUtil<ServiceScriptItem> util = new ExcelUtil<ServiceScriptItem>(ServiceScriptItem.class);
        util.exportExcel(response, list, "标准话术单元数据");
    }

    /**
     * 获取标准话术单元详细信息
     */
    @PreAuthorize("@ss.hasPermi('service:item:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(serviceScriptItemService.selectServiceScriptItemById(id));
    }

    /**
     * 新增标准话术单元
     */
    @PreAuthorize("@ss.hasPermi('service:item:add')")
    @Log(title = "标准话术单元", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServiceScriptItem serviceScriptItem)
    {
        
        serviceScriptItem.setCreatedBy(getUserId());
        return toAjax(serviceScriptItemService.insertServiceScriptItem(serviceScriptItem));
    }

    /**
     * 修改标准话术单元
     */
    @PreAuthorize("@ss.hasPermi('service:item:edit')")
    @Log(title = "标准话术单元", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServiceScriptItem serviceScriptItem)
    {
        return toAjax(serviceScriptItemService.updateServiceScriptItem(serviceScriptItem));
    }

    /**
     * 删除标准话术单元
     */
    @PreAuthorize("@ss.hasPermi('service:item:remove')")
    @Log(title = "标准话术单元", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(serviceScriptItemService.deleteServiceScriptItemByIds(ids));
    }
    
    /**
     * 获取主店面ID
     */
    @PreAuthorize("@ss.hasPermi('service:item:list')")
    @GetMapping("/getStoreRootId")
    public AjaxResult getStoreRootId() {
        Long rootStoreId = serviceScriptItemService.getStoreRootId();
        return success(rootStoreId);
    }
    
    /**
     * 根据店面名称获取店面ID
     */
    @GetMapping("/getStoreIdByName")
    public AjaxResult getStoreIdByName(@RequestParam String storeName) {
        Long storeId = serviceScriptItemService.getStoreIdByName(storeName);
        return success(storeId);
    }
}