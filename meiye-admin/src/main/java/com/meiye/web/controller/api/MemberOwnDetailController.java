package com.meiye.web.controller.api;

import com.meiye.api.domain.Member;
import com.meiye.api.service.MemberService;
import com.meiye.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会员API接口
 */
@Api(tags = "会员管理接口")
@RestController
@RequestMapping("/service/member")
public class MemberOwnDetailController {

    @Autowired
    private MemberService memberService;

    /**
     * 获取会员详细信息
     */
    @ApiOperation("获取会员详细信息")
    @GetMapping("/owner-member-info")
    public R<Member> getOwnerMemberInfo(
            @ApiParam(value = "会员ID", required = true) @RequestParam(required = false) Long memberId) {
        
        // 如果未提供会员ID，可以从当前登录用户获取
        if (memberId == null) {
            // 从当前登录用户获取会员ID的逻辑
            // 这里可以根据实际情况从token或session中获取
            return R.fail("会员ID不能为空");
        }
        
        // 调用服务获取会员详细信息
        Member member = memberService.getMemberDetailInfo(memberId);
        
        if (member == null) {
            return R.fail("未找到会员信息");
        }
        
        return R.ok(member);
    }
}