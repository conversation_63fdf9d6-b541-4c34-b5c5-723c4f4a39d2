package com.meiye.web.controller.api;

import com.meiye.api.dto.PendingOrderDto;
import com.meiye.api.dto.SaleOrderDTO;
import com.meiye.api.service.SaleOrderService;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/saleOrder")
public class SaleOrderController extends BaseController {

    @Autowired
    SaleOrderService saleOrderService;

//    @PreAuthorize("@ss.hasPermi('saleOrder:saleOrder:add')")
//    @PostMapping("/addSaleOrder")
//    public AjaxResult queryStoreProductList(SaleOrderDTO dto) {
//        saleOrderService.addSaleOrder(dto);
//        return success(dto);
//    }

    @PreAuthorize("@ss.hasPermi('saleOrder:saleOrder:add')")
    @PostMapping("/addSaleOrder")
    public AjaxResult addSaleOrder(PendingOrderDto dto) {
        dto.setUserId(getUserId());
        if (getStoreId()==null){
            throw new RuntimeException("用户未选择门店");
        }
        dto.setStoreId(getStoreId());
        return saleOrderService.addSaleOrderNew(dto);
    }
}
