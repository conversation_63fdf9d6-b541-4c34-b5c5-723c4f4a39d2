package com.meiye.web.controller.api;

import com.meiye.api.domain.MemberProduct;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.core.page.TableDataInfo;
import com.meiye.common.enums.BusinessType;
import com.meiye.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 适于该会员的推荐项目Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/member/product")
public class MemberProductController extends BaseController
{
    @Autowired
    private com.meiye.api.service.MemberProductService memberProductService;

    /**
     * 查询适于该会员的推荐项目列表
     */
    @PreAuthorize("@ss.hasPermi('member:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(MemberProduct memberProduct)
    {
        startPage();
        List<MemberProduct> list = memberProductService.selectMemberProductList(memberProduct);
        return getDataTable(list);
    }

    /**
     * 导出适于该会员的推荐项目列表
     */
    @PreAuthorize("@ss.hasPermi('member:product:export')")
    @Log(title = "适于该会员的推荐项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MemberProduct memberProduct)
    {
        List<MemberProduct> list = memberProductService.selectMemberProductList(memberProduct);
        ExcelUtil<MemberProduct> util = new ExcelUtil<MemberProduct>(MemberProduct.class);
        util.exportExcel(response, list, "适于该会员的推荐项目数据");
    }

    /**
     * 获取适于该会员的推荐项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('member:product:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(memberProductService.selectMemberProductById(id));
    }

    /**
     * 新增适于该会员的推荐项目
     */
    @PreAuthorize("@ss.hasPermi('member:product:add')")
    @Log(title = "适于该会员的推荐项目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MemberProduct memberProduct)
    {
        return toAjax(memberProductService.insertMemberProduct(memberProduct));
    }

    /**
     * 修改适于该会员的推荐项目
     */
    @PreAuthorize("@ss.hasPermi('member:product:edit')")
    @Log(title = "适于该会员的推荐项目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MemberProduct memberProduct)
    {
        return toAjax(memberProductService.updateMemberProduct(memberProduct));
    }

    /**
     * 删除适于该会员的推荐项目
     */
    @PreAuthorize("@ss.hasPermi('member:product:remove')")
    @Log(title = "适于该会员的推荐项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(memberProductService.deleteMemberProductByIds(ids));
    }
}