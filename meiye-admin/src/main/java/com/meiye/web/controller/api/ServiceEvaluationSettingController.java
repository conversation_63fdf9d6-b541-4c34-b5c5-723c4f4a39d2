package com.meiye.web.controller.api;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.meiye.api.domain.ServiceEvaluationSetting;
import com.meiye.api.service.ServiceEvaluationSettingService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.meiye.common.annotation.Log;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import com.meiye.common.enums.BusinessType;

import com.meiye.common.utils.poi.ExcelUtil;
import com.meiye.common.core.page.TableDataInfo;

/**
 * 服务评价设置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@RestController
@RequestMapping("/evaluation/setting")
public class ServiceEvaluationSettingController extends BaseController
{
    @Autowired
    private ServiceEvaluationSettingService serviceEvaluationSettingService;

    /**
     * 查询服务评价设置列表
     */
    @PreAuthorize("@ss.hasPermi('evaluation:setting:list')")
    @GetMapping("/list")
    public TableDataInfo list(ServiceEvaluationSetting serviceEvaluationSetting)
    {
        startPage();
        List<ServiceEvaluationSetting> list = serviceEvaluationSettingService.selectServiceEvaluationSettingList(serviceEvaluationSetting);
        return getDataTable(list);
    }

    /**
     * 导出服务评价设置列表
     */
    @PreAuthorize("@ss.hasPermi('evaluation:setting:export')")
    @Log(title = "服务评价设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ServiceEvaluationSetting serviceEvaluationSetting)
    {
        List<ServiceEvaluationSetting> list = serviceEvaluationSettingService.selectServiceEvaluationSettingList(serviceEvaluationSetting);
        ExcelUtil<ServiceEvaluationSetting> util = new ExcelUtil<ServiceEvaluationSetting>(ServiceEvaluationSetting.class);
        util.exportExcel(response, list, "服务评价设置数据");
    }

    /**
     * 获取服务评价设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('evaluation:setting:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(serviceEvaluationSettingService.selectServiceEvaluationSettingById(id));
    }

    /**
     * 新增服务评价设置
     */
    @PreAuthorize("@ss.hasPermi('evaluation:setting:add')")
    @Log(title = "服务评价设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ServiceEvaluationSetting serviceEvaluationSetting)
    {
        // 自动填充创建人ID
        if (serviceEvaluationSetting.getCreatedBy() == null) {
            serviceEvaluationSetting.setCreatedBy(getUserId());
        }
        return toAjax(serviceEvaluationSettingService.insertServiceEvaluationSetting(serviceEvaluationSetting));
    }

    /**
     * 修改服务评价设置
     */
    @PreAuthorize("@ss.hasPermi('evaluation:setting:edit')")
    @Log(title = "服务评价设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ServiceEvaluationSetting serviceEvaluationSetting)
    {
        // 确保不更新创建人ID和创建时间
        ServiceEvaluationSetting existingSetting = serviceEvaluationSettingService.selectServiceEvaluationSettingById(serviceEvaluationSetting.getId());
        if (existingSetting != null) {
            serviceEvaluationSetting.setCreatedBy(existingSetting.getCreatedBy());
            serviceEvaluationSetting.setCreatedAt(existingSetting.getCreatedAt());
        }
        return toAjax(serviceEvaluationSettingService.updateServiceEvaluationSetting(serviceEvaluationSetting));
    }

    /**
     * 删除服务评价设置
     */
    @PreAuthorize("@ss.hasPermi('evaluation:setting:remove')")
    @Log(title = "服务评价设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(serviceEvaluationSettingService.deleteServiceEvaluationSettingByIds(ids));
    }
}
